{"ast": null, "code": "/**\n * Converts seconds to milliseconds\n *\n * @param seconds - Time in seconds.\n * @return milliseconds - Converted time in milliseconds.\n */\nconst secondsToMilliseconds = seconds => seconds * 1000;\nconst millisecondsToSeconds = milliseconds => milliseconds / 1000;\nexport { millisecondsToSeconds, secondsToMilliseconds };", "map": {"version": 3, "names": ["secondsToMilliseconds", "seconds", "millisecondsToSeconds", "milliseconds"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/test 3/medical-assistant-react/node_modules/framer-motion/dist/es/utils/time-conversion.mjs"], "sourcesContent": ["/**\n * Converts seconds to milliseconds\n *\n * @param seconds - Time in seconds.\n * @return milliseconds - Converted time in milliseconds.\n */\nconst secondsToMilliseconds = (seconds) => seconds * 1000;\nconst millisecondsToSeconds = (milliseconds) => milliseconds / 1000;\n\nexport { millisecondsToSeconds, secondsToMilliseconds };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,qBAAqB,GAAIC,OAAO,IAAKA,OAAO,GAAG,IAAI;AACzD,MAAMC,qBAAqB,GAAIC,YAAY,IAAKA,YAAY,GAAG,IAAI;AAEnE,SAASD,qBAAqB,EAAEF,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}