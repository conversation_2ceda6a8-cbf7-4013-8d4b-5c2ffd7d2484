{"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"a": {"title": "Anchor Point", "description": "Transform Anchor Point", "oneOf": [{"$ref": "#/properties/multiDimensional"}, {"$ref": "#/properties/multiDimensionalKeyframed"}], "default": {"a": 0, "k": [0, 0, 0]}, "type": "object"}, "p": {"title": "Position", "description": "Transform Position", "oneOf": [{"$ref": "#/properties/multiDimensional"}, {"$ref": "#/properties/multiDimensionalKeyframed"}], "default": {"a": 0, "k": [0, 0, 0]}, "type": "object"}, "s": {"title": "Scale", "description": "Transform Scale", "oneOf": [{"$ref": "#/properties/multiDimensional"}, {"$ref": "#/properties/multiDimensionalKeyframed"}], "default": {"a": 0, "k": [100, 100, 100]}, "type": "object"}, "r": {"title": "Rotation", "description": "Transform Rotation", "oneOf": [{"$ref": "#/properties/value"}, {"$ref": "#/properties/valueKeyframed"}], "default": {"a": 0, "k": 0}, "type": "object"}, "o": {"title": "Opacity", "description": "Transform Opacity", "oneOf": [{"$ref": "#/properties/value"}, {"$ref": "#/properties/valueKeyframed"}], "default": {"a": 0, "k": 100}, "type": "object"}, "px": {"title": "Position X", "description": "Transform Position X", "oneOf": [{"$ref": "#/properties/value"}, {"$ref": "#/properties/valueKeyframed"}], "default": {"a": 0, "k": 0}, "type": "object"}, "py": {"title": "Position Y", "description": "Transform Position Y", "oneOf": [{"$ref": "#/properties/value"}, {"$ref": "#/properties/valueKeyframed"}], "default": {"a": 0, "k": 0}, "type": "object"}, "pz": {"title": "Position Z", "description": "Transform Position Z", "oneOf": [{"$ref": "#/properties/value"}, {"$ref": "#/properties/valueKeyframed"}], "default": {"a": 0, "k": 0}, "type": "object"}, "sk": {"title": "Skew", "description": "Transform Skew", "oneOf": [{"$ref": "#/properties/value"}, {"$ref": "#/properties/valueKeyframed"}], "default": {"a": 0, "k": 0}, "type": "object"}, "sa": {"title": "Skew Axis", "description": "Transform Skew Axis", "oneOf": [{"$ref": "#/properties/value"}, {"$ref": "#/properties/valueKeyframed"}], "default": {"a": 0, "k": 0}, "type": "object"}}}