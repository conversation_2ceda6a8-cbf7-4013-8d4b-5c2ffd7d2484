{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nconst ModalContext = /*#__PURE__*/React.createContext({\n  onHide() {}\n});\nexport default ModalContext;", "map": {"version": 3, "names": ["React", "ModalContext", "createContext", "onHide"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/test 3/medical-assistant-react/node_modules/react-bootstrap/esm/ModalContext.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nconst ModalContext = /*#__PURE__*/React.createContext({\n  onHide() {}\n});\nexport default ModalContext;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,MAAMC,YAAY,GAAG,aAAaD,KAAK,CAACE,aAAa,CAAC;EACpDC,MAAMA,CAAA,EAAG,CAAC;AACZ,CAAC,CAAC;AACF,eAAeF,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}