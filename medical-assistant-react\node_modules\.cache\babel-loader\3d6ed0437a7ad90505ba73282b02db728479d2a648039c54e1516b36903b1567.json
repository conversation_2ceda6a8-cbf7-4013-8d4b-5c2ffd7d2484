{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive - MOE Student S4M\\\\Desktop\\\\test 3\\\\medical-assistant-react\\\\src\\\\pages\\\\Home\\\\components\\\\CTASection.tsx\";\nimport React from 'react';\nimport { Container, Row, <PERSON>, <PERSON><PERSON> } from 'react-bootstrap';\nimport { Link } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CTASection = () => {\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    className: \"bg-primary py-5 py-md-6\",\n    children: /*#__PURE__*/_jsxDEV(Container, {\n      children: /*#__PURE__*/_jsxDEV(Row, {\n        className: \"justify-content-center text-center\",\n        children: /*#__PURE__*/_jsxDEV(Col, {\n          lg: 8,\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"fw-bold text-white mb-4\",\n            children: \"\\u0647\\u0644 \\u0623\\u0646\\u062A \\u0645\\u0633\\u062A\\u0639\\u062F \\u0644\\u062A\\u062C\\u0631\\u0628\\u0629 \\u0645\\u0633\\u0627\\u0639\\u062F\\u0646\\u0627 \\u0627\\u0644\\u0637\\u0628\\u064A \\u0627\\u0644\\u0630\\u0643\\u064A\\u061F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 11,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"lead text-white-50 mb-5\",\n            children: \"\\u0627\\u0628\\u062F\\u0623 \\u062A\\u0634\\u062E\\u064A\\u0635\\u0643 \\u0627\\u0644\\u0623\\u0648\\u0644 - \\u0625\\u0646\\u0647 \\u0645\\u062C\\u0627\\u0646\\u064A \\u0648\\u064A\\u0633\\u062A\\u063A\\u0631\\u0642 \\u0623\\u0642\\u0644 \\u0645\\u0646 5 \\u062F\\u0642\\u0627\\u0626\\u0642\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 14,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/diagnosis\",\n            className: \"text-decoration-none\",\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"light\",\n              size: \"lg\",\n              className: \"px-5 py-3 fw-semibold\",\n              style: {\n                borderRadius: '50px'\n              },\n              children: \"\\u0627\\u0628\\u062F\\u0623 \\u062A\\u0634\\u062E\\u064A\\u0635\\u0643\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 18,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 17,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 10,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 9,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 7,\n    columnNumber: 5\n  }, this);\n};\n_c = CTASection;\nexport default CTASection;\nvar _c;\n$RefreshReg$(_c, \"CTASection\");", "map": {"version": 3, "names": ["React", "Container", "Row", "Col", "<PERSON><PERSON>", "Link", "jsxDEV", "_jsxDEV", "CTASection", "className", "children", "lg", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "variant", "size", "style", "borderRadius", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/test 3/medical-assistant-react/src/pages/Home/components/CTASection.tsx"], "sourcesContent": ["import React from 'react';\nimport { Container, <PERSON>, <PERSON>, But<PERSON> } from 'react-bootstrap';\nimport { Link } from 'react-router-dom';\n\nconst CTASection: React.FC = () => {\n  return (\n    <section className=\"bg-primary py-5 py-md-6\">\n      <Container>\n        <Row className=\"justify-content-center text-center\">\n          <Col lg={8}>\n            <h2 className=\"fw-bold text-white mb-4\">\n              هل أنت مستعد لتجربة مساعدنا الطبي الذكي؟\n            </h2>\n            <p className=\"lead text-white-50 mb-5\">\n              ابدأ تشخيصك الأول - إنه مجاني ويستغرق أقل من 5 دقائق\n            </p>\n            <Link to=\"/diagnosis\" className=\"text-decoration-none\">\n              <Button\n                variant=\"light\"\n                size=\"lg\"\n                className=\"px-5 py-3 fw-semibold\"\n                style={{ borderRadius: '50px' }}\n              >\n                ابدأ تشخيصك\n              </Button>\n            </Link>\n          </Col>\n        </Row>\n      </Container>\n    </section>\n  );\n};\n\nexport default CTASection;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,SAAS,EAAEC,GAAG,EAAEC,GAAG,EAAEC,MAAM,QAAQ,iBAAiB;AAC7D,SAASC,IAAI,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,UAAoB,GAAGA,CAAA,KAAM;EACjC,oBACED,OAAA;IAASE,SAAS,EAAC,yBAAyB;IAAAC,QAAA,eAC1CH,OAAA,CAACN,SAAS;MAAAS,QAAA,eACRH,OAAA,CAACL,GAAG;QAACO,SAAS,EAAC,oCAAoC;QAAAC,QAAA,eACjDH,OAAA,CAACJ,GAAG;UAACQ,EAAE,EAAE,CAAE;UAAAD,QAAA,gBACTH,OAAA;YAAIE,SAAS,EAAC,yBAAyB;YAAAC,QAAA,EAAC;UAExC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLR,OAAA;YAAGE,SAAS,EAAC,yBAAyB;YAAAC,QAAA,EAAC;UAEvC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJR,OAAA,CAACF,IAAI;YAACW,EAAE,EAAC,YAAY;YAACP,SAAS,EAAC,sBAAsB;YAAAC,QAAA,eACpDH,OAAA,CAACH,MAAM;cACLa,OAAO,EAAC,OAAO;cACfC,IAAI,EAAC,IAAI;cACTT,SAAS,EAAC,uBAAuB;cACjCU,KAAK,EAAE;gBAAEC,YAAY,EAAE;cAAO,CAAE;cAAAV,QAAA,EACjC;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAACM,EAAA,GA3BIb,UAAoB;AA6B1B,eAAeA,UAAU;AAAC,IAAAa,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}