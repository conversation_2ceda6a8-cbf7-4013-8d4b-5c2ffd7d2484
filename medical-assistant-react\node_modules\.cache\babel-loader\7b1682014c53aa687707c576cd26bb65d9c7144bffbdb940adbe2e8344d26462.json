{"ast": null, "code": "\"use client\";\n\nimport _objectSpread from \"C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/test 3/medical-assistant-react/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport _objectWithoutProperties from \"C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/test 3/medical-assistant-react/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\";\nconst _excluded = [\"closeLabel\", \"closeVariant\", \"closeButton\", \"onHide\", \"children\"];\nimport * as React from 'react';\nimport { useContext } from 'react';\nimport useEventCallback from '@restart/hooks/useEventCallback';\nimport CloseButton from './CloseButton';\nimport ModalContext from './ModalContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst AbstractModalHeader = /*#__PURE__*/React.forwardRef((_ref, ref) => {\n  let {\n      closeLabel = 'Close',\n      closeVariant,\n      closeButton = false,\n      onHide,\n      children\n    } = _ref,\n    props = _objectWithoutProperties(_ref, _excluded);\n  const context = useContext(ModalContext);\n  const handleClick = useEventCallback(() => {\n    context == null || context.onHide();\n    onHide == null || onHide();\n  });\n  return /*#__PURE__*/_jsxs(\"div\", _objectSpread(_objectSpread({\n    ref: ref\n  }, props), {}, {\n    children: [children, closeButton && /*#__PURE__*/_jsx(CloseButton, {\n      \"aria-label\": closeLabel,\n      variant: closeVariant,\n      onClick: handleClick\n    })]\n  }));\n});\nAbstractModalHeader.displayName = 'AbstractModalHeader';\nexport default AbstractModalHeader;", "map": {"version": 3, "names": ["_objectSpread", "_objectWithoutProperties", "_excluded", "React", "useContext", "useEventCallback", "CloseButton", "ModalContext", "jsx", "_jsx", "jsxs", "_jsxs", "AbstractModalHeader", "forwardRef", "_ref", "ref", "<PERSON><PERSON><PERSON><PERSON>", "closeVariant", "closeButton", "onHide", "children", "props", "context", "handleClick", "variant", "onClick", "displayName"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/test 3/medical-assistant-react/node_modules/react-bootstrap/esm/AbstractModalHeader.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nimport { useContext } from 'react';\nimport useEventCallback from '@restart/hooks/useEventCallback';\nimport CloseButton from './CloseButton';\nimport ModalContext from './ModalContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst AbstractModalHeader = /*#__PURE__*/React.forwardRef(({\n  closeLabel = 'Close',\n  closeVariant,\n  closeButton = false,\n  onHide,\n  children,\n  ...props\n}, ref) => {\n  const context = useContext(ModalContext);\n  const handleClick = useEventCallback(() => {\n    context == null || context.onHide();\n    onHide == null || onHide();\n  });\n  return /*#__PURE__*/_jsxs(\"div\", {\n    ref: ref,\n    ...props,\n    children: [children, closeButton && /*#__PURE__*/_jsx(CloseButton, {\n      \"aria-label\": closeLabel,\n      variant: closeVariant,\n      onClick: handleClick\n    })]\n  });\n});\nAbstractModalHeader.displayName = 'AbstractModalHeader';\nexport default AbstractModalHeader;"], "mappings": "AAAA,YAAY;;AAAC,OAAAA,aAAA;AAAA,OAAAC,wBAAA;AAAA,MAAAC,SAAA;AAEb,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,QAAQ,OAAO;AAClC,OAAOC,gBAAgB,MAAM,iCAAiC;AAC9D,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,YAAY,MAAM,gBAAgB;AACzC,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AACjD,MAAMC,mBAAmB,GAAG,aAAaT,KAAK,CAACU,UAAU,CAAC,CAAAC,IAAA,EAOvDC,GAAG,KAAK;EAAA,IAPgD;MACzDC,UAAU,GAAG,OAAO;MACpBC,YAAY;MACZC,WAAW,GAAG,KAAK;MACnBC,MAAM;MACNC;IAEF,CAAC,GAAAN,IAAA;IADIO,KAAK,GAAApB,wBAAA,CAAAa,IAAA,EAAAZ,SAAA;EAER,MAAMoB,OAAO,GAAGlB,UAAU,CAACG,YAAY,CAAC;EACxC,MAAMgB,WAAW,GAAGlB,gBAAgB,CAAC,MAAM;IACzCiB,OAAO,IAAI,IAAI,IAAIA,OAAO,CAACH,MAAM,CAAC,CAAC;IACnCA,MAAM,IAAI,IAAI,IAAIA,MAAM,CAAC,CAAC;EAC5B,CAAC,CAAC;EACF,OAAO,aAAaR,KAAK,CAAC,KAAK,EAAAX,aAAA,CAAAA,aAAA;IAC7Be,GAAG,EAAEA;EAAG,GACLM,KAAK;IACRD,QAAQ,EAAE,CAACA,QAAQ,EAAEF,WAAW,IAAI,aAAaT,IAAI,CAACH,WAAW,EAAE;MACjE,YAAY,EAAEU,UAAU;MACxBQ,OAAO,EAAEP,YAAY;MACrBQ,OAAO,EAAEF;IACX,CAAC,CAAC;EAAC,EACJ,CAAC;AACJ,CAAC,CAAC;AACFX,mBAAmB,CAACc,WAAW,GAAG,qBAAqB;AACvD,eAAed,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}