{"ast": null, "code": "// can-promise has a crash in some versions of react native that dont have\n// standard global objects\n// https://github.com/soldair/node-qrcode/issues/157\n\nmodule.exports = function () {\n  return typeof Promise === 'function' && Promise.prototype && Promise.prototype.then;\n};", "map": {"version": 3, "names": ["module", "exports", "Promise", "prototype", "then"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/test 3/medical-assistant-react/node_modules/qrcode/lib/can-promise.js"], "sourcesContent": ["// can-promise has a crash in some versions of react native that dont have\n// standard global objects\n// https://github.com/soldair/node-qrcode/issues/157\n\nmodule.exports = function () {\n  return typeof Promise === 'function' && Promise.prototype && Promise.prototype.then\n}\n"], "mappings": "AAAA;AACA;AACA;;AAEAA,MAAM,CAACC,OAAO,GAAG,YAAY;EAC3B,OAAO,OAAOC,OAAO,KAAK,UAAU,IAAIA,OAAO,CAACC,SAAS,IAAID,OAAO,CAACC,SAAS,CAACC,IAAI;AACrF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}