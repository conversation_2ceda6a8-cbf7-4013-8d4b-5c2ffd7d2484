{"ast": null, "code": "const appearStoreId = (id, value) => \"\".concat(id, \": \").concat(value);\nexport { appearStoreId };", "map": {"version": 3, "names": ["appearStoreId", "id", "value", "concat"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/test 3/medical-assistant-react/node_modules/framer-motion/dist/es/animation/optimized-appear/store-id.mjs"], "sourcesContent": ["const appearStoreId = (id, value) => `${id}: ${value}`;\n\nexport { appearStoreId };\n"], "mappings": "AAAA,MAAMA,aAAa,GAAGA,CAACC,EAAE,EAAEC,KAAK,QAAAC,MAAA,CAAQF,EAAE,QAAAE,MAAA,CAAKD,KAAK,CAAE;AAEtD,SAASF,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}