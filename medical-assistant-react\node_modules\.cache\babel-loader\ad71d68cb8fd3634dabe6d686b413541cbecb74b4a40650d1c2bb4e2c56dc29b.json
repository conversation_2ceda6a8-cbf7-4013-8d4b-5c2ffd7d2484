{"ast": null, "code": "const Utils = require('./utils');\nfunction getColorAttrib(color, attrib) {\n  const alpha = color.a / 255;\n  const str = attrib + '=\"' + color.hex + '\"';\n  return alpha < 1 ? str + ' ' + attrib + '-opacity=\"' + alpha.toFixed(2).slice(1) + '\"' : str;\n}\nfunction svgCmd(cmd, x, y) {\n  let str = cmd + x;\n  if (typeof y !== 'undefined') str += ' ' + y;\n  return str;\n}\nfunction qrToPath(data, size, margin) {\n  let path = '';\n  let moveBy = 0;\n  let newRow = false;\n  let lineLength = 0;\n  for (let i = 0; i < data.length; i++) {\n    const col = Math.floor(i % size);\n    const row = Math.floor(i / size);\n    if (!col && !newRow) newRow = true;\n    if (data[i]) {\n      lineLength++;\n      if (!(i > 0 && col > 0 && data[i - 1])) {\n        path += newRow ? svgCmd('M', col + margin, 0.5 + row + margin) : svgCmd('m', moveBy, 0);\n        moveBy = 0;\n        newRow = false;\n      }\n      if (!(col + 1 < size && data[i + 1])) {\n        path += svgCmd('h', lineLength);\n        lineLength = 0;\n      }\n    } else {\n      moveBy++;\n    }\n  }\n  return path;\n}\nexports.render = function render(qrData, options, cb) {\n  const opts = Utils.getOptions(options);\n  const size = qrData.modules.size;\n  const data = qrData.modules.data;\n  const qrcodesize = size + opts.margin * 2;\n  const bg = !opts.color.light.a ? '' : '<path ' + getColorAttrib(opts.color.light, 'fill') + ' d=\"M0 0h' + qrcodesize + 'v' + qrcodesize + 'H0z\"/>';\n  const path = '<path ' + getColorAttrib(opts.color.dark, 'stroke') + ' d=\"' + qrToPath(data, size, opts.margin) + '\"/>';\n  const viewBox = 'viewBox=\"' + '0 0 ' + qrcodesize + ' ' + qrcodesize + '\"';\n  const width = !opts.width ? '' : 'width=\"' + opts.width + '\" height=\"' + opts.width + '\" ';\n  const svgTag = '<svg xmlns=\"http://www.w3.org/2000/svg\" ' + width + viewBox + ' shape-rendering=\"crispEdges\">' + bg + path + '</svg>\\n';\n  if (typeof cb === 'function') {\n    cb(null, svgTag);\n  }\n  return svgTag;\n};", "map": {"version": 3, "names": ["Utils", "require", "getColorAttrib", "color", "attrib", "alpha", "a", "str", "hex", "toFixed", "slice", "svgCmd", "cmd", "x", "y", "qrTo<PERSON><PERSON>", "data", "size", "margin", "path", "moveBy", "newRow", "lineLength", "i", "length", "col", "Math", "floor", "row", "exports", "render", "qrData", "options", "cb", "opts", "getOptions", "modules", "qrcodesize", "bg", "light", "dark", "viewBox", "width", "svgTag"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/test 3/medical-assistant-react/node_modules/qrcode/lib/renderer/svg-tag.js"], "sourcesContent": ["const Utils = require('./utils')\n\nfunction getColorAttrib (color, attrib) {\n  const alpha = color.a / 255\n  const str = attrib + '=\"' + color.hex + '\"'\n\n  return alpha < 1\n    ? str + ' ' + attrib + '-opacity=\"' + alpha.toFixed(2).slice(1) + '\"'\n    : str\n}\n\nfunction svgCmd (cmd, x, y) {\n  let str = cmd + x\n  if (typeof y !== 'undefined') str += ' ' + y\n\n  return str\n}\n\nfunction qrToPath (data, size, margin) {\n  let path = ''\n  let moveBy = 0\n  let newRow = false\n  let lineLength = 0\n\n  for (let i = 0; i < data.length; i++) {\n    const col = Math.floor(i % size)\n    const row = Math.floor(i / size)\n\n    if (!col && !newRow) newRow = true\n\n    if (data[i]) {\n      lineLength++\n\n      if (!(i > 0 && col > 0 && data[i - 1])) {\n        path += newRow\n          ? svgCmd('M', col + margin, 0.5 + row + margin)\n          : svgCmd('m', moveBy, 0)\n\n        moveBy = 0\n        newRow = false\n      }\n\n      if (!(col + 1 < size && data[i + 1])) {\n        path += svgCmd('h', lineLength)\n        lineLength = 0\n      }\n    } else {\n      moveBy++\n    }\n  }\n\n  return path\n}\n\nexports.render = function render (qrData, options, cb) {\n  const opts = Utils.getOptions(options)\n  const size = qrData.modules.size\n  const data = qrData.modules.data\n  const qrcodesize = size + opts.margin * 2\n\n  const bg = !opts.color.light.a\n    ? ''\n    : '<path ' + getColorAttrib(opts.color.light, 'fill') +\n      ' d=\"M0 0h' + qrcodesize + 'v' + qrcodesize + 'H0z\"/>'\n\n  const path =\n    '<path ' + getColorAttrib(opts.color.dark, 'stroke') +\n    ' d=\"' + qrToPath(data, size, opts.margin) + '\"/>'\n\n  const viewBox = 'viewBox=\"' + '0 0 ' + qrcodesize + ' ' + qrcodesize + '\"'\n\n  const width = !opts.width ? '' : 'width=\"' + opts.width + '\" height=\"' + opts.width + '\" '\n\n  const svgTag = '<svg xmlns=\"http://www.w3.org/2000/svg\" ' + width + viewBox + ' shape-rendering=\"crispEdges\">' + bg + path + '</svg>\\n'\n\n  if (typeof cb === 'function') {\n    cb(null, svgTag)\n  }\n\n  return svgTag\n}\n"], "mappings": "AAAA,MAAMA,KAAK,GAAGC,OAAO,CAAC,SAAS,CAAC;AAEhC,SAASC,cAAcA,CAAEC,KAAK,EAAEC,MAAM,EAAE;EACtC,MAAMC,KAAK,GAAGF,KAAK,CAACG,CAAC,GAAG,GAAG;EAC3B,MAAMC,GAAG,GAAGH,MAAM,GAAG,IAAI,GAAGD,KAAK,CAACK,GAAG,GAAG,GAAG;EAE3C,OAAOH,KAAK,GAAG,CAAC,GACZE,GAAG,GAAG,GAAG,GAAGH,MAAM,GAAG,YAAY,GAAGC,KAAK,CAACI,OAAO,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG,GACnEH,GAAG;AACT;AAEA,SAASI,MAAMA,CAAEC,GAAG,EAAEC,CAAC,EAAEC,CAAC,EAAE;EAC1B,IAAIP,GAAG,GAAGK,GAAG,GAAGC,CAAC;EACjB,IAAI,OAAOC,CAAC,KAAK,WAAW,EAAEP,GAAG,IAAI,GAAG,GAAGO,CAAC;EAE5C,OAAOP,GAAG;AACZ;AAEA,SAASQ,QAAQA,CAAEC,IAAI,EAAEC,IAAI,EAAEC,MAAM,EAAE;EACrC,IAAIC,IAAI,GAAG,EAAE;EACb,IAAIC,MAAM,GAAG,CAAC;EACd,IAAIC,MAAM,GAAG,KAAK;EAClB,IAAIC,UAAU,GAAG,CAAC;EAElB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,IAAI,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IACpC,MAAME,GAAG,GAAGC,IAAI,CAACC,KAAK,CAACJ,CAAC,GAAGN,IAAI,CAAC;IAChC,MAAMW,GAAG,GAAGF,IAAI,CAACC,KAAK,CAACJ,CAAC,GAAGN,IAAI,CAAC;IAEhC,IAAI,CAACQ,GAAG,IAAI,CAACJ,MAAM,EAAEA,MAAM,GAAG,IAAI;IAElC,IAAIL,IAAI,CAACO,CAAC,CAAC,EAAE;MACXD,UAAU,EAAE;MAEZ,IAAI,EAAEC,CAAC,GAAG,CAAC,IAAIE,GAAG,GAAG,CAAC,IAAIT,IAAI,CAACO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;QACtCJ,IAAI,IAAIE,MAAM,GACVV,MAAM,CAAC,GAAG,EAAEc,GAAG,GAAGP,MAAM,EAAE,GAAG,GAAGU,GAAG,GAAGV,MAAM,CAAC,GAC7CP,MAAM,CAAC,GAAG,EAAES,MAAM,EAAE,CAAC,CAAC;QAE1BA,MAAM,GAAG,CAAC;QACVC,MAAM,GAAG,KAAK;MAChB;MAEA,IAAI,EAAEI,GAAG,GAAG,CAAC,GAAGR,IAAI,IAAID,IAAI,CAACO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;QACpCJ,IAAI,IAAIR,MAAM,CAAC,GAAG,EAAEW,UAAU,CAAC;QAC/BA,UAAU,GAAG,CAAC;MAChB;IACF,CAAC,MAAM;MACLF,MAAM,EAAE;IACV;EACF;EAEA,OAAOD,IAAI;AACb;AAEAU,OAAO,CAACC,MAAM,GAAG,SAASA,MAAMA,CAAEC,MAAM,EAAEC,OAAO,EAAEC,EAAE,EAAE;EACrD,MAAMC,IAAI,GAAGlC,KAAK,CAACmC,UAAU,CAACH,OAAO,CAAC;EACtC,MAAMf,IAAI,GAAGc,MAAM,CAACK,OAAO,CAACnB,IAAI;EAChC,MAAMD,IAAI,GAAGe,MAAM,CAACK,OAAO,CAACpB,IAAI;EAChC,MAAMqB,UAAU,GAAGpB,IAAI,GAAGiB,IAAI,CAAChB,MAAM,GAAG,CAAC;EAEzC,MAAMoB,EAAE,GAAG,CAACJ,IAAI,CAAC/B,KAAK,CAACoC,KAAK,CAACjC,CAAC,GAC1B,EAAE,GACF,QAAQ,GAAGJ,cAAc,CAACgC,IAAI,CAAC/B,KAAK,CAACoC,KAAK,EAAE,MAAM,CAAC,GACnD,WAAW,GAAGF,UAAU,GAAG,GAAG,GAAGA,UAAU,GAAG,QAAQ;EAE1D,MAAMlB,IAAI,GACR,QAAQ,GAAGjB,cAAc,CAACgC,IAAI,CAAC/B,KAAK,CAACqC,IAAI,EAAE,QAAQ,CAAC,GACpD,MAAM,GAAGzB,QAAQ,CAACC,IAAI,EAAEC,IAAI,EAAEiB,IAAI,CAAChB,MAAM,CAAC,GAAG,KAAK;EAEpD,MAAMuB,OAAO,GAAG,WAAW,GAAG,MAAM,GAAGJ,UAAU,GAAG,GAAG,GAAGA,UAAU,GAAG,GAAG;EAE1E,MAAMK,KAAK,GAAG,CAACR,IAAI,CAACQ,KAAK,GAAG,EAAE,GAAG,SAAS,GAAGR,IAAI,CAACQ,KAAK,GAAG,YAAY,GAAGR,IAAI,CAACQ,KAAK,GAAG,IAAI;EAE1F,MAAMC,MAAM,GAAG,0CAA0C,GAAGD,KAAK,GAAGD,OAAO,GAAG,gCAAgC,GAAGH,EAAE,GAAGnB,IAAI,GAAG,UAAU;EAEvI,IAAI,OAAOc,EAAE,KAAK,UAAU,EAAE;IAC5BA,EAAE,CAAC,IAAI,EAAEU,MAAM,CAAC;EAClB;EAEA,OAAOA,MAAM;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}