{"ast": null, "code": "const numeric = '[0-9]+';\nconst alphanumeric = '[A-Z $%*+\\\\-./:]+';\nlet kanji = '(?:[u3000-u303F]|[u3040-u309F]|[u30A0-u30FF]|' + '[uFF00-uFFEF]|[u4E00-u9FAF]|[u2605-u2606]|[u2190-u2195]|u203B|' + '[u2010u2015u2018u2019u2025u2026u201Cu201Du2225u2260]|' + '[u0391-u0451]|[u00A7u00A8u00B1u00B4u00D7u00F7])+';\nkanji = kanji.replace(/u/g, '\\\\u');\nconst byte = '(?:(?![A-Z0-9 $%*+\\\\-./:]|' + kanji + ')(?:.|[\\r\\n]))+';\nexports.KANJI = new RegExp(kanji, 'g');\nexports.BYTE_KANJI = new RegExp('[^A-Z0-9 $%*+\\\\-./:]+', 'g');\nexports.BYTE = new RegExp(byte, 'g');\nexports.NUMERIC = new RegExp(numeric, 'g');\nexports.ALPHANUMERIC = new RegExp(alphanumeric, 'g');\nconst TEST_KANJI = new RegExp('^' + kanji + '$');\nconst TEST_NUMERIC = new RegExp('^' + numeric + '$');\nconst TEST_ALPHANUMERIC = new RegExp('^[A-Z0-9 $%*+\\\\-./:]+$');\nexports.testKanji = function testKanji(str) {\n  return TEST_KANJI.test(str);\n};\nexports.testNumeric = function testNumeric(str) {\n  return TEST_NUMERIC.test(str);\n};\nexports.testAlphanumeric = function testAlphanumeric(str) {\n  return TEST_ALPHANUMERIC.test(str);\n};", "map": {"version": 3, "names": ["numeric", "alphanumeric", "kanji", "replace", "byte", "exports", "KANJI", "RegExp", "BYTE_KANJI", "BYTE", "NUMERIC", "ALPHANUMERIC", "TEST_KANJI", "TEST_NUMERIC", "TEST_ALPHANUMERIC", "<PERSON><PERSON><PERSON><PERSON>", "str", "test", "testNumeric", "testAlphanumeric"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/test 3/medical-assistant-react/node_modules/qrcode/lib/core/regex.js"], "sourcesContent": ["const numeric = '[0-9]+'\nconst alphanumeric = '[A-Z $%*+\\\\-./:]+'\nlet kanji = '(?:[u3000-u303F]|[u3040-u309F]|[u30A0-u30FF]|' +\n  '[uFF00-uFFEF]|[u4E00-u9FAF]|[u2605-u2606]|[u2190-u2195]|u203B|' +\n  '[u2010u2015u2018u2019u2025u2026u201Cu201Du2225u2260]|' +\n  '[u0391-u0451]|[u00A7u00A8u00B1u00B4u00D7u00F7])+'\nkanji = kanji.replace(/u/g, '\\\\u')\n\nconst byte = '(?:(?![A-Z0-9 $%*+\\\\-./:]|' + kanji + ')(?:.|[\\r\\n]))+'\n\nexports.KANJI = new RegExp(kanji, 'g')\nexports.BYTE_KANJI = new RegExp('[^A-Z0-9 $%*+\\\\-./:]+', 'g')\nexports.BYTE = new RegExp(byte, 'g')\nexports.NUMERIC = new RegExp(numeric, 'g')\nexports.ALPHANUMERIC = new RegExp(alphanumeric, 'g')\n\nconst TEST_KANJI = new RegExp('^' + kanji + '$')\nconst TEST_NUMERIC = new RegExp('^' + numeric + '$')\nconst TEST_ALPHANUMERIC = new RegExp('^[A-Z0-9 $%*+\\\\-./:]+$')\n\nexports.testKanji = function testKanji (str) {\n  return TEST_KANJI.test(str)\n}\n\nexports.testNumeric = function testNumeric (str) {\n  return TEST_NUMERIC.test(str)\n}\n\nexports.testAlphanumeric = function testAlphanumeric (str) {\n  return TEST_ALPHANUMERIC.test(str)\n}\n"], "mappings": "AAAA,MAAMA,OAAO,GAAG,QAAQ;AACxB,MAAMC,YAAY,GAAG,mBAAmB;AACxC,IAAIC,KAAK,GAAG,+CAA+C,GACzD,gEAAgE,GAChE,uDAAuD,GACvD,kDAAkD;AACpDA,KAAK,GAAGA,KAAK,CAACC,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC;AAElC,MAAMC,IAAI,GAAG,4BAA4B,GAAGF,KAAK,GAAG,iBAAiB;AAErEG,OAAO,CAACC,KAAK,GAAG,IAAIC,MAAM,CAACL,KAAK,EAAE,GAAG,CAAC;AACtCG,OAAO,CAACG,UAAU,GAAG,IAAID,MAAM,CAAC,uBAAuB,EAAE,GAAG,CAAC;AAC7DF,OAAO,CAACI,IAAI,GAAG,IAAIF,MAAM,CAACH,IAAI,EAAE,GAAG,CAAC;AACpCC,OAAO,CAACK,OAAO,GAAG,IAAIH,MAAM,CAACP,OAAO,EAAE,GAAG,CAAC;AAC1CK,OAAO,CAACM,YAAY,GAAG,IAAIJ,MAAM,CAACN,YAAY,EAAE,GAAG,CAAC;AAEpD,MAAMW,UAAU,GAAG,IAAIL,MAAM,CAAC,GAAG,GAAGL,KAAK,GAAG,GAAG,CAAC;AAChD,MAAMW,YAAY,GAAG,IAAIN,MAAM,CAAC,GAAG,GAAGP,OAAO,GAAG,GAAG,CAAC;AACpD,MAAMc,iBAAiB,GAAG,IAAIP,MAAM,CAAC,wBAAwB,CAAC;AAE9DF,OAAO,CAACU,SAAS,GAAG,SAASA,SAASA,CAAEC,GAAG,EAAE;EAC3C,OAAOJ,UAAU,CAACK,IAAI,CAACD,GAAG,CAAC;AAC7B,CAAC;AAEDX,OAAO,CAACa,WAAW,GAAG,SAASA,WAAWA,CAAEF,GAAG,EAAE;EAC/C,OAAOH,YAAY,CAACI,IAAI,CAACD,GAAG,CAAC;AAC/B,CAAC;AAEDX,OAAO,CAACc,gBAAgB,GAAG,SAASA,gBAAgBA,CAAEH,GAAG,EAAE;EACzD,OAAOF,iBAAiB,CAACG,IAAI,CAACD,GAAG,CAAC;AACpC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}