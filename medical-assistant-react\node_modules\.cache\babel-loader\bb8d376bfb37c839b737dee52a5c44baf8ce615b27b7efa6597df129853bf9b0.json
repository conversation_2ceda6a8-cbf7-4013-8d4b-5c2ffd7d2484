{"ast": null, "code": "\"use client\";\n\nimport _objectSpread from \"C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/test 3/medical-assistant-react/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport _objectWithoutProperties from \"C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/test 3/medical-assistant-react/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\";\nconst _excluded = [\"animation\", \"bg\", \"bsPrefix\", \"size\"],\n  _excluded2 = [\"className\"];\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { useCol } from './Col';\nexport default function usePlaceholder(_ref) {\n  let {\n      animation,\n      bg,\n      bsPrefix,\n      size\n    } = _ref,\n    props = _objectWithoutProperties(_ref, _excluded);\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'placeholder');\n  const [_ref2] = useCol(props),\n    {\n      className\n    } = _ref2,\n    colProps = _objectWithoutProperties(_ref2, _excluded2);\n  return _objectSpread(_objectSpread({}, colProps), {}, {\n    className: classNames(className, animation ? \"\".concat(bsPrefix, \"-\").concat(animation) : bsPrefix, size && \"\".concat(bsPrefix, \"-\").concat(size), bg && \"bg-\".concat(bg))\n  });\n}", "map": {"version": 3, "names": ["_objectSpread", "_objectWithoutProperties", "_excluded", "_excluded2", "classNames", "useBootstrapPrefix", "useCol", "usePlaceholder", "_ref", "animation", "bg", "bsPrefix", "size", "props", "_ref2", "className", "colProps", "concat"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/test 3/medical-assistant-react/node_modules/react-bootstrap/esm/usePlaceholder.js"], "sourcesContent": ["\"use client\";\n\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { useCol } from './Col';\nexport default function usePlaceholder({\n  animation,\n  bg,\n  bsPrefix,\n  size,\n  ...props\n}) {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'placeholder');\n  const [{\n    className,\n    ...colProps\n  }] = useCol(props);\n  return {\n    ...colProps,\n    className: classNames(className, animation ? `${bsPrefix}-${animation}` : bsPrefix, size && `${bsPrefix}-${size}`, bg && `bg-${bg}`)\n  };\n}"], "mappings": "AAAA,YAAY;;AAAC,OAAAA,aAAA;AAAA,OAAAC,wBAAA;AAAA,MAAAC,SAAA;EAAAC,UAAA;AAEb,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,SAASC,MAAM,QAAQ,OAAO;AAC9B,eAAe,SAASC,cAAcA,CAAAC,IAAA,EAMnC;EAAA,IANoC;MACrCC,SAAS;MACTC,EAAE;MACFC,QAAQ;MACRC;IAEF,CAAC,GAAAJ,IAAA;IADIK,KAAK,GAAAZ,wBAAA,CAAAO,IAAA,EAAAN,SAAA;EAERS,QAAQ,GAAGN,kBAAkB,CAACM,QAAQ,EAAE,aAAa,CAAC;EACtD,MAAM,CAAAG,KAAA,CAGJ,GAAGR,MAAM,CAACO,KAAK,CAAC;IAHX;MACLE;IAEF,CAAC,GAAAD,KAAA;IADIE,QAAQ,GAAAf,wBAAA,CAAAa,KAAA,EAAAX,UAAA;EAEb,OAAAH,aAAA,CAAAA,aAAA,KACKgB,QAAQ;IACXD,SAAS,EAAEX,UAAU,CAACW,SAAS,EAAEN,SAAS,MAAAQ,MAAA,CAAMN,QAAQ,OAAAM,MAAA,CAAIR,SAAS,IAAKE,QAAQ,EAAEC,IAAI,OAAAK,MAAA,CAAON,QAAQ,OAAAM,MAAA,CAAIL,IAAI,CAAE,EAAEF,EAAE,UAAAO,MAAA,CAAUP,EAAE,CAAE;EAAC;AAExI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}