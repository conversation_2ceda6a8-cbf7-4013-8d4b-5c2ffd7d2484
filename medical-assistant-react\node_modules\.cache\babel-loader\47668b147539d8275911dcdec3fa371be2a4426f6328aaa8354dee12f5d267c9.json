{"ast": null, "code": "import _objectSpread from \"C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/test 3/medical-assistant-react/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport * as React from 'react';\nimport { ENTERING, EXITING } from 'react-transition-group/Transition';\nimport Fade from './Fade';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst fadeStyles = {\n  [ENTERING]: 'showing',\n  [EXITING]: 'showing show'\n};\nconst ToastFade = /*#__PURE__*/React.forwardRef((props, ref) => /*#__PURE__*/_jsx(Fade, _objectSpread(_objectSpread({}, props), {}, {\n  ref: ref,\n  transitionClasses: fadeStyles\n})));\nToastFade.displayName = 'ToastFade';\nexport default ToastFade;", "map": {"version": 3, "names": ["React", "ENTERING", "EXITING", "Fade", "jsx", "_jsx", "fadeStyles", "ToastFade", "forwardRef", "props", "ref", "_objectSpread", "transitionClasses", "displayName"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/test 3/medical-assistant-react/node_modules/react-bootstrap/esm/ToastFade.js"], "sourcesContent": ["import * as React from 'react';\nimport { ENTERING, EXITING } from 'react-transition-group/Transition';\nimport Fade from './Fade';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst fadeStyles = {\n  [ENTERING]: 'showing',\n  [EXITING]: 'showing show'\n};\nconst ToastFade = /*#__PURE__*/React.forwardRef((props, ref) => /*#__PURE__*/_jsx(Fade, {\n  ...props,\n  ref: ref,\n  transitionClasses: fadeStyles\n}));\nToastFade.displayName = 'ToastFade';\nexport default ToastFade;"], "mappings": ";AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,EAAEC,OAAO,QAAQ,mCAAmC;AACrE,OAAOC,IAAI,MAAM,QAAQ;AACzB,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,UAAU,GAAG;EACjB,CAACL,QAAQ,GAAG,SAAS;EACrB,CAACC,OAAO,GAAG;AACb,CAAC;AACD,MAAMK,SAAS,GAAG,aAAaP,KAAK,CAACQ,UAAU,CAAC,CAACC,KAAK,EAAEC,GAAG,KAAK,aAAaL,IAAI,CAACF,IAAI,EAAAQ,aAAA,CAAAA,aAAA,KACjFF,KAAK;EACRC,GAAG,EAAEA,GAAG;EACRE,iBAAiB,EAAEN;AAAU,EAC9B,CAAC,CAAC;AACHC,SAAS,CAACM,WAAW,GAAG,WAAW;AACnC,eAAeN,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}