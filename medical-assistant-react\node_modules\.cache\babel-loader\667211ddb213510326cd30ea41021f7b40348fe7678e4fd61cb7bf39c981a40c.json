{"ast": null, "code": "import { makeUseVisualState } from '../../motion/utils/use-visual-state.mjs';\nimport { scrapeMotionValuesFromProps } from './utils/scrape-motion-values.mjs';\nimport { createHtmlRenderState } from './utils/create-render-state.mjs';\nconst htmlMotionConfig = {\n  useVisualState: makeUseVisualState({\n    scrapeMotionValuesFromProps,\n    createRenderState: createHtmlRenderState\n  })\n};\nexport { htmlMotionConfig };", "map": {"version": 3, "names": ["makeUseVisualState", "scrapeMotionValuesFromProps", "createHtmlRenderState", "htmlMotionConfig", "useVisualState", "createRenderState"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/test 3/medical-assistant-react/node_modules/framer-motion/dist/es/render/html/config-motion.mjs"], "sourcesContent": ["import { makeUseVisualState } from '../../motion/utils/use-visual-state.mjs';\nimport { scrapeMotionValuesFromProps } from './utils/scrape-motion-values.mjs';\nimport { createHtmlRenderState } from './utils/create-render-state.mjs';\n\nconst htmlMotionConfig = {\n    useVisualState: makeUseVisualState({\n        scrapeMotionValuesFromProps,\n        createRenderState: createHtmlRenderState,\n    }),\n};\n\nexport { htmlMotionConfig };\n"], "mappings": "AAAA,SAASA,kBAAkB,QAAQ,yCAAyC;AAC5E,SAASC,2BAA2B,QAAQ,kCAAkC;AAC9E,SAASC,qBAAqB,QAAQ,iCAAiC;AAEvE,MAAMC,gBAAgB,GAAG;EACrBC,cAAc,EAAEJ,kBAAkB,CAAC;IAC/BC,2BAA2B;IAC3BI,iBAAiB,EAAEH;EACvB,CAAC;AACL,CAAC;AAED,SAASC,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}