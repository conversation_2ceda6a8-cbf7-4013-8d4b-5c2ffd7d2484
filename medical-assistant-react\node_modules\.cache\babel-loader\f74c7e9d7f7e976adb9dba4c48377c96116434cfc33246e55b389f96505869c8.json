{"ast": null, "code": "import listen from 'dom-helpers/listen';\nimport ownerDocument from 'dom-helpers/ownerDocument';\nimport { useEffect } from 'react';\nimport useEventCallback from '@restart/hooks/useEventCallback';\nimport useClickOutside, { getRefTarget } from './useClickOutside';\nimport { isEscKey } from './utils';\nconst noop = () => {};\n/**\n * The `useRootClose` hook registers your callback on the document\n * when rendered. Powers the `<Overlay/>` component. This is used achieve modal\n * style behavior where your callback is triggered when the user tries to\n * interact with the rest of the document or hits the `esc` key.\n *\n * @param {Ref<HTMLElement>| HTMLElement} ref  The element boundary\n * @param {function} onRootClose\n * @param {object=}  options\n * @param {boolean=} options.disabled\n * @param {string=}  options.clickTrigger The DOM event name (click, mousedown, etc) to attach listeners on\n */\nfunction useRootClose(ref, onRootClose) {\n  let {\n    disabled,\n    clickTrigger\n  } = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n  const onClose = onRootClose || noop;\n  useClickOutside(ref, onClose, {\n    disabled,\n    clickTrigger\n  });\n  const handleKeyUp = useEventCallback(e => {\n    if (isEscKey(e)) {\n      onClose(e);\n    }\n  });\n  useEffect(() => {\n    if (disabled || ref == null) return undefined;\n    const doc = ownerDocument(getRefTarget(ref));\n\n    // Store the current event to avoid triggering handlers immediately\n    // https://github.com/facebook/react/issues/20074\n    let currentEvent = (doc.defaultView || window).event;\n    const removeKeyupListener = listen(doc, 'keyup', e => {\n      // skip if this event is the same as the one running when we added the handlers\n      if (e === currentEvent) {\n        currentEvent = undefined;\n        return;\n      }\n      handleKeyUp(e);\n    });\n    return () => {\n      removeKeyupListener();\n    };\n  }, [ref, disabled, handleKeyUp]);\n}\nexport default useRootClose;", "map": {"version": 3, "names": ["listen", "ownerDocument", "useEffect", "useEventCallback", "useClickOutside", "getRefTarget", "isEscKey", "noop", "useRootClose", "ref", "onRootClose", "disabled", "clickTrigger", "arguments", "length", "undefined", "onClose", "handleKeyUp", "e", "doc", "currentEvent", "defaultView", "window", "event", "removeKeyupListener"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/test 3/medical-assistant-react/node_modules/@restart/ui/esm/useRootClose.js"], "sourcesContent": ["import listen from 'dom-helpers/listen';\nimport ownerDocument from 'dom-helpers/ownerDocument';\nimport { useEffect } from 'react';\nimport useEventCallback from '@restart/hooks/useEventCallback';\nimport useClickOutside, { getRefTarget } from './useClickOutside';\nimport { isEscKey } from './utils';\nconst noop = () => {};\n/**\n * The `useRootClose` hook registers your callback on the document\n * when rendered. Powers the `<Overlay/>` component. This is used achieve modal\n * style behavior where your callback is triggered when the user tries to\n * interact with the rest of the document or hits the `esc` key.\n *\n * @param {Ref<HTMLElement>| HTMLElement} ref  The element boundary\n * @param {function} onRootClose\n * @param {object=}  options\n * @param {boolean=} options.disabled\n * @param {string=}  options.clickTrigger The DOM event name (click, mousedown, etc) to attach listeners on\n */\nfunction useRootClose(ref, onRootClose, {\n  disabled,\n  clickTrigger\n} = {}) {\n  const onClose = onRootClose || noop;\n  useClickOutside(ref, onClose, {\n    disabled,\n    clickTrigger\n  });\n  const handleKeyUp = useEventCallback(e => {\n    if (isEscKey(e)) {\n      onClose(e);\n    }\n  });\n  useEffect(() => {\n    if (disabled || ref == null) return undefined;\n    const doc = ownerDocument(getRefTarget(ref));\n\n    // Store the current event to avoid triggering handlers immediately\n    // https://github.com/facebook/react/issues/20074\n    let currentEvent = (doc.defaultView || window).event;\n    const removeKeyupListener = listen(doc, 'keyup', e => {\n      // skip if this event is the same as the one running when we added the handlers\n      if (e === currentEvent) {\n        currentEvent = undefined;\n        return;\n      }\n      handleKeyUp(e);\n    });\n    return () => {\n      removeKeyupListener();\n    };\n  }, [ref, disabled, handleKeyUp]);\n}\nexport default useRootClose;"], "mappings": "AAAA,OAAOA,MAAM,MAAM,oBAAoB;AACvC,OAAOC,aAAa,MAAM,2BAA2B;AACrD,SAASC,SAAS,QAAQ,OAAO;AACjC,OAAOC,gBAAgB,MAAM,iCAAiC;AAC9D,OAAOC,eAAe,IAAIC,YAAY,QAAQ,mBAAmB;AACjE,SAASC,QAAQ,QAAQ,SAAS;AAClC,MAAMC,IAAI,GAAGA,CAAA,KAAM,CAAC,CAAC;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,YAAYA,CAACC,GAAG,EAAEC,WAAW,EAG9B;EAAA,IAHgC;IACtCC,QAAQ;IACRC;EACF,CAAC,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EACJ,MAAMG,OAAO,GAAGN,WAAW,IAAIH,IAAI;EACnCH,eAAe,CAACK,GAAG,EAAEO,OAAO,EAAE;IAC5BL,QAAQ;IACRC;EACF,CAAC,CAAC;EACF,MAAMK,WAAW,GAAGd,gBAAgB,CAACe,CAAC,IAAI;IACxC,IAAIZ,QAAQ,CAACY,CAAC,CAAC,EAAE;MACfF,OAAO,CAACE,CAAC,CAAC;IACZ;EACF,CAAC,CAAC;EACFhB,SAAS,CAAC,MAAM;IACd,IAAIS,QAAQ,IAAIF,GAAG,IAAI,IAAI,EAAE,OAAOM,SAAS;IAC7C,MAAMI,GAAG,GAAGlB,aAAa,CAACI,YAAY,CAACI,GAAG,CAAC,CAAC;;IAE5C;IACA;IACA,IAAIW,YAAY,GAAG,CAACD,GAAG,CAACE,WAAW,IAAIC,MAAM,EAAEC,KAAK;IACpD,MAAMC,mBAAmB,GAAGxB,MAAM,CAACmB,GAAG,EAAE,OAAO,EAAED,CAAC,IAAI;MACpD;MACA,IAAIA,CAAC,KAAKE,YAAY,EAAE;QACtBA,YAAY,GAAGL,SAAS;QACxB;MACF;MACAE,WAAW,CAACC,CAAC,CAAC;IAChB,CAAC,CAAC;IACF,OAAO,MAAM;MACXM,mBAAmB,CAAC,CAAC;IACvB,CAAC;EACH,CAAC,EAAE,CAACf,GAAG,EAAEE,QAAQ,EAAEM,WAAW,CAAC,CAAC;AAClC;AACA,eAAeT,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}