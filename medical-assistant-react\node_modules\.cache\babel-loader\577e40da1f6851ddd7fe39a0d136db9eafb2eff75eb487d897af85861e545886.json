{"ast": null, "code": "import useCallbackRef from './useCallbackRef';\nimport useCommittedRef from './useCommittedRef';\nimport useEventCallback from './useEventCallback';\nimport useEventListener from './useEventListener';\nimport useGlobalListener from './useGlobalListener';\nimport useInterval from './useInterval';\nimport useRafInterval from './useRafInterval';\nimport useMergeState from './useMergeState';\nimport useMergeStateFromProps from './useMergeStateFromProps';\nimport useMounted from './useMounted';\nimport usePrevious from './usePrevious';\nimport useImage from './useImage';\nimport useResizeObserver from './useResizeObserver';\nexport { useCallbackRef, useCommittedRef, useEventCallback, useEventListener, useGlobalListener, useInterval, useRafInterval, useMergeState, useMergeStateFromProps, useMounted, usePrevious, useImage, useResizeObserver };", "map": {"version": 3, "names": ["useCallbackRef", "useCommittedRef", "useEventCallback", "useEventListener", "useGlobalListener", "useInterval", "useRafInterval", "useMergeState", "useMergeStateFromProps", "useMounted", "usePrevious", "useImage", "useResizeObserver"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/test 3/medical-assistant-react/node_modules/@restart/ui/node_modules/@restart/hooks/esm/index.js"], "sourcesContent": ["import useCallbackRef from './useCallbackRef';\nimport useCommittedRef from './useCommittedRef';\nimport useEventCallback from './useEventCallback';\nimport useEventListener from './useEventListener';\nimport useGlobalListener from './useGlobalListener';\nimport useInterval from './useInterval';\nimport useRafInterval from './useRafInterval';\nimport useMergeState from './useMergeState';\nimport useMergeStateFromProps from './useMergeStateFromProps';\nimport useMounted from './useMounted';\nimport usePrevious from './usePrevious';\nimport useImage from './useImage';\nimport useResizeObserver from './useResizeObserver';\nexport { useCallbackRef, useCommittedRef, useEventCallback, useEventListener, useGlobalListener, useInterval, useRafInterval, useMergeState, useMergeStateFromProps, useMounted, usePrevious, useImage, useResizeObserver };"], "mappings": "AAAA,OAAOA,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,sBAAsB,MAAM,0BAA0B;AAC7D,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,SAASZ,cAAc,EAAEC,eAAe,EAAEC,gBAAgB,EAAEC,gBAAgB,EAAEC,iBAAiB,EAAEC,WAAW,EAAEC,cAAc,EAAEC,aAAa,EAAEC,sBAAsB,EAAEC,UAAU,EAAEC,WAAW,EAAEC,QAAQ,EAAEC,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}