{"ast": null, "code": "import getVariation from \"./getVariation.js\";\nimport { variationPlacements, basePlacements, placements as allPlacements } from \"../enums.js\";\nimport detectOverflow from \"./detectOverflow.js\";\nimport getBasePlacement from \"./getBasePlacement.js\";\nexport default function computeAutoPlacement(state, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  var _options = options,\n    placement = _options.placement,\n    boundary = _options.boundary,\n    rootBoundary = _options.rootBoundary,\n    padding = _options.padding,\n    flipVariations = _options.flipVariations,\n    _options$allowedAutoP = _options.allowedAutoPlacements,\n    allowedAutoPlacements = _options$allowedAutoP === void 0 ? allPlacements : _options$allowedAutoP;\n  var variation = getVariation(placement);\n  var placements = variation ? flipVariations ? variationPlacements : variationPlacements.filter(function (placement) {\n    return getVariation(placement) === variation;\n  }) : basePlacements;\n  var allowedPlacements = placements.filter(function (placement) {\n    return allowedAutoPlacements.indexOf(placement) >= 0;\n  });\n  if (allowedPlacements.length === 0) {\n    allowedPlacements = placements;\n  } // $FlowFixMe[incompatible-type]: Flow seems to have problems with two array unions...\n\n  var overflows = allowedPlacements.reduce(function (acc, placement) {\n    acc[placement] = detectOverflow(state, {\n      placement: placement,\n      boundary: boundary,\n      rootBoundary: rootBoundary,\n      padding: padding\n    })[getBasePlacement(placement)];\n    return acc;\n  }, {});\n  return Object.keys(overflows).sort(function (a, b) {\n    return overflows[a] - overflows[b];\n  });\n}", "map": {"version": 3, "names": ["getVariation", "variationPlacements", "basePlacements", "placements", "allPlacements", "detectOverflow", "getBasePlacement", "computeAutoPlacement", "state", "options", "_options", "placement", "boundary", "rootBoundary", "padding", "flipVariations", "_options$allowedAutoP", "allowedAutoPlacements", "variation", "filter", "allowedPlacements", "indexOf", "length", "overflows", "reduce", "acc", "Object", "keys", "sort", "a", "b"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/test 3/medical-assistant-react/node_modules/@popperjs/core/lib/utils/computeAutoPlacement.js"], "sourcesContent": ["import getVariation from \"./getVariation.js\";\nimport { variationPlacements, basePlacements, placements as allPlacements } from \"../enums.js\";\nimport detectOverflow from \"./detectOverflow.js\";\nimport getBasePlacement from \"./getBasePlacement.js\";\nexport default function computeAutoPlacement(state, options) {\n  if (options === void 0) {\n    options = {};\n  }\n\n  var _options = options,\n      placement = _options.placement,\n      boundary = _options.boundary,\n      rootBoundary = _options.rootBoundary,\n      padding = _options.padding,\n      flipVariations = _options.flipVariations,\n      _options$allowedAutoP = _options.allowedAutoPlacements,\n      allowedAutoPlacements = _options$allowedAutoP === void 0 ? allPlacements : _options$allowedAutoP;\n  var variation = getVariation(placement);\n  var placements = variation ? flipVariations ? variationPlacements : variationPlacements.filter(function (placement) {\n    return getVariation(placement) === variation;\n  }) : basePlacements;\n  var allowedPlacements = placements.filter(function (placement) {\n    return allowedAutoPlacements.indexOf(placement) >= 0;\n  });\n\n  if (allowedPlacements.length === 0) {\n    allowedPlacements = placements;\n  } // $FlowFixMe[incompatible-type]: Flow seems to have problems with two array unions...\n\n\n  var overflows = allowedPlacements.reduce(function (acc, placement) {\n    acc[placement] = detectOverflow(state, {\n      placement: placement,\n      boundary: boundary,\n      rootBoundary: rootBoundary,\n      padding: padding\n    })[getBasePlacement(placement)];\n    return acc;\n  }, {});\n  return Object.keys(overflows).sort(function (a, b) {\n    return overflows[a] - overflows[b];\n  });\n}"], "mappings": "AAAA,OAAOA,YAAY,MAAM,mBAAmB;AAC5C,SAASC,mBAAmB,EAAEC,cAAc,EAAEC,UAAU,IAAIC,aAAa,QAAQ,aAAa;AAC9F,OAAOC,cAAc,MAAM,qBAAqB;AAChD,OAAOC,gBAAgB,MAAM,uBAAuB;AACpD,eAAe,SAASC,oBAAoBA,CAACC,KAAK,EAAEC,OAAO,EAAE;EAC3D,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;IACtBA,OAAO,GAAG,CAAC,CAAC;EACd;EAEA,IAAIC,QAAQ,GAAGD,OAAO;IAClBE,SAAS,GAAGD,QAAQ,CAACC,SAAS;IAC9BC,QAAQ,GAAGF,QAAQ,CAACE,QAAQ;IAC5BC,YAAY,GAAGH,QAAQ,CAACG,YAAY;IACpCC,OAAO,GAAGJ,QAAQ,CAACI,OAAO;IAC1BC,cAAc,GAAGL,QAAQ,CAACK,cAAc;IACxCC,qBAAqB,GAAGN,QAAQ,CAACO,qBAAqB;IACtDA,qBAAqB,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAGZ,aAAa,GAAGY,qBAAqB;EACpG,IAAIE,SAAS,GAAGlB,YAAY,CAACW,SAAS,CAAC;EACvC,IAAIR,UAAU,GAAGe,SAAS,GAAGH,cAAc,GAAGd,mBAAmB,GAAGA,mBAAmB,CAACkB,MAAM,CAAC,UAAUR,SAAS,EAAE;IAClH,OAAOX,YAAY,CAACW,SAAS,CAAC,KAAKO,SAAS;EAC9C,CAAC,CAAC,GAAGhB,cAAc;EACnB,IAAIkB,iBAAiB,GAAGjB,UAAU,CAACgB,MAAM,CAAC,UAAUR,SAAS,EAAE;IAC7D,OAAOM,qBAAqB,CAACI,OAAO,CAACV,SAAS,CAAC,IAAI,CAAC;EACtD,CAAC,CAAC;EAEF,IAAIS,iBAAiB,CAACE,MAAM,KAAK,CAAC,EAAE;IAClCF,iBAAiB,GAAGjB,UAAU;EAChC,CAAC,CAAC;;EAGF,IAAIoB,SAAS,GAAGH,iBAAiB,CAACI,MAAM,CAAC,UAAUC,GAAG,EAAEd,SAAS,EAAE;IACjEc,GAAG,CAACd,SAAS,CAAC,GAAGN,cAAc,CAACG,KAAK,EAAE;MACrCG,SAAS,EAAEA,SAAS;MACpBC,QAAQ,EAAEA,QAAQ;MAClBC,YAAY,EAAEA,YAAY;MAC1BC,OAAO,EAAEA;IACX,CAAC,CAAC,CAACR,gBAAgB,CAACK,SAAS,CAAC,CAAC;IAC/B,OAAOc,GAAG;EACZ,CAAC,EAAE,CAAC,CAAC,CAAC;EACN,OAAOC,MAAM,CAACC,IAAI,CAACJ,SAAS,CAAC,CAACK,IAAI,CAAC,UAAUC,CAAC,EAAEC,CAAC,EAAE;IACjD,OAAOP,SAAS,CAACM,CAAC,CAAC,GAAGN,SAAS,CAACO,CAAC,CAAC;EACpC,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}