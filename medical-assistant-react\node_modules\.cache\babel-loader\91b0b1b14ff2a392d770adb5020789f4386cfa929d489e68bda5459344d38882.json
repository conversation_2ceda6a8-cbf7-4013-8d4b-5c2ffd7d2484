{"ast": null, "code": "const Utils = require('./utils');\nconst ECLevel = require('./error-correction-level');\nconst BitBuffer = require('./bit-buffer');\nconst BitMatrix = require('./bit-matrix');\nconst AlignmentPattern = require('./alignment-pattern');\nconst FinderPattern = require('./finder-pattern');\nconst MaskPattern = require('./mask-pattern');\nconst ECCode = require('./error-correction-code');\nconst ReedSolomonEncoder = require('./reed-solomon-encoder');\nconst Version = require('./version');\nconst FormatInfo = require('./format-info');\nconst Mode = require('./mode');\nconst Segments = require('./segments');\n\n/**\n * QRCode for JavaScript\n *\n * modified by <PERSON> for nodejs support\n * Copyright (c) 2011 Ryan Day\n *\n * Licensed under the MIT license:\n *   http://www.opensource.org/licenses/mit-license.php\n *\n//---------------------------------------------------------------------\n// QRCode for JavaScript\n//\n// Copyright (c) 2009 Kazuhiko Arase\n//\n// URL: http://www.d-project.com/\n//\n// Licensed under the MIT license:\n//   http://www.opensource.org/licenses/mit-license.php\n//\n// The word \"QR Code\" is registered trademark of\n// DENSO WAVE INCORPORATED\n//   http://www.denso-wave.com/qrcode/faqpatent-e.html\n//\n//---------------------------------------------------------------------\n*/\n\n/**\n * Add finder patterns bits to matrix\n *\n * @param  {BitMatrix} matrix  Modules matrix\n * @param  {Number}    version QR Code version\n */\nfunction setupFinderPattern(matrix, version) {\n  const size = matrix.size;\n  const pos = FinderPattern.getPositions(version);\n  for (let i = 0; i < pos.length; i++) {\n    const row = pos[i][0];\n    const col = pos[i][1];\n    for (let r = -1; r <= 7; r++) {\n      if (row + r <= -1 || size <= row + r) continue;\n      for (let c = -1; c <= 7; c++) {\n        if (col + c <= -1 || size <= col + c) continue;\n        if (r >= 0 && r <= 6 && (c === 0 || c === 6) || c >= 0 && c <= 6 && (r === 0 || r === 6) || r >= 2 && r <= 4 && c >= 2 && c <= 4) {\n          matrix.set(row + r, col + c, true, true);\n        } else {\n          matrix.set(row + r, col + c, false, true);\n        }\n      }\n    }\n  }\n}\n\n/**\n * Add timing pattern bits to matrix\n *\n * Note: this function must be called before {@link setupAlignmentPattern}\n *\n * @param  {BitMatrix} matrix Modules matrix\n */\nfunction setupTimingPattern(matrix) {\n  const size = matrix.size;\n  for (let r = 8; r < size - 8; r++) {\n    const value = r % 2 === 0;\n    matrix.set(r, 6, value, true);\n    matrix.set(6, r, value, true);\n  }\n}\n\n/**\n * Add alignment patterns bits to matrix\n *\n * Note: this function must be called after {@link setupTimingPattern}\n *\n * @param  {BitMatrix} matrix  Modules matrix\n * @param  {Number}    version QR Code version\n */\nfunction setupAlignmentPattern(matrix, version) {\n  const pos = AlignmentPattern.getPositions(version);\n  for (let i = 0; i < pos.length; i++) {\n    const row = pos[i][0];\n    const col = pos[i][1];\n    for (let r = -2; r <= 2; r++) {\n      for (let c = -2; c <= 2; c++) {\n        if (r === -2 || r === 2 || c === -2 || c === 2 || r === 0 && c === 0) {\n          matrix.set(row + r, col + c, true, true);\n        } else {\n          matrix.set(row + r, col + c, false, true);\n        }\n      }\n    }\n  }\n}\n\n/**\n * Add version info bits to matrix\n *\n * @param  {BitMatrix} matrix  Modules matrix\n * @param  {Number}    version QR Code version\n */\nfunction setupVersionInfo(matrix, version) {\n  const size = matrix.size;\n  const bits = Version.getEncodedBits(version);\n  let row, col, mod;\n  for (let i = 0; i < 18; i++) {\n    row = Math.floor(i / 3);\n    col = i % 3 + size - 8 - 3;\n    mod = (bits >> i & 1) === 1;\n    matrix.set(row, col, mod, true);\n    matrix.set(col, row, mod, true);\n  }\n}\n\n/**\n * Add format info bits to matrix\n *\n * @param  {BitMatrix} matrix               Modules matrix\n * @param  {ErrorCorrectionLevel}    errorCorrectionLevel Error correction level\n * @param  {Number}    maskPattern          Mask pattern reference value\n */\nfunction setupFormatInfo(matrix, errorCorrectionLevel, maskPattern) {\n  const size = matrix.size;\n  const bits = FormatInfo.getEncodedBits(errorCorrectionLevel, maskPattern);\n  let i, mod;\n  for (i = 0; i < 15; i++) {\n    mod = (bits >> i & 1) === 1;\n\n    // vertical\n    if (i < 6) {\n      matrix.set(i, 8, mod, true);\n    } else if (i < 8) {\n      matrix.set(i + 1, 8, mod, true);\n    } else {\n      matrix.set(size - 15 + i, 8, mod, true);\n    }\n\n    // horizontal\n    if (i < 8) {\n      matrix.set(8, size - i - 1, mod, true);\n    } else if (i < 9) {\n      matrix.set(8, 15 - i - 1 + 1, mod, true);\n    } else {\n      matrix.set(8, 15 - i - 1, mod, true);\n    }\n  }\n\n  // fixed module\n  matrix.set(size - 8, 8, 1, true);\n}\n\n/**\n * Add encoded data bits to matrix\n *\n * @param  {BitMatrix}  matrix Modules matrix\n * @param  {Uint8Array} data   Data codewords\n */\nfunction setupData(matrix, data) {\n  const size = matrix.size;\n  let inc = -1;\n  let row = size - 1;\n  let bitIndex = 7;\n  let byteIndex = 0;\n  for (let col = size - 1; col > 0; col -= 2) {\n    if (col === 6) col--;\n    while (true) {\n      for (let c = 0; c < 2; c++) {\n        if (!matrix.isReserved(row, col - c)) {\n          let dark = false;\n          if (byteIndex < data.length) {\n            dark = (data[byteIndex] >>> bitIndex & 1) === 1;\n          }\n          matrix.set(row, col - c, dark);\n          bitIndex--;\n          if (bitIndex === -1) {\n            byteIndex++;\n            bitIndex = 7;\n          }\n        }\n      }\n      row += inc;\n      if (row < 0 || size <= row) {\n        row -= inc;\n        inc = -inc;\n        break;\n      }\n    }\n  }\n}\n\n/**\n * Create encoded codewords from data input\n *\n * @param  {Number}   version              QR Code version\n * @param  {ErrorCorrectionLevel}   errorCorrectionLevel Error correction level\n * @param  {ByteData} data                 Data input\n * @return {Uint8Array}                    Buffer containing encoded codewords\n */\nfunction createData(version, errorCorrectionLevel, segments) {\n  // Prepare data buffer\n  const buffer = new BitBuffer();\n  segments.forEach(function (data) {\n    // prefix data with mode indicator (4 bits)\n    buffer.put(data.mode.bit, 4);\n\n    // Prefix data with character count indicator.\n    // The character count indicator is a string of bits that represents the\n    // number of characters that are being encoded.\n    // The character count indicator must be placed after the mode indicator\n    // and must be a certain number of bits long, depending on the QR version\n    // and data mode\n    // @see {@link Mode.getCharCountIndicator}.\n    buffer.put(data.getLength(), Mode.getCharCountIndicator(data.mode, version));\n\n    // add binary data sequence to buffer\n    data.write(buffer);\n  });\n\n  // Calculate required number of bits\n  const totalCodewords = Utils.getSymbolTotalCodewords(version);\n  const ecTotalCodewords = ECCode.getTotalCodewordsCount(version, errorCorrectionLevel);\n  const dataTotalCodewordsBits = (totalCodewords - ecTotalCodewords) * 8;\n\n  // Add a terminator.\n  // If the bit string is shorter than the total number of required bits,\n  // a terminator of up to four 0s must be added to the right side of the string.\n  // If the bit string is more than four bits shorter than the required number of bits,\n  // add four 0s to the end.\n  if (buffer.getLengthInBits() + 4 <= dataTotalCodewordsBits) {\n    buffer.put(0, 4);\n  }\n\n  // If the bit string is fewer than four bits shorter, add only the number of 0s that\n  // are needed to reach the required number of bits.\n\n  // After adding the terminator, if the number of bits in the string is not a multiple of 8,\n  // pad the string on the right with 0s to make the string's length a multiple of 8.\n  while (buffer.getLengthInBits() % 8 !== 0) {\n    buffer.putBit(0);\n  }\n\n  // Add pad bytes if the string is still shorter than the total number of required bits.\n  // Extend the buffer to fill the data capacity of the symbol corresponding to\n  // the Version and Error Correction Level by adding the Pad Codewords 11101100 (0xEC)\n  // and 00010001 (0x11) alternately.\n  const remainingByte = (dataTotalCodewordsBits - buffer.getLengthInBits()) / 8;\n  for (let i = 0; i < remainingByte; i++) {\n    buffer.put(i % 2 ? 0x11 : 0xEC, 8);\n  }\n  return createCodewords(buffer, version, errorCorrectionLevel);\n}\n\n/**\n * Encode input data with Reed-Solomon and return codewords with\n * relative error correction bits\n *\n * @param  {BitBuffer} bitBuffer            Data to encode\n * @param  {Number}    version              QR Code version\n * @param  {ErrorCorrectionLevel} errorCorrectionLevel Error correction level\n * @return {Uint8Array}                     Buffer containing encoded codewords\n */\nfunction createCodewords(bitBuffer, version, errorCorrectionLevel) {\n  // Total codewords for this QR code version (Data + Error correction)\n  const totalCodewords = Utils.getSymbolTotalCodewords(version);\n\n  // Total number of error correction codewords\n  const ecTotalCodewords = ECCode.getTotalCodewordsCount(version, errorCorrectionLevel);\n\n  // Total number of data codewords\n  const dataTotalCodewords = totalCodewords - ecTotalCodewords;\n\n  // Total number of blocks\n  const ecTotalBlocks = ECCode.getBlocksCount(version, errorCorrectionLevel);\n\n  // Calculate how many blocks each group should contain\n  const blocksInGroup2 = totalCodewords % ecTotalBlocks;\n  const blocksInGroup1 = ecTotalBlocks - blocksInGroup2;\n  const totalCodewordsInGroup1 = Math.floor(totalCodewords / ecTotalBlocks);\n  const dataCodewordsInGroup1 = Math.floor(dataTotalCodewords / ecTotalBlocks);\n  const dataCodewordsInGroup2 = dataCodewordsInGroup1 + 1;\n\n  // Number of EC codewords is the same for both groups\n  const ecCount = totalCodewordsInGroup1 - dataCodewordsInGroup1;\n\n  // Initialize a Reed-Solomon encoder with a generator polynomial of degree ecCount\n  const rs = new ReedSolomonEncoder(ecCount);\n  let offset = 0;\n  const dcData = new Array(ecTotalBlocks);\n  const ecData = new Array(ecTotalBlocks);\n  let maxDataSize = 0;\n  const buffer = new Uint8Array(bitBuffer.buffer);\n\n  // Divide the buffer into the required number of blocks\n  for (let b = 0; b < ecTotalBlocks; b++) {\n    const dataSize = b < blocksInGroup1 ? dataCodewordsInGroup1 : dataCodewordsInGroup2;\n\n    // extract a block of data from buffer\n    dcData[b] = buffer.slice(offset, offset + dataSize);\n\n    // Calculate EC codewords for this data block\n    ecData[b] = rs.encode(dcData[b]);\n    offset += dataSize;\n    maxDataSize = Math.max(maxDataSize, dataSize);\n  }\n\n  // Create final data\n  // Interleave the data and error correction codewords from each block\n  const data = new Uint8Array(totalCodewords);\n  let index = 0;\n  let i, r;\n\n  // Add data codewords\n  for (i = 0; i < maxDataSize; i++) {\n    for (r = 0; r < ecTotalBlocks; r++) {\n      if (i < dcData[r].length) {\n        data[index++] = dcData[r][i];\n      }\n    }\n  }\n\n  // Apped EC codewords\n  for (i = 0; i < ecCount; i++) {\n    for (r = 0; r < ecTotalBlocks; r++) {\n      data[index++] = ecData[r][i];\n    }\n  }\n  return data;\n}\n\n/**\n * Build QR Code symbol\n *\n * @param  {String} data                 Input string\n * @param  {Number} version              QR Code version\n * @param  {ErrorCorretionLevel} errorCorrectionLevel Error level\n * @param  {MaskPattern} maskPattern     Mask pattern\n * @return {Object}                      Object containing symbol data\n */\nfunction createSymbol(data, version, errorCorrectionLevel, maskPattern) {\n  let segments;\n  if (Array.isArray(data)) {\n    segments = Segments.fromArray(data);\n  } else if (typeof data === 'string') {\n    let estimatedVersion = version;\n    if (!estimatedVersion) {\n      const rawSegments = Segments.rawSplit(data);\n\n      // Estimate best version that can contain raw splitted segments\n      estimatedVersion = Version.getBestVersionForData(rawSegments, errorCorrectionLevel);\n    }\n\n    // Build optimized segments\n    // If estimated version is undefined, try with the highest version\n    segments = Segments.fromString(data, estimatedVersion || 40);\n  } else {\n    throw new Error('Invalid data');\n  }\n\n  // Get the min version that can contain data\n  const bestVersion = Version.getBestVersionForData(segments, errorCorrectionLevel);\n\n  // If no version is found, data cannot be stored\n  if (!bestVersion) {\n    throw new Error('The amount of data is too big to be stored in a QR Code');\n  }\n\n  // If not specified, use min version as default\n  if (!version) {\n    version = bestVersion;\n\n    // Check if the specified version can contain the data\n  } else if (version < bestVersion) {\n    throw new Error('\\n' + 'The chosen QR Code version cannot contain this amount of data.\\n' + 'Minimum version required to store current data is: ' + bestVersion + '.\\n');\n  }\n  const dataBits = createData(version, errorCorrectionLevel, segments);\n\n  // Allocate matrix buffer\n  const moduleCount = Utils.getSymbolSize(version);\n  const modules = new BitMatrix(moduleCount);\n\n  // Add function modules\n  setupFinderPattern(modules, version);\n  setupTimingPattern(modules);\n  setupAlignmentPattern(modules, version);\n\n  // Add temporary dummy bits for format info just to set them as reserved.\n  // This is needed to prevent these bits from being masked by {@link MaskPattern.applyMask}\n  // since the masking operation must be performed only on the encoding region.\n  // These blocks will be replaced with correct values later in code.\n  setupFormatInfo(modules, errorCorrectionLevel, 0);\n  if (version >= 7) {\n    setupVersionInfo(modules, version);\n  }\n\n  // Add data codewords\n  setupData(modules, dataBits);\n  if (isNaN(maskPattern)) {\n    // Find best mask pattern\n    maskPattern = MaskPattern.getBestMask(modules, setupFormatInfo.bind(null, modules, errorCorrectionLevel));\n  }\n\n  // Apply mask pattern\n  MaskPattern.applyMask(maskPattern, modules);\n\n  // Replace format info bits with correct values\n  setupFormatInfo(modules, errorCorrectionLevel, maskPattern);\n  return {\n    modules: modules,\n    version: version,\n    errorCorrectionLevel: errorCorrectionLevel,\n    maskPattern: maskPattern,\n    segments: segments\n  };\n}\n\n/**\n * QR Code\n *\n * @param {String | Array} data                 Input data\n * @param {Object} options                      Optional configurations\n * @param {Number} options.version              QR Code version\n * @param {String} options.errorCorrectionLevel Error correction level\n * @param {Function} options.toSJISFunc         Helper func to convert utf8 to sjis\n */\nexports.create = function create(data, options) {\n  if (typeof data === 'undefined' || data === '') {\n    throw new Error('No input text');\n  }\n  let errorCorrectionLevel = ECLevel.M;\n  let version;\n  let mask;\n  if (typeof options !== 'undefined') {\n    // Use higher error correction level as default\n    errorCorrectionLevel = ECLevel.from(options.errorCorrectionLevel, ECLevel.M);\n    version = Version.from(options.version);\n    mask = MaskPattern.from(options.maskPattern);\n    if (options.toSJISFunc) {\n      Utils.setToSJISFunction(options.toSJISFunc);\n    }\n  }\n  return createSymbol(data, version, errorCorrectionLevel, mask);\n};", "map": {"version": 3, "names": ["Utils", "require", "ECLevel", "BitBuffer", "BitMatrix", "AlignmentPattern", "FinderPattern", "MaskPattern", "ECCode", "ReedSolomonEncoder", "Version", "FormatInfo", "Mode", "Segments", "setupFinderPattern", "matrix", "version", "size", "pos", "getPositions", "i", "length", "row", "col", "r", "c", "set", "setupTimingPattern", "value", "setupAlignmentPattern", "setupVersionInfo", "bits", "getEncodedBits", "mod", "Math", "floor", "setupFormatInfo", "errorCorrectionLevel", "maskPattern", "setupData", "data", "inc", "bitIndex", "byteIndex", "isReserved", "dark", "createData", "segments", "buffer", "for<PERSON>ach", "put", "mode", "bit", "<PERSON><PERSON><PERSON><PERSON>", "getCharCountIndicator", "write", "totalCodewords", "getSymbolTotalCodewords", "ecTotalCodewords", "getTotalCodewordsCount", "dataTotalCodewordsBits", "getLengthInBits", "putBit", "remainingByte", "createCodewords", "bitBuffer", "dataTotalCodewords", "ecTotalBlocks", "getBlocksCount", "blocksInGroup2", "blocksInGroup1", "totalCodewordsInGroup1", "dataCodewordsInGroup1", "dataCodewordsInGroup2", "ecCount", "rs", "offset", "dcData", "Array", "ecData", "maxDataSize", "Uint8Array", "b", "dataSize", "slice", "encode", "max", "index", "createSymbol", "isArray", "fromArray", "estimatedVersion", "rawSegments", "rawSplit", "getBestVersionForData", "fromString", "Error", "bestVersion", "dataBits", "moduleCount", "getSymbolSize", "modules", "isNaN", "getBestMask", "bind", "applyMask", "exports", "create", "options", "M", "mask", "from", "toSJISFunc", "setToSJISFunction"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/test 3/medical-assistant-react/node_modules/qrcode/lib/core/qrcode.js"], "sourcesContent": ["const Utils = require('./utils')\nconst ECLevel = require('./error-correction-level')\nconst BitBuffer = require('./bit-buffer')\nconst BitMatrix = require('./bit-matrix')\nconst AlignmentPattern = require('./alignment-pattern')\nconst FinderPattern = require('./finder-pattern')\nconst MaskPattern = require('./mask-pattern')\nconst ECCode = require('./error-correction-code')\nconst ReedSolomonEncoder = require('./reed-solomon-encoder')\nconst Version = require('./version')\nconst FormatInfo = require('./format-info')\nconst Mode = require('./mode')\nconst Segments = require('./segments')\n\n/**\n * QRCode for JavaScript\n *\n * modified by <PERSON> for nodejs support\n * Copyright (c) 2011 Ryan Day\n *\n * Licensed under the MIT license:\n *   http://www.opensource.org/licenses/mit-license.php\n *\n//---------------------------------------------------------------------\n// QRCode for JavaScript\n//\n// Copyright (c) 2009 <PERSON><PERSON><PERSON>\n//\n// URL: http://www.d-project.com/\n//\n// Licensed under the MIT license:\n//   http://www.opensource.org/licenses/mit-license.php\n//\n// The word \"QR Code\" is registered trademark of\n// DENSO WAVE INCORPORATED\n//   http://www.denso-wave.com/qrcode/faqpatent-e.html\n//\n//---------------------------------------------------------------------\n*/\n\n/**\n * Add finder patterns bits to matrix\n *\n * @param  {BitMatrix} matrix  Modules matrix\n * @param  {Number}    version QR Code version\n */\nfunction setupFinderPattern (matrix, version) {\n  const size = matrix.size\n  const pos = FinderPattern.getPositions(version)\n\n  for (let i = 0; i < pos.length; i++) {\n    const row = pos[i][0]\n    const col = pos[i][1]\n\n    for (let r = -1; r <= 7; r++) {\n      if (row + r <= -1 || size <= row + r) continue\n\n      for (let c = -1; c <= 7; c++) {\n        if (col + c <= -1 || size <= col + c) continue\n\n        if ((r >= 0 && r <= 6 && (c === 0 || c === 6)) ||\n          (c >= 0 && c <= 6 && (r === 0 || r === 6)) ||\n          (r >= 2 && r <= 4 && c >= 2 && c <= 4)) {\n          matrix.set(row + r, col + c, true, true)\n        } else {\n          matrix.set(row + r, col + c, false, true)\n        }\n      }\n    }\n  }\n}\n\n/**\n * Add timing pattern bits to matrix\n *\n * Note: this function must be called before {@link setupAlignmentPattern}\n *\n * @param  {BitMatrix} matrix Modules matrix\n */\nfunction setupTimingPattern (matrix) {\n  const size = matrix.size\n\n  for (let r = 8; r < size - 8; r++) {\n    const value = r % 2 === 0\n    matrix.set(r, 6, value, true)\n    matrix.set(6, r, value, true)\n  }\n}\n\n/**\n * Add alignment patterns bits to matrix\n *\n * Note: this function must be called after {@link setupTimingPattern}\n *\n * @param  {BitMatrix} matrix  Modules matrix\n * @param  {Number}    version QR Code version\n */\nfunction setupAlignmentPattern (matrix, version) {\n  const pos = AlignmentPattern.getPositions(version)\n\n  for (let i = 0; i < pos.length; i++) {\n    const row = pos[i][0]\n    const col = pos[i][1]\n\n    for (let r = -2; r <= 2; r++) {\n      for (let c = -2; c <= 2; c++) {\n        if (r === -2 || r === 2 || c === -2 || c === 2 ||\n          (r === 0 && c === 0)) {\n          matrix.set(row + r, col + c, true, true)\n        } else {\n          matrix.set(row + r, col + c, false, true)\n        }\n      }\n    }\n  }\n}\n\n/**\n * Add version info bits to matrix\n *\n * @param  {BitMatrix} matrix  Modules matrix\n * @param  {Number}    version QR Code version\n */\nfunction setupVersionInfo (matrix, version) {\n  const size = matrix.size\n  const bits = Version.getEncodedBits(version)\n  let row, col, mod\n\n  for (let i = 0; i < 18; i++) {\n    row = Math.floor(i / 3)\n    col = i % 3 + size - 8 - 3\n    mod = ((bits >> i) & 1) === 1\n\n    matrix.set(row, col, mod, true)\n    matrix.set(col, row, mod, true)\n  }\n}\n\n/**\n * Add format info bits to matrix\n *\n * @param  {BitMatrix} matrix               Modules matrix\n * @param  {ErrorCorrectionLevel}    errorCorrectionLevel Error correction level\n * @param  {Number}    maskPattern          Mask pattern reference value\n */\nfunction setupFormatInfo (matrix, errorCorrectionLevel, maskPattern) {\n  const size = matrix.size\n  const bits = FormatInfo.getEncodedBits(errorCorrectionLevel, maskPattern)\n  let i, mod\n\n  for (i = 0; i < 15; i++) {\n    mod = ((bits >> i) & 1) === 1\n\n    // vertical\n    if (i < 6) {\n      matrix.set(i, 8, mod, true)\n    } else if (i < 8) {\n      matrix.set(i + 1, 8, mod, true)\n    } else {\n      matrix.set(size - 15 + i, 8, mod, true)\n    }\n\n    // horizontal\n    if (i < 8) {\n      matrix.set(8, size - i - 1, mod, true)\n    } else if (i < 9) {\n      matrix.set(8, 15 - i - 1 + 1, mod, true)\n    } else {\n      matrix.set(8, 15 - i - 1, mod, true)\n    }\n  }\n\n  // fixed module\n  matrix.set(size - 8, 8, 1, true)\n}\n\n/**\n * Add encoded data bits to matrix\n *\n * @param  {BitMatrix}  matrix Modules matrix\n * @param  {Uint8Array} data   Data codewords\n */\nfunction setupData (matrix, data) {\n  const size = matrix.size\n  let inc = -1\n  let row = size - 1\n  let bitIndex = 7\n  let byteIndex = 0\n\n  for (let col = size - 1; col > 0; col -= 2) {\n    if (col === 6) col--\n\n    while (true) {\n      for (let c = 0; c < 2; c++) {\n        if (!matrix.isReserved(row, col - c)) {\n          let dark = false\n\n          if (byteIndex < data.length) {\n            dark = (((data[byteIndex] >>> bitIndex) & 1) === 1)\n          }\n\n          matrix.set(row, col - c, dark)\n          bitIndex--\n\n          if (bitIndex === -1) {\n            byteIndex++\n            bitIndex = 7\n          }\n        }\n      }\n\n      row += inc\n\n      if (row < 0 || size <= row) {\n        row -= inc\n        inc = -inc\n        break\n      }\n    }\n  }\n}\n\n/**\n * Create encoded codewords from data input\n *\n * @param  {Number}   version              QR Code version\n * @param  {ErrorCorrectionLevel}   errorCorrectionLevel Error correction level\n * @param  {ByteData} data                 Data input\n * @return {Uint8Array}                    Buffer containing encoded codewords\n */\nfunction createData (version, errorCorrectionLevel, segments) {\n  // Prepare data buffer\n  const buffer = new BitBuffer()\n\n  segments.forEach(function (data) {\n    // prefix data with mode indicator (4 bits)\n    buffer.put(data.mode.bit, 4)\n\n    // Prefix data with character count indicator.\n    // The character count indicator is a string of bits that represents the\n    // number of characters that are being encoded.\n    // The character count indicator must be placed after the mode indicator\n    // and must be a certain number of bits long, depending on the QR version\n    // and data mode\n    // @see {@link Mode.getCharCountIndicator}.\n    buffer.put(data.getLength(), Mode.getCharCountIndicator(data.mode, version))\n\n    // add binary data sequence to buffer\n    data.write(buffer)\n  })\n\n  // Calculate required number of bits\n  const totalCodewords = Utils.getSymbolTotalCodewords(version)\n  const ecTotalCodewords = ECCode.getTotalCodewordsCount(version, errorCorrectionLevel)\n  const dataTotalCodewordsBits = (totalCodewords - ecTotalCodewords) * 8\n\n  // Add a terminator.\n  // If the bit string is shorter than the total number of required bits,\n  // a terminator of up to four 0s must be added to the right side of the string.\n  // If the bit string is more than four bits shorter than the required number of bits,\n  // add four 0s to the end.\n  if (buffer.getLengthInBits() + 4 <= dataTotalCodewordsBits) {\n    buffer.put(0, 4)\n  }\n\n  // If the bit string is fewer than four bits shorter, add only the number of 0s that\n  // are needed to reach the required number of bits.\n\n  // After adding the terminator, if the number of bits in the string is not a multiple of 8,\n  // pad the string on the right with 0s to make the string's length a multiple of 8.\n  while (buffer.getLengthInBits() % 8 !== 0) {\n    buffer.putBit(0)\n  }\n\n  // Add pad bytes if the string is still shorter than the total number of required bits.\n  // Extend the buffer to fill the data capacity of the symbol corresponding to\n  // the Version and Error Correction Level by adding the Pad Codewords 11101100 (0xEC)\n  // and 00010001 (0x11) alternately.\n  const remainingByte = (dataTotalCodewordsBits - buffer.getLengthInBits()) / 8\n  for (let i = 0; i < remainingByte; i++) {\n    buffer.put(i % 2 ? 0x11 : 0xEC, 8)\n  }\n\n  return createCodewords(buffer, version, errorCorrectionLevel)\n}\n\n/**\n * Encode input data with Reed-Solomon and return codewords with\n * relative error correction bits\n *\n * @param  {BitBuffer} bitBuffer            Data to encode\n * @param  {Number}    version              QR Code version\n * @param  {ErrorCorrectionLevel} errorCorrectionLevel Error correction level\n * @return {Uint8Array}                     Buffer containing encoded codewords\n */\nfunction createCodewords (bitBuffer, version, errorCorrectionLevel) {\n  // Total codewords for this QR code version (Data + Error correction)\n  const totalCodewords = Utils.getSymbolTotalCodewords(version)\n\n  // Total number of error correction codewords\n  const ecTotalCodewords = ECCode.getTotalCodewordsCount(version, errorCorrectionLevel)\n\n  // Total number of data codewords\n  const dataTotalCodewords = totalCodewords - ecTotalCodewords\n\n  // Total number of blocks\n  const ecTotalBlocks = ECCode.getBlocksCount(version, errorCorrectionLevel)\n\n  // Calculate how many blocks each group should contain\n  const blocksInGroup2 = totalCodewords % ecTotalBlocks\n  const blocksInGroup1 = ecTotalBlocks - blocksInGroup2\n\n  const totalCodewordsInGroup1 = Math.floor(totalCodewords / ecTotalBlocks)\n\n  const dataCodewordsInGroup1 = Math.floor(dataTotalCodewords / ecTotalBlocks)\n  const dataCodewordsInGroup2 = dataCodewordsInGroup1 + 1\n\n  // Number of EC codewords is the same for both groups\n  const ecCount = totalCodewordsInGroup1 - dataCodewordsInGroup1\n\n  // Initialize a Reed-Solomon encoder with a generator polynomial of degree ecCount\n  const rs = new ReedSolomonEncoder(ecCount)\n\n  let offset = 0\n  const dcData = new Array(ecTotalBlocks)\n  const ecData = new Array(ecTotalBlocks)\n  let maxDataSize = 0\n  const buffer = new Uint8Array(bitBuffer.buffer)\n\n  // Divide the buffer into the required number of blocks\n  for (let b = 0; b < ecTotalBlocks; b++) {\n    const dataSize = b < blocksInGroup1 ? dataCodewordsInGroup1 : dataCodewordsInGroup2\n\n    // extract a block of data from buffer\n    dcData[b] = buffer.slice(offset, offset + dataSize)\n\n    // Calculate EC codewords for this data block\n    ecData[b] = rs.encode(dcData[b])\n\n    offset += dataSize\n    maxDataSize = Math.max(maxDataSize, dataSize)\n  }\n\n  // Create final data\n  // Interleave the data and error correction codewords from each block\n  const data = new Uint8Array(totalCodewords)\n  let index = 0\n  let i, r\n\n  // Add data codewords\n  for (i = 0; i < maxDataSize; i++) {\n    for (r = 0; r < ecTotalBlocks; r++) {\n      if (i < dcData[r].length) {\n        data[index++] = dcData[r][i]\n      }\n    }\n  }\n\n  // Apped EC codewords\n  for (i = 0; i < ecCount; i++) {\n    for (r = 0; r < ecTotalBlocks; r++) {\n      data[index++] = ecData[r][i]\n    }\n  }\n\n  return data\n}\n\n/**\n * Build QR Code symbol\n *\n * @param  {String} data                 Input string\n * @param  {Number} version              QR Code version\n * @param  {ErrorCorretionLevel} errorCorrectionLevel Error level\n * @param  {MaskPattern} maskPattern     Mask pattern\n * @return {Object}                      Object containing symbol data\n */\nfunction createSymbol (data, version, errorCorrectionLevel, maskPattern) {\n  let segments\n\n  if (Array.isArray(data)) {\n    segments = Segments.fromArray(data)\n  } else if (typeof data === 'string') {\n    let estimatedVersion = version\n\n    if (!estimatedVersion) {\n      const rawSegments = Segments.rawSplit(data)\n\n      // Estimate best version that can contain raw splitted segments\n      estimatedVersion = Version.getBestVersionForData(rawSegments, errorCorrectionLevel)\n    }\n\n    // Build optimized segments\n    // If estimated version is undefined, try with the highest version\n    segments = Segments.fromString(data, estimatedVersion || 40)\n  } else {\n    throw new Error('Invalid data')\n  }\n\n  // Get the min version that can contain data\n  const bestVersion = Version.getBestVersionForData(segments, errorCorrectionLevel)\n\n  // If no version is found, data cannot be stored\n  if (!bestVersion) {\n    throw new Error('The amount of data is too big to be stored in a QR Code')\n  }\n\n  // If not specified, use min version as default\n  if (!version) {\n    version = bestVersion\n\n  // Check if the specified version can contain the data\n  } else if (version < bestVersion) {\n    throw new Error('\\n' +\n      'The chosen QR Code version cannot contain this amount of data.\\n' +\n      'Minimum version required to store current data is: ' + bestVersion + '.\\n'\n    )\n  }\n\n  const dataBits = createData(version, errorCorrectionLevel, segments)\n\n  // Allocate matrix buffer\n  const moduleCount = Utils.getSymbolSize(version)\n  const modules = new BitMatrix(moduleCount)\n\n  // Add function modules\n  setupFinderPattern(modules, version)\n  setupTimingPattern(modules)\n  setupAlignmentPattern(modules, version)\n\n  // Add temporary dummy bits for format info just to set them as reserved.\n  // This is needed to prevent these bits from being masked by {@link MaskPattern.applyMask}\n  // since the masking operation must be performed only on the encoding region.\n  // These blocks will be replaced with correct values later in code.\n  setupFormatInfo(modules, errorCorrectionLevel, 0)\n\n  if (version >= 7) {\n    setupVersionInfo(modules, version)\n  }\n\n  // Add data codewords\n  setupData(modules, dataBits)\n\n  if (isNaN(maskPattern)) {\n    // Find best mask pattern\n    maskPattern = MaskPattern.getBestMask(modules,\n      setupFormatInfo.bind(null, modules, errorCorrectionLevel))\n  }\n\n  // Apply mask pattern\n  MaskPattern.applyMask(maskPattern, modules)\n\n  // Replace format info bits with correct values\n  setupFormatInfo(modules, errorCorrectionLevel, maskPattern)\n\n  return {\n    modules: modules,\n    version: version,\n    errorCorrectionLevel: errorCorrectionLevel,\n    maskPattern: maskPattern,\n    segments: segments\n  }\n}\n\n/**\n * QR Code\n *\n * @param {String | Array} data                 Input data\n * @param {Object} options                      Optional configurations\n * @param {Number} options.version              QR Code version\n * @param {String} options.errorCorrectionLevel Error correction level\n * @param {Function} options.toSJISFunc         Helper func to convert utf8 to sjis\n */\nexports.create = function create (data, options) {\n  if (typeof data === 'undefined' || data === '') {\n    throw new Error('No input text')\n  }\n\n  let errorCorrectionLevel = ECLevel.M\n  let version\n  let mask\n\n  if (typeof options !== 'undefined') {\n    // Use higher error correction level as default\n    errorCorrectionLevel = ECLevel.from(options.errorCorrectionLevel, ECLevel.M)\n    version = Version.from(options.version)\n    mask = MaskPattern.from(options.maskPattern)\n\n    if (options.toSJISFunc) {\n      Utils.setToSJISFunction(options.toSJISFunc)\n    }\n  }\n\n  return createSymbol(data, version, errorCorrectionLevel, mask)\n}\n"], "mappings": "AAAA,MAAMA,KAAK,GAAGC,OAAO,CAAC,SAAS,CAAC;AAChC,MAAMC,OAAO,GAAGD,OAAO,CAAC,0BAA0B,CAAC;AACnD,MAAME,SAAS,GAAGF,OAAO,CAAC,cAAc,CAAC;AACzC,MAAMG,SAAS,GAAGH,OAAO,CAAC,cAAc,CAAC;AACzC,MAAMI,gBAAgB,GAAGJ,OAAO,CAAC,qBAAqB,CAAC;AACvD,MAAMK,aAAa,GAAGL,OAAO,CAAC,kBAAkB,CAAC;AACjD,MAAMM,WAAW,GAAGN,OAAO,CAAC,gBAAgB,CAAC;AAC7C,MAAMO,MAAM,GAAGP,OAAO,CAAC,yBAAyB,CAAC;AACjD,MAAMQ,kBAAkB,GAAGR,OAAO,CAAC,wBAAwB,CAAC;AAC5D,MAAMS,OAAO,GAAGT,OAAO,CAAC,WAAW,CAAC;AACpC,MAAMU,UAAU,GAAGV,OAAO,CAAC,eAAe,CAAC;AAC3C,MAAMW,IAAI,GAAGX,OAAO,CAAC,QAAQ,CAAC;AAC9B,MAAMY,QAAQ,GAAGZ,OAAO,CAAC,YAAY,CAAC;;AAEtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASa,kBAAkBA,CAAEC,MAAM,EAAEC,OAAO,EAAE;EAC5C,MAAMC,IAAI,GAAGF,MAAM,CAACE,IAAI;EACxB,MAAMC,GAAG,GAAGZ,aAAa,CAACa,YAAY,CAACH,OAAO,CAAC;EAE/C,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,GAAG,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;IACnC,MAAME,GAAG,GAAGJ,GAAG,CAACE,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB,MAAMG,GAAG,GAAGL,GAAG,CAACE,CAAC,CAAC,CAAC,CAAC,CAAC;IAErB,KAAK,IAAII,CAAC,GAAG,CAAC,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC5B,IAAIF,GAAG,GAAGE,CAAC,IAAI,CAAC,CAAC,IAAIP,IAAI,IAAIK,GAAG,GAAGE,CAAC,EAAE;MAEtC,KAAK,IAAIC,CAAC,GAAG,CAAC,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;QAC5B,IAAIF,GAAG,GAAGE,CAAC,IAAI,CAAC,CAAC,IAAIR,IAAI,IAAIM,GAAG,GAAGE,CAAC,EAAE;QAEtC,IAAKD,CAAC,IAAI,CAAC,IAAIA,CAAC,IAAI,CAAC,KAAKC,CAAC,KAAK,CAAC,IAAIA,CAAC,KAAK,CAAC,CAAC,IAC1CA,CAAC,IAAI,CAAC,IAAIA,CAAC,IAAI,CAAC,KAAKD,CAAC,KAAK,CAAC,IAAIA,CAAC,KAAK,CAAC,CAAE,IACzCA,CAAC,IAAI,CAAC,IAAIA,CAAC,IAAI,CAAC,IAAIC,CAAC,IAAI,CAAC,IAAIA,CAAC,IAAI,CAAE,EAAE;UACxCV,MAAM,CAACW,GAAG,CAACJ,GAAG,GAAGE,CAAC,EAAED,GAAG,GAAGE,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC;QAC1C,CAAC,MAAM;UACLV,MAAM,CAACW,GAAG,CAACJ,GAAG,GAAGE,CAAC,EAAED,GAAG,GAAGE,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC;QAC3C;MACF;IACF;EACF;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,kBAAkBA,CAAEZ,MAAM,EAAE;EACnC,MAAME,IAAI,GAAGF,MAAM,CAACE,IAAI;EAExB,KAAK,IAAIO,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,IAAI,GAAG,CAAC,EAAEO,CAAC,EAAE,EAAE;IACjC,MAAMI,KAAK,GAAGJ,CAAC,GAAG,CAAC,KAAK,CAAC;IACzBT,MAAM,CAACW,GAAG,CAACF,CAAC,EAAE,CAAC,EAAEI,KAAK,EAAE,IAAI,CAAC;IAC7Bb,MAAM,CAACW,GAAG,CAAC,CAAC,EAAEF,CAAC,EAAEI,KAAK,EAAE,IAAI,CAAC;EAC/B;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,qBAAqBA,CAAEd,MAAM,EAAEC,OAAO,EAAE;EAC/C,MAAME,GAAG,GAAGb,gBAAgB,CAACc,YAAY,CAACH,OAAO,CAAC;EAElD,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,GAAG,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;IACnC,MAAME,GAAG,GAAGJ,GAAG,CAACE,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB,MAAMG,GAAG,GAAGL,GAAG,CAACE,CAAC,CAAC,CAAC,CAAC,CAAC;IAErB,KAAK,IAAII,CAAC,GAAG,CAAC,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC5B,KAAK,IAAIC,CAAC,GAAG,CAAC,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;QAC5B,IAAID,CAAC,KAAK,CAAC,CAAC,IAAIA,CAAC,KAAK,CAAC,IAAIC,CAAC,KAAK,CAAC,CAAC,IAAIA,CAAC,KAAK,CAAC,IAC3CD,CAAC,KAAK,CAAC,IAAIC,CAAC,KAAK,CAAE,EAAE;UACtBV,MAAM,CAACW,GAAG,CAACJ,GAAG,GAAGE,CAAC,EAAED,GAAG,GAAGE,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC;QAC1C,CAAC,MAAM;UACLV,MAAM,CAACW,GAAG,CAACJ,GAAG,GAAGE,CAAC,EAAED,GAAG,GAAGE,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC;QAC3C;MACF;IACF;EACF;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASK,gBAAgBA,CAAEf,MAAM,EAAEC,OAAO,EAAE;EAC1C,MAAMC,IAAI,GAAGF,MAAM,CAACE,IAAI;EACxB,MAAMc,IAAI,GAAGrB,OAAO,CAACsB,cAAc,CAAChB,OAAO,CAAC;EAC5C,IAAIM,GAAG,EAAEC,GAAG,EAAEU,GAAG;EAEjB,KAAK,IAAIb,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;IAC3BE,GAAG,GAAGY,IAAI,CAACC,KAAK,CAACf,CAAC,GAAG,CAAC,CAAC;IACvBG,GAAG,GAAGH,CAAC,GAAG,CAAC,GAAGH,IAAI,GAAG,CAAC,GAAG,CAAC;IAC1BgB,GAAG,GAAG,CAAEF,IAAI,IAAIX,CAAC,GAAI,CAAC,MAAM,CAAC;IAE7BL,MAAM,CAACW,GAAG,CAACJ,GAAG,EAAEC,GAAG,EAAEU,GAAG,EAAE,IAAI,CAAC;IAC/BlB,MAAM,CAACW,GAAG,CAACH,GAAG,EAAED,GAAG,EAAEW,GAAG,EAAE,IAAI,CAAC;EACjC;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,eAAeA,CAAErB,MAAM,EAAEsB,oBAAoB,EAAEC,WAAW,EAAE;EACnE,MAAMrB,IAAI,GAAGF,MAAM,CAACE,IAAI;EACxB,MAAMc,IAAI,GAAGpB,UAAU,CAACqB,cAAc,CAACK,oBAAoB,EAAEC,WAAW,CAAC;EACzE,IAAIlB,CAAC,EAAEa,GAAG;EAEV,KAAKb,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;IACvBa,GAAG,GAAG,CAAEF,IAAI,IAAIX,CAAC,GAAI,CAAC,MAAM,CAAC;;IAE7B;IACA,IAAIA,CAAC,GAAG,CAAC,EAAE;MACTL,MAAM,CAACW,GAAG,CAACN,CAAC,EAAE,CAAC,EAAEa,GAAG,EAAE,IAAI,CAAC;IAC7B,CAAC,MAAM,IAAIb,CAAC,GAAG,CAAC,EAAE;MAChBL,MAAM,CAACW,GAAG,CAACN,CAAC,GAAG,CAAC,EAAE,CAAC,EAAEa,GAAG,EAAE,IAAI,CAAC;IACjC,CAAC,MAAM;MACLlB,MAAM,CAACW,GAAG,CAACT,IAAI,GAAG,EAAE,GAAGG,CAAC,EAAE,CAAC,EAAEa,GAAG,EAAE,IAAI,CAAC;IACzC;;IAEA;IACA,IAAIb,CAAC,GAAG,CAAC,EAAE;MACTL,MAAM,CAACW,GAAG,CAAC,CAAC,EAAET,IAAI,GAAGG,CAAC,GAAG,CAAC,EAAEa,GAAG,EAAE,IAAI,CAAC;IACxC,CAAC,MAAM,IAAIb,CAAC,GAAG,CAAC,EAAE;MAChBL,MAAM,CAACW,GAAG,CAAC,CAAC,EAAE,EAAE,GAAGN,CAAC,GAAG,CAAC,GAAG,CAAC,EAAEa,GAAG,EAAE,IAAI,CAAC;IAC1C,CAAC,MAAM;MACLlB,MAAM,CAACW,GAAG,CAAC,CAAC,EAAE,EAAE,GAAGN,CAAC,GAAG,CAAC,EAAEa,GAAG,EAAE,IAAI,CAAC;IACtC;EACF;;EAEA;EACAlB,MAAM,CAACW,GAAG,CAACT,IAAI,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;AAClC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASsB,SAASA,CAAExB,MAAM,EAAEyB,IAAI,EAAE;EAChC,MAAMvB,IAAI,GAAGF,MAAM,CAACE,IAAI;EACxB,IAAIwB,GAAG,GAAG,CAAC,CAAC;EACZ,IAAInB,GAAG,GAAGL,IAAI,GAAG,CAAC;EAClB,IAAIyB,QAAQ,GAAG,CAAC;EAChB,IAAIC,SAAS,GAAG,CAAC;EAEjB,KAAK,IAAIpB,GAAG,GAAGN,IAAI,GAAG,CAAC,EAAEM,GAAG,GAAG,CAAC,EAAEA,GAAG,IAAI,CAAC,EAAE;IAC1C,IAAIA,GAAG,KAAK,CAAC,EAAEA,GAAG,EAAE;IAEpB,OAAO,IAAI,EAAE;MACX,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;QAC1B,IAAI,CAACV,MAAM,CAAC6B,UAAU,CAACtB,GAAG,EAAEC,GAAG,GAAGE,CAAC,CAAC,EAAE;UACpC,IAAIoB,IAAI,GAAG,KAAK;UAEhB,IAAIF,SAAS,GAAGH,IAAI,CAACnB,MAAM,EAAE;YAC3BwB,IAAI,GAAI,CAAEL,IAAI,CAACG,SAAS,CAAC,KAAKD,QAAQ,GAAI,CAAC,MAAM,CAAE;UACrD;UAEA3B,MAAM,CAACW,GAAG,CAACJ,GAAG,EAAEC,GAAG,GAAGE,CAAC,EAAEoB,IAAI,CAAC;UAC9BH,QAAQ,EAAE;UAEV,IAAIA,QAAQ,KAAK,CAAC,CAAC,EAAE;YACnBC,SAAS,EAAE;YACXD,QAAQ,GAAG,CAAC;UACd;QACF;MACF;MAEApB,GAAG,IAAImB,GAAG;MAEV,IAAInB,GAAG,GAAG,CAAC,IAAIL,IAAI,IAAIK,GAAG,EAAE;QAC1BA,GAAG,IAAImB,GAAG;QACVA,GAAG,GAAG,CAACA,GAAG;QACV;MACF;IACF;EACF;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASK,UAAUA,CAAE9B,OAAO,EAAEqB,oBAAoB,EAAEU,QAAQ,EAAE;EAC5D;EACA,MAAMC,MAAM,GAAG,IAAI7C,SAAS,CAAC,CAAC;EAE9B4C,QAAQ,CAACE,OAAO,CAAC,UAAUT,IAAI,EAAE;IAC/B;IACAQ,MAAM,CAACE,GAAG,CAACV,IAAI,CAACW,IAAI,CAACC,GAAG,EAAE,CAAC,CAAC;;IAE5B;IACA;IACA;IACA;IACA;IACA;IACA;IACAJ,MAAM,CAACE,GAAG,CAACV,IAAI,CAACa,SAAS,CAAC,CAAC,EAAEzC,IAAI,CAAC0C,qBAAqB,CAACd,IAAI,CAACW,IAAI,EAAEnC,OAAO,CAAC,CAAC;;IAE5E;IACAwB,IAAI,CAACe,KAAK,CAACP,MAAM,CAAC;EACpB,CAAC,CAAC;;EAEF;EACA,MAAMQ,cAAc,GAAGxD,KAAK,CAACyD,uBAAuB,CAACzC,OAAO,CAAC;EAC7D,MAAM0C,gBAAgB,GAAGlD,MAAM,CAACmD,sBAAsB,CAAC3C,OAAO,EAAEqB,oBAAoB,CAAC;EACrF,MAAMuB,sBAAsB,GAAG,CAACJ,cAAc,GAAGE,gBAAgB,IAAI,CAAC;;EAEtE;EACA;EACA;EACA;EACA;EACA,IAAIV,MAAM,CAACa,eAAe,CAAC,CAAC,GAAG,CAAC,IAAID,sBAAsB,EAAE;IAC1DZ,MAAM,CAACE,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;EAClB;;EAEA;EACA;;EAEA;EACA;EACA,OAAOF,MAAM,CAACa,eAAe,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;IACzCb,MAAM,CAACc,MAAM,CAAC,CAAC,CAAC;EAClB;;EAEA;EACA;EACA;EACA;EACA,MAAMC,aAAa,GAAG,CAACH,sBAAsB,GAAGZ,MAAM,CAACa,eAAe,CAAC,CAAC,IAAI,CAAC;EAC7E,KAAK,IAAIzC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2C,aAAa,EAAE3C,CAAC,EAAE,EAAE;IACtC4B,MAAM,CAACE,GAAG,CAAC9B,CAAC,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,EAAE,CAAC,CAAC;EACpC;EAEA,OAAO4C,eAAe,CAAChB,MAAM,EAAEhC,OAAO,EAAEqB,oBAAoB,CAAC;AAC/D;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS2B,eAAeA,CAAEC,SAAS,EAAEjD,OAAO,EAAEqB,oBAAoB,EAAE;EAClE;EACA,MAAMmB,cAAc,GAAGxD,KAAK,CAACyD,uBAAuB,CAACzC,OAAO,CAAC;;EAE7D;EACA,MAAM0C,gBAAgB,GAAGlD,MAAM,CAACmD,sBAAsB,CAAC3C,OAAO,EAAEqB,oBAAoB,CAAC;;EAErF;EACA,MAAM6B,kBAAkB,GAAGV,cAAc,GAAGE,gBAAgB;;EAE5D;EACA,MAAMS,aAAa,GAAG3D,MAAM,CAAC4D,cAAc,CAACpD,OAAO,EAAEqB,oBAAoB,CAAC;;EAE1E;EACA,MAAMgC,cAAc,GAAGb,cAAc,GAAGW,aAAa;EACrD,MAAMG,cAAc,GAAGH,aAAa,GAAGE,cAAc;EAErD,MAAME,sBAAsB,GAAGrC,IAAI,CAACC,KAAK,CAACqB,cAAc,GAAGW,aAAa,CAAC;EAEzE,MAAMK,qBAAqB,GAAGtC,IAAI,CAACC,KAAK,CAAC+B,kBAAkB,GAAGC,aAAa,CAAC;EAC5E,MAAMM,qBAAqB,GAAGD,qBAAqB,GAAG,CAAC;;EAEvD;EACA,MAAME,OAAO,GAAGH,sBAAsB,GAAGC,qBAAqB;;EAE9D;EACA,MAAMG,EAAE,GAAG,IAAIlE,kBAAkB,CAACiE,OAAO,CAAC;EAE1C,IAAIE,MAAM,GAAG,CAAC;EACd,MAAMC,MAAM,GAAG,IAAIC,KAAK,CAACX,aAAa,CAAC;EACvC,MAAMY,MAAM,GAAG,IAAID,KAAK,CAACX,aAAa,CAAC;EACvC,IAAIa,WAAW,GAAG,CAAC;EACnB,MAAMhC,MAAM,GAAG,IAAIiC,UAAU,CAAChB,SAAS,CAACjB,MAAM,CAAC;;EAE/C;EACA,KAAK,IAAIkC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGf,aAAa,EAAEe,CAAC,EAAE,EAAE;IACtC,MAAMC,QAAQ,GAAGD,CAAC,GAAGZ,cAAc,GAAGE,qBAAqB,GAAGC,qBAAqB;;IAEnF;IACAI,MAAM,CAACK,CAAC,CAAC,GAAGlC,MAAM,CAACoC,KAAK,CAACR,MAAM,EAAEA,MAAM,GAAGO,QAAQ,CAAC;;IAEnD;IACAJ,MAAM,CAACG,CAAC,CAAC,GAAGP,EAAE,CAACU,MAAM,CAACR,MAAM,CAACK,CAAC,CAAC,CAAC;IAEhCN,MAAM,IAAIO,QAAQ;IAClBH,WAAW,GAAG9C,IAAI,CAACoD,GAAG,CAACN,WAAW,EAAEG,QAAQ,CAAC;EAC/C;;EAEA;EACA;EACA,MAAM3C,IAAI,GAAG,IAAIyC,UAAU,CAACzB,cAAc,CAAC;EAC3C,IAAI+B,KAAK,GAAG,CAAC;EACb,IAAInE,CAAC,EAAEI,CAAC;;EAER;EACA,KAAKJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4D,WAAW,EAAE5D,CAAC,EAAE,EAAE;IAChC,KAAKI,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2C,aAAa,EAAE3C,CAAC,EAAE,EAAE;MAClC,IAAIJ,CAAC,GAAGyD,MAAM,CAACrD,CAAC,CAAC,CAACH,MAAM,EAAE;QACxBmB,IAAI,CAAC+C,KAAK,EAAE,CAAC,GAAGV,MAAM,CAACrD,CAAC,CAAC,CAACJ,CAAC,CAAC;MAC9B;IACF;EACF;;EAEA;EACA,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsD,OAAO,EAAEtD,CAAC,EAAE,EAAE;IAC5B,KAAKI,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2C,aAAa,EAAE3C,CAAC,EAAE,EAAE;MAClCgB,IAAI,CAAC+C,KAAK,EAAE,CAAC,GAAGR,MAAM,CAACvD,CAAC,CAAC,CAACJ,CAAC,CAAC;IAC9B;EACF;EAEA,OAAOoB,IAAI;AACb;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASgD,YAAYA,CAAEhD,IAAI,EAAExB,OAAO,EAAEqB,oBAAoB,EAAEC,WAAW,EAAE;EACvE,IAAIS,QAAQ;EAEZ,IAAI+B,KAAK,CAACW,OAAO,CAACjD,IAAI,CAAC,EAAE;IACvBO,QAAQ,GAAGlC,QAAQ,CAAC6E,SAAS,CAAClD,IAAI,CAAC;EACrC,CAAC,MAAM,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;IACnC,IAAImD,gBAAgB,GAAG3E,OAAO;IAE9B,IAAI,CAAC2E,gBAAgB,EAAE;MACrB,MAAMC,WAAW,GAAG/E,QAAQ,CAACgF,QAAQ,CAACrD,IAAI,CAAC;;MAE3C;MACAmD,gBAAgB,GAAGjF,OAAO,CAACoF,qBAAqB,CAACF,WAAW,EAAEvD,oBAAoB,CAAC;IACrF;;IAEA;IACA;IACAU,QAAQ,GAAGlC,QAAQ,CAACkF,UAAU,CAACvD,IAAI,EAAEmD,gBAAgB,IAAI,EAAE,CAAC;EAC9D,CAAC,MAAM;IACL,MAAM,IAAIK,KAAK,CAAC,cAAc,CAAC;EACjC;;EAEA;EACA,MAAMC,WAAW,GAAGvF,OAAO,CAACoF,qBAAqB,CAAC/C,QAAQ,EAAEV,oBAAoB,CAAC;;EAEjF;EACA,IAAI,CAAC4D,WAAW,EAAE;IAChB,MAAM,IAAID,KAAK,CAAC,yDAAyD,CAAC;EAC5E;;EAEA;EACA,IAAI,CAAChF,OAAO,EAAE;IACZA,OAAO,GAAGiF,WAAW;;IAEvB;EACA,CAAC,MAAM,IAAIjF,OAAO,GAAGiF,WAAW,EAAE;IAChC,MAAM,IAAID,KAAK,CAAC,IAAI,GAClB,kEAAkE,GAClE,qDAAqD,GAAGC,WAAW,GAAG,KACxE,CAAC;EACH;EAEA,MAAMC,QAAQ,GAAGpD,UAAU,CAAC9B,OAAO,EAAEqB,oBAAoB,EAAEU,QAAQ,CAAC;;EAEpE;EACA,MAAMoD,WAAW,GAAGnG,KAAK,CAACoG,aAAa,CAACpF,OAAO,CAAC;EAChD,MAAMqF,OAAO,GAAG,IAAIjG,SAAS,CAAC+F,WAAW,CAAC;;EAE1C;EACArF,kBAAkB,CAACuF,OAAO,EAAErF,OAAO,CAAC;EACpCW,kBAAkB,CAAC0E,OAAO,CAAC;EAC3BxE,qBAAqB,CAACwE,OAAO,EAAErF,OAAO,CAAC;;EAEvC;EACA;EACA;EACA;EACAoB,eAAe,CAACiE,OAAO,EAAEhE,oBAAoB,EAAE,CAAC,CAAC;EAEjD,IAAIrB,OAAO,IAAI,CAAC,EAAE;IAChBc,gBAAgB,CAACuE,OAAO,EAAErF,OAAO,CAAC;EACpC;;EAEA;EACAuB,SAAS,CAAC8D,OAAO,EAAEH,QAAQ,CAAC;EAE5B,IAAII,KAAK,CAAChE,WAAW,CAAC,EAAE;IACtB;IACAA,WAAW,GAAG/B,WAAW,CAACgG,WAAW,CAACF,OAAO,EAC3CjE,eAAe,CAACoE,IAAI,CAAC,IAAI,EAAEH,OAAO,EAAEhE,oBAAoB,CAAC,CAAC;EAC9D;;EAEA;EACA9B,WAAW,CAACkG,SAAS,CAACnE,WAAW,EAAE+D,OAAO,CAAC;;EAE3C;EACAjE,eAAe,CAACiE,OAAO,EAAEhE,oBAAoB,EAAEC,WAAW,CAAC;EAE3D,OAAO;IACL+D,OAAO,EAAEA,OAAO;IAChBrF,OAAO,EAAEA,OAAO;IAChBqB,oBAAoB,EAAEA,oBAAoB;IAC1CC,WAAW,EAAEA,WAAW;IACxBS,QAAQ,EAAEA;EACZ,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA2D,OAAO,CAACC,MAAM,GAAG,SAASA,MAAMA,CAAEnE,IAAI,EAAEoE,OAAO,EAAE;EAC/C,IAAI,OAAOpE,IAAI,KAAK,WAAW,IAAIA,IAAI,KAAK,EAAE,EAAE;IAC9C,MAAM,IAAIwD,KAAK,CAAC,eAAe,CAAC;EAClC;EAEA,IAAI3D,oBAAoB,GAAGnC,OAAO,CAAC2G,CAAC;EACpC,IAAI7F,OAAO;EACX,IAAI8F,IAAI;EAER,IAAI,OAAOF,OAAO,KAAK,WAAW,EAAE;IAClC;IACAvE,oBAAoB,GAAGnC,OAAO,CAAC6G,IAAI,CAACH,OAAO,CAACvE,oBAAoB,EAAEnC,OAAO,CAAC2G,CAAC,CAAC;IAC5E7F,OAAO,GAAGN,OAAO,CAACqG,IAAI,CAACH,OAAO,CAAC5F,OAAO,CAAC;IACvC8F,IAAI,GAAGvG,WAAW,CAACwG,IAAI,CAACH,OAAO,CAACtE,WAAW,CAAC;IAE5C,IAAIsE,OAAO,CAACI,UAAU,EAAE;MACtBhH,KAAK,CAACiH,iBAAiB,CAACL,OAAO,CAACI,UAAU,CAAC;IAC7C;EACF;EAEA,OAAOxB,YAAY,CAAChD,IAAI,EAAExB,OAAO,EAAEqB,oBAAoB,EAAEyE,IAAI,CAAC;AAChE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}