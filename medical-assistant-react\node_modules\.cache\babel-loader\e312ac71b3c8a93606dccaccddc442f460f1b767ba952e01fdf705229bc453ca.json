{"ast": null, "code": "const GF = require('./galois-field');\n\n/**\n * Multiplies two polynomials inside Galois Field\n *\n * @param  {Uint8Array} p1 Polynomial\n * @param  {Uint8Array} p2 Polynomial\n * @return {Uint8Array}    Product of p1 and p2\n */\nexports.mul = function mul(p1, p2) {\n  const coeff = new Uint8Array(p1.length + p2.length - 1);\n  for (let i = 0; i < p1.length; i++) {\n    for (let j = 0; j < p2.length; j++) {\n      coeff[i + j] ^= GF.mul(p1[i], p2[j]);\n    }\n  }\n  return coeff;\n};\n\n/**\n * Calculate the remainder of polynomials division\n *\n * @param  {Uint8Array} divident Polynomial\n * @param  {Uint8Array} divisor  Polynomial\n * @return {Uint8Array}          Remainder\n */\nexports.mod = function mod(divident, divisor) {\n  let result = new Uint8Array(divident);\n  while (result.length - divisor.length >= 0) {\n    const coeff = result[0];\n    for (let i = 0; i < divisor.length; i++) {\n      result[i] ^= GF.mul(divisor[i], coeff);\n    }\n\n    // remove all zeros from buffer head\n    let offset = 0;\n    while (offset < result.length && result[offset] === 0) offset++;\n    result = result.slice(offset);\n  }\n  return result;\n};\n\n/**\n * Generate an irreducible generator polynomial of specified degree\n * (used by Reed-Solomon encoder)\n *\n * @param  {Number} degree Degree of the generator polynomial\n * @return {Uint8Array}    Buffer containing polynomial coefficients\n */\nexports.generateECPolynomial = function generateECPolynomial(degree) {\n  let poly = new Uint8Array([1]);\n  for (let i = 0; i < degree; i++) {\n    poly = exports.mul(poly, new Uint8Array([1, GF.exp(i)]));\n  }\n  return poly;\n};", "map": {"version": 3, "names": ["GF", "require", "exports", "mul", "p1", "p2", "coeff", "Uint8Array", "length", "i", "j", "mod", "divident", "divisor", "result", "offset", "slice", "generateECPolynomial", "degree", "poly", "exp"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/test 3/medical-assistant-react/node_modules/qrcode/lib/core/polynomial.js"], "sourcesContent": ["const GF = require('./galois-field')\n\n/**\n * Multiplies two polynomials inside Galois Field\n *\n * @param  {Uint8Array} p1 Polynomial\n * @param  {Uint8Array} p2 Polynomial\n * @return {Uint8Array}    Product of p1 and p2\n */\nexports.mul = function mul (p1, p2) {\n  const coeff = new Uint8Array(p1.length + p2.length - 1)\n\n  for (let i = 0; i < p1.length; i++) {\n    for (let j = 0; j < p2.length; j++) {\n      coeff[i + j] ^= GF.mul(p1[i], p2[j])\n    }\n  }\n\n  return coeff\n}\n\n/**\n * Calculate the remainder of polynomials division\n *\n * @param  {Uint8Array} divident Polynomial\n * @param  {Uint8Array} divisor  Polynomial\n * @return {Uint8Array}          Remainder\n */\nexports.mod = function mod (divident, divisor) {\n  let result = new Uint8Array(divident)\n\n  while ((result.length - divisor.length) >= 0) {\n    const coeff = result[0]\n\n    for (let i = 0; i < divisor.length; i++) {\n      result[i] ^= GF.mul(divisor[i], coeff)\n    }\n\n    // remove all zeros from buffer head\n    let offset = 0\n    while (offset < result.length && result[offset] === 0) offset++\n    result = result.slice(offset)\n  }\n\n  return result\n}\n\n/**\n * Generate an irreducible generator polynomial of specified degree\n * (used by Reed-Solomon encoder)\n *\n * @param  {Number} degree Degree of the generator polynomial\n * @return {Uint8Array}    Buffer containing polynomial coefficients\n */\nexports.generateECPolynomial = function generateECPolynomial (degree) {\n  let poly = new Uint8Array([1])\n  for (let i = 0; i < degree; i++) {\n    poly = exports.mul(poly, new Uint8Array([1, GF.exp(i)]))\n  }\n\n  return poly\n}\n"], "mappings": "AAAA,MAAMA,EAAE,GAAGC,OAAO,CAAC,gBAAgB,CAAC;;AAEpC;AACA;AACA;AACA;AACA;AACA;AACA;AACAC,OAAO,CAACC,GAAG,GAAG,SAASA,GAAGA,CAAEC,EAAE,EAAEC,EAAE,EAAE;EAClC,MAAMC,KAAK,GAAG,IAAIC,UAAU,CAACH,EAAE,CAACI,MAAM,GAAGH,EAAE,CAACG,MAAM,GAAG,CAAC,CAAC;EAEvD,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,EAAE,CAACI,MAAM,EAAEC,CAAC,EAAE,EAAE;IAClC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,EAAE,CAACG,MAAM,EAAEE,CAAC,EAAE,EAAE;MAClCJ,KAAK,CAACG,CAAC,GAAGC,CAAC,CAAC,IAAIV,EAAE,CAACG,GAAG,CAACC,EAAE,CAACK,CAAC,CAAC,EAAEJ,EAAE,CAACK,CAAC,CAAC,CAAC;IACtC;EACF;EAEA,OAAOJ,KAAK;AACd,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACAJ,OAAO,CAACS,GAAG,GAAG,SAASA,GAAGA,CAAEC,QAAQ,EAAEC,OAAO,EAAE;EAC7C,IAAIC,MAAM,GAAG,IAAIP,UAAU,CAACK,QAAQ,CAAC;EAErC,OAAQE,MAAM,CAACN,MAAM,GAAGK,OAAO,CAACL,MAAM,IAAK,CAAC,EAAE;IAC5C,MAAMF,KAAK,GAAGQ,MAAM,CAAC,CAAC,CAAC;IAEvB,KAAK,IAAIL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGI,OAAO,CAACL,MAAM,EAAEC,CAAC,EAAE,EAAE;MACvCK,MAAM,CAACL,CAAC,CAAC,IAAIT,EAAE,CAACG,GAAG,CAACU,OAAO,CAACJ,CAAC,CAAC,EAAEH,KAAK,CAAC;IACxC;;IAEA;IACA,IAAIS,MAAM,GAAG,CAAC;IACd,OAAOA,MAAM,GAAGD,MAAM,CAACN,MAAM,IAAIM,MAAM,CAACC,MAAM,CAAC,KAAK,CAAC,EAAEA,MAAM,EAAE;IAC/DD,MAAM,GAAGA,MAAM,CAACE,KAAK,CAACD,MAAM,CAAC;EAC/B;EAEA,OAAOD,MAAM;AACf,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACAZ,OAAO,CAACe,oBAAoB,GAAG,SAASA,oBAAoBA,CAAEC,MAAM,EAAE;EACpE,IAAIC,IAAI,GAAG,IAAIZ,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;EAC9B,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGS,MAAM,EAAET,CAAC,EAAE,EAAE;IAC/BU,IAAI,GAAGjB,OAAO,CAACC,GAAG,CAACgB,IAAI,EAAE,IAAIZ,UAAU,CAAC,CAAC,CAAC,EAAEP,EAAE,CAACoB,GAAG,CAACX,CAAC,CAAC,CAAC,CAAC,CAAC;EAC1D;EAEA,OAAOU,IAAI;AACb,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}