[{"C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\test 3\\medical-assistant-react\\src\\index.tsx": "1", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\test 3\\medical-assistant-react\\src\\App.tsx": "2", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\test 3\\medical-assistant-react\\src\\contexts\\AuthContext.tsx": "3", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\test 3\\medical-assistant-react\\src\\contexts\\ToastContext.tsx": "4", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\test 3\\medical-assistant-react\\src\\components\\ProtectedRoute\\ProtectedRoute.tsx": "5", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\test 3\\medical-assistant-react\\src\\pages\\Diagnosis\\Diagnosis.tsx": "6", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\test 3\\medical-assistant-react\\src\\components\\Layout\\Layout.tsx": "7", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\test 3\\medical-assistant-react\\src\\pages\\Appointments\\Appointments.tsx": "8", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\test 3\\medical-assistant-react\\src\\pages\\Doctors\\Doctors.tsx": "9", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\test 3\\medical-assistant-react\\src\\pages\\MyAppointments\\MyAppointments.tsx": "10", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\test 3\\medical-assistant-react\\src\\pages\\Home\\Home.tsx": "11", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\test 3\\medical-assistant-react\\src\\pages\\Booking\\Booking.tsx": "12", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\test 3\\medical-assistant-react\\src\\pages\\Results\\Results.tsx": "13", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\test 3\\medical-assistant-react\\src\\pages\\Confirmation\\Confirmation.tsx": "14", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\test 3\\medical-assistant-react\\src\\pages\\Profile\\Profile.tsx": "15", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\test 3\\medical-assistant-react\\src\\components\\Layout\\Header.tsx": "16", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\test 3\\medical-assistant-react\\src\\components\\Layout\\Footer.tsx": "17", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\test 3\\medical-assistant-react\\src\\pages\\Diagnosis\\components\\TypingIndicator.tsx": "18", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\test 3\\medical-assistant-react\\src\\components\\Auth\\AuthModal.tsx": "19", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\test 3\\medical-assistant-react\\src\\pages\\Diagnosis\\components\\ChatMessage.tsx": "20", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\test 3\\medical-assistant-react\\src\\pages\\Appointments\\components\\StepIndicator.tsx": "21", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\test 3\\medical-assistant-react\\src\\pages\\Home\\components\\HowItWorksSection.tsx": "22", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\test 3\\medical-assistant-react\\src\\pages\\Home\\components\\HeroSection.tsx": "23", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\test 3\\medical-assistant-react\\src\\pages\\Home\\components\\FeaturesSection.tsx": "24", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\test 3\\medical-assistant-react\\src\\pages\\Home\\components\\CTASection.tsx": "25", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\test 3\\medical-assistant-react\\src\\pages\\Home\\components\\QRCodeSection.tsx": "26", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\test 3\\medical-assistant-react\\src\\pages\\Home\\components\\TestimonialsSection.tsx": "27", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\test 3\\medical-assistant-react\\src\\components\\Auth\\LoginForm.tsx": "28", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\test 3\\medical-assistant-react\\src\\components\\Auth\\RegisterForm.tsx": "29"}, {"size": 892, "mtime": 1750205036361, "results": "30", "hashOfConfig": "31"}, {"size": 2390, "mtime": 1750205070584, "results": "32", "hashOfConfig": "31"}, {"size": 6485, "mtime": 1750205161379, "results": "33", "hashOfConfig": "31"}, {"size": 3354, "mtime": 1750206774127, "results": "34", "hashOfConfig": "31"}, {"size": 1944, "mtime": 1750205357758, "results": "35", "hashOfConfig": "31"}, {"size": 14662, "mtime": 1750205575175, "results": "36", "hashOfConfig": "31"}, {"size": 423, "mtime": 1750205196007, "results": "37", "hashOfConfig": "31"}, {"size": 7446, "mtime": 1750206787606, "results": "38", "hashOfConfig": "31"}, {"size": 2432, "mtime": 1750205669029, "results": "39", "hashOfConfig": "31"}, {"size": 4829, "mtime": 1750205724223, "results": "40", "hashOfConfig": "31"}, {"size": 897, "mtime": 1750205369454, "results": "41", "hashOfConfig": "31"}, {"size": 3575, "mtime": 1750205685063, "results": "42", "hashOfConfig": "31"}, {"size": 3930, "mtime": 1750205775160, "results": "43", "hashOfConfig": "31"}, {"size": 3379, "mtime": 1750205701883, "results": "44", "hashOfConfig": "31"}, {"size": 6833, "mtime": 1750205754103, "results": "45", "hashOfConfig": "31"}, {"size": 4051, "mtime": 1750205220264, "results": "46", "hashOfConfig": "31"}, {"size": 3148, "mtime": 1750205240437, "results": "47", "hashOfConfig": "31"}, {"size": 1786, "mtime": 1750206813358, "results": "48", "hashOfConfig": "31"}, {"size": 1363, "mtime": 1750207923538, "results": "49", "hashOfConfig": "31"}, {"size": 2028, "mtime": 1750205593694, "results": "50", "hashOfConfig": "31"}, {"size": 1962, "mtime": 1750206801101, "results": "51", "hashOfConfig": "31"}, {"size": 2098, "mtime": 1750205452062, "results": "52", "hashOfConfig": "31"}, {"size": 2668, "mtime": 1750207968523, "results": "53", "hashOfConfig": "31"}, {"size": 2700, "mtime": 1750205409912, "results": "54", "hashOfConfig": "31"}, {"size": 1115, "mtime": 1750207983409, "results": "55", "hashOfConfig": "31"}, {"size": 6113, "mtime": 1750205478556, "results": "56", "hashOfConfig": "31"}, {"size": 3384, "mtime": 1750205504841, "results": "57", "hashOfConfig": "31"}, {"size": 3742, "mtime": 1750205300579, "results": "58", "hashOfConfig": "31"}, {"size": 7170, "mtime": 1750205340476, "results": "59", "hashOfConfig": "31"}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "owti31", {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\test 3\\medical-assistant-react\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\test 3\\medical-assistant-react\\src\\App.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\test 3\\medical-assistant-react\\src\\contexts\\AuthContext.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\test 3\\medical-assistant-react\\src\\contexts\\ToastContext.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\test 3\\medical-assistant-react\\src\\components\\ProtectedRoute\\ProtectedRoute.tsx", ["147"], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\test 3\\medical-assistant-react\\src\\pages\\Diagnosis\\Diagnosis.tsx", ["148"], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\test 3\\medical-assistant-react\\src\\components\\Layout\\Layout.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\test 3\\medical-assistant-react\\src\\pages\\Appointments\\Appointments.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\test 3\\medical-assistant-react\\src\\pages\\Doctors\\Doctors.tsx", ["149"], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\test 3\\medical-assistant-react\\src\\pages\\MyAppointments\\MyAppointments.tsx", ["150", "151"], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\test 3\\medical-assistant-react\\src\\pages\\Home\\Home.tsx", ["152", "153", "154", "155", "156", "157", "158", "159"], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\test 3\\medical-assistant-react\\src\\pages\\Booking\\Booking.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\test 3\\medical-assistant-react\\src\\pages\\Results\\Results.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\test 3\\medical-assistant-react\\src\\pages\\Confirmation\\Confirmation.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\test 3\\medical-assistant-react\\src\\pages\\Profile\\Profile.tsx", ["160"], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\test 3\\medical-assistant-react\\src\\components\\Layout\\Header.tsx", ["161"], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\test 3\\medical-assistant-react\\src\\components\\Layout\\Footer.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\test 3\\medical-assistant-react\\src\\pages\\Diagnosis\\components\\TypingIndicator.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\test 3\\medical-assistant-react\\src\\components\\Auth\\AuthModal.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\test 3\\medical-assistant-react\\src\\pages\\Diagnosis\\components\\ChatMessage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\test 3\\medical-assistant-react\\src\\pages\\Appointments\\components\\StepIndicator.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\test 3\\medical-assistant-react\\src\\pages\\Home\\components\\HowItWorksSection.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\test 3\\medical-assistant-react\\src\\pages\\Home\\components\\HeroSection.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\test 3\\medical-assistant-react\\src\\pages\\Home\\components\\FeaturesSection.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\test 3\\medical-assistant-react\\src\\pages\\Home\\components\\CTASection.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\test 3\\medical-assistant-react\\src\\pages\\Home\\components\\QRCodeSection.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\test 3\\medical-assistant-react\\src\\pages\\Home\\components\\TestimonialsSection.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\test 3\\medical-assistant-react\\src\\components\\Auth\\LoginForm.tsx", ["162"], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\test 3\\medical-assistant-react\\src\\components\\Auth\\RegisterForm.tsx", ["163"], [], {"ruleId": "164", "severity": 1, "message": "165", "line": 19, "column": 9, "nodeType": "166", "messageId": "167", "endLine": 19, "endColumn": 26}, {"ruleId": "164", "severity": 1, "message": "168", "line": 2, "column": 58, "nodeType": "166", "messageId": "167", "endLine": 2, "endColumn": 63}, {"ruleId": "164", "severity": 1, "message": "169", "line": 5, "column": 34, "nodeType": "166", "messageId": "167", "endLine": 5, "endColumn": 45}, {"ruleId": "164", "severity": 1, "message": "170", "line": 4, "column": 31, "nodeType": "166", "messageId": "167", "endLine": 4, "endColumn": 37}, {"ruleId": "164", "severity": 1, "message": "171", "line": 4, "column": 39, "nodeType": "166", "messageId": "167", "endLine": 4, "endColumn": 53}, {"ruleId": "164", "severity": 1, "message": "172", "line": 2, "column": 10, "nodeType": "166", "messageId": "167", "endLine": 2, "endColumn": 19}, {"ruleId": "164", "severity": 1, "message": "173", "line": 2, "column": 21, "nodeType": "166", "messageId": "167", "endLine": 2, "endColumn": 24}, {"ruleId": "164", "severity": 1, "message": "174", "line": 2, "column": 26, "nodeType": "166", "messageId": "167", "endLine": 2, "endColumn": 29}, {"ruleId": "164", "severity": 1, "message": "175", "line": 2, "column": 31, "nodeType": "166", "messageId": "167", "endLine": 2, "endColumn": 37}, {"ruleId": "164", "severity": 1, "message": "176", "line": 3, "column": 10, "nodeType": "166", "messageId": "167", "endLine": 3, "endColumn": 14}, {"ruleId": "164", "severity": 1, "message": "177", "line": 4, "column": 10, "nodeType": "166", "messageId": "167", "endLine": 4, "endColumn": 25}, {"ruleId": "164", "severity": 1, "message": "178", "line": 5, "column": 10, "nodeType": "166", "messageId": "167", "endLine": 5, "endColumn": 23}, {"ruleId": "164", "severity": 1, "message": "179", "line": 5, "column": 25, "nodeType": "166", "messageId": "167", "endLine": 5, "endColumn": 39}, {"ruleId": "164", "severity": 1, "message": "168", "line": 2, "column": 51, "nodeType": "166", "messageId": "167", "endLine": 2, "endColumn": 56}, {"ruleId": "164", "severity": 1, "message": "180", "line": 3, "column": 52, "nodeType": "166", "messageId": "167", "endLine": 3, "endColumn": 57}, {"ruleId": "181", "severity": 1, "message": "182", "line": 123, "column": 9, "nodeType": "183", "endLine": 123, "endColumn": 50}, {"ruleId": "181", "severity": 1, "message": "182", "line": 212, "column": 25, "nodeType": "183", "endLine": 212, "endColumn": 62}, "@typescript-eslint/no-unused-vars", "'handleAuthSuccess' is assigned a value but never used.", "Identifier", "unusedVar", "'Alert' is defined but never used.", "'faArrowLeft' is defined but never used.", "'faUser' is defined but never used.", "'faMapMarkerAlt' is defined but never used.", "'Container' is defined but never used.", "'Row' is defined but never used.", "'Col' is defined but never used.", "'Button' is defined but never used.", "'Link' is defined but never used.", "'FontAwesomeIcon' is defined but never used.", "'faStethoscope' is defined but never used.", "'faCalendarPlus' is defined but never used.", "'Modal' is defined but never used.", "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement"]