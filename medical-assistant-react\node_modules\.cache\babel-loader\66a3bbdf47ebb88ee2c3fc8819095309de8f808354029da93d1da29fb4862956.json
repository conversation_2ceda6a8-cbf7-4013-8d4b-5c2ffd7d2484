{"ast": null, "code": "function BitBuffer() {\n  this.buffer = [];\n  this.length = 0;\n}\nBitBuffer.prototype = {\n  get: function (index) {\n    const bufIndex = Math.floor(index / 8);\n    return (this.buffer[bufIndex] >>> 7 - index % 8 & 1) === 1;\n  },\n  put: function (num, length) {\n    for (let i = 0; i < length; i++) {\n      this.putBit((num >>> length - i - 1 & 1) === 1);\n    }\n  },\n  getLengthInBits: function () {\n    return this.length;\n  },\n  putBit: function (bit) {\n    const bufIndex = Math.floor(this.length / 8);\n    if (this.buffer.length <= bufIndex) {\n      this.buffer.push(0);\n    }\n    if (bit) {\n      this.buffer[bufIndex] |= 0x80 >>> this.length % 8;\n    }\n    this.length++;\n  }\n};\nmodule.exports = BitBuffer;", "map": {"version": 3, "names": ["BitBuffer", "buffer", "length", "prototype", "get", "index", "bufIndex", "Math", "floor", "put", "num", "i", "putBit", "getLengthInBits", "bit", "push", "module", "exports"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/test 3/medical-assistant-react/node_modules/qrcode/lib/core/bit-buffer.js"], "sourcesContent": ["function BitBuffer () {\n  this.buffer = []\n  this.length = 0\n}\n\nBitBuffer.prototype = {\n\n  get: function (index) {\n    const bufIndex = Math.floor(index / 8)\n    return ((this.buffer[bufIndex] >>> (7 - index % 8)) & 1) === 1\n  },\n\n  put: function (num, length) {\n    for (let i = 0; i < length; i++) {\n      this.putBit(((num >>> (length - i - 1)) & 1) === 1)\n    }\n  },\n\n  getLengthInBits: function () {\n    return this.length\n  },\n\n  putBit: function (bit) {\n    const bufIndex = Math.floor(this.length / 8)\n    if (this.buffer.length <= bufIndex) {\n      this.buffer.push(0)\n    }\n\n    if (bit) {\n      this.buffer[bufIndex] |= (0x80 >>> (this.length % 8))\n    }\n\n    this.length++\n  }\n}\n\nmodule.exports = BitBuffer\n"], "mappings": "AAAA,SAASA,SAASA,CAAA,EAAI;EACpB,IAAI,CAACC,MAAM,GAAG,EAAE;EAChB,IAAI,CAACC,MAAM,GAAG,CAAC;AACjB;AAEAF,SAAS,CAACG,SAAS,GAAG;EAEpBC,GAAG,EAAE,SAAAA,CAAUC,KAAK,EAAE;IACpB,MAAMC,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACH,KAAK,GAAG,CAAC,CAAC;IACtC,OAAO,CAAE,IAAI,CAACJ,MAAM,CAACK,QAAQ,CAAC,KAAM,CAAC,GAAGD,KAAK,GAAG,CAAE,GAAI,CAAC,MAAM,CAAC;EAChE,CAAC;EAEDI,GAAG,EAAE,SAAAA,CAAUC,GAAG,EAAER,MAAM,EAAE;IAC1B,KAAK,IAAIS,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGT,MAAM,EAAES,CAAC,EAAE,EAAE;MAC/B,IAAI,CAACC,MAAM,CAAC,CAAEF,GAAG,KAAMR,MAAM,GAAGS,CAAC,GAAG,CAAE,GAAI,CAAC,MAAM,CAAC,CAAC;IACrD;EACF,CAAC;EAEDE,eAAe,EAAE,SAAAA,CAAA,EAAY;IAC3B,OAAO,IAAI,CAACX,MAAM;EACpB,CAAC;EAEDU,MAAM,EAAE,SAAAA,CAAUE,GAAG,EAAE;IACrB,MAAMR,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAAC,IAAI,CAACN,MAAM,GAAG,CAAC,CAAC;IAC5C,IAAI,IAAI,CAACD,MAAM,CAACC,MAAM,IAAII,QAAQ,EAAE;MAClC,IAAI,CAACL,MAAM,CAACc,IAAI,CAAC,CAAC,CAAC;IACrB;IAEA,IAAID,GAAG,EAAE;MACP,IAAI,CAACb,MAAM,CAACK,QAAQ,CAAC,IAAK,IAAI,KAAM,IAAI,CAACJ,MAAM,GAAG,CAAG;IACvD;IAEA,IAAI,CAACA,MAAM,EAAE;EACf;AACF,CAAC;AAEDc,MAAM,CAACC,OAAO,GAAGjB,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}