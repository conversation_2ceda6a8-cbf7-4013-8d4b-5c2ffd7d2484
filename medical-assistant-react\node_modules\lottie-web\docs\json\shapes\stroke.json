{"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"mn": {"title": "Match Name", "description": "After Effect's Match Name. Used for expressions.", "type": "string"}, "nm": {"title": "Name", "description": "After Effect's Name. Used for expressions.", "type": "string"}, "ty": {"title": "Type", "description": "Shape content type.", "type": "string", "const": "st"}, "lc": {"title": "Line Cap", "description": "Stroke Line Cap", "$ref": "#/helpers/lineCap", "type": "number"}, "lj": {"title": "Line Join", "description": "Stroke Line Join", "$ref": "#/helpers/lineJoin", "type": "number"}, "ml": {"title": "<PERSON><PERSON>", "description": "Stroke Miter Limit. Only if Line Join is set to <PERSON><PERSON>.", "type": "number"}, "o": {"title": "Opacity", "description": "Stroke Opacity", "oneOf": [{"$ref": "#/properties/value"}, {"$ref": "#/properties/valueKeyframed"}], "type": "object"}, "w": {"title": "<PERSON><PERSON><PERSON>", "description": "Stroke Width", "oneOf": [{"$ref": "#/properties/value"}, {"$ref": "#/properties/valueKeyframed"}], "type": "object"}, "c": {"title": "Color", "description": "Stroke Color", "oneOf": [{"$ref": "#/properties/multiDimensional"}, {"$ref": "#/properties/multiDimensionalKeyframed"}], "type": "object"}}}