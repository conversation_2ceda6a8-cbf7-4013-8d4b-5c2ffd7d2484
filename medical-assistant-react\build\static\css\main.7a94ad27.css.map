{"version": 3, "file": "static/css/main.7a94ad27.css", "mappings": "AAOA,MAEE,oBAAqB,CACrB,qBAAsB,CACtB,qBAAsB,CACtB,qBAAsB,CACtB,qBAAsB,CACtB,qBAAsB,CACtB,qBAAsB,CACtB,qBAAsB,CACtB,qBAAsB,CACtB,qBAAsB,CAGtB,sBAAuB,CACvB,uBAAwB,CACxB,uBAAwB,CACxB,uBAAwB,CACxB,uBAAwB,CACxB,uBAAwB,CACxB,uBAAwB,CACxB,uBAAwB,CACxB,uBAAwB,CACxB,uBAAwB,CAGxB,oBAAqB,CACrB,qBAAsB,CACtB,qBAAsB,CACtB,qBAAsB,CAEtB,oBAAqB,CACrB,qBAAsB,CACtB,qBAAsB,CAEtB,mBAAoB,CACpB,oBAAqB,CACrB,oBAAqB,CAErB,iBAAkB,CAClB,kBAAmB,CACnB,kBAAmB,CAGnB,iBAAkB,CAClB,kBAAmB,CACnB,kBAAmB,CACnB,kBAAmB,CACnB,kBAAmB,CACnB,kBAAmB,CACnB,kBAAmB,CACnB,kBAAmB,CACnB,kBAAmB,CACnB,kBAAmB,CAGnB,wEAA6E,CAC7E,wEAA4E,CAE5E,sBAAuB,CACvB,uBAAwB,CACxB,qBAAsB,CACtB,uBAAwB,CACxB,sBAAuB,CACvB,sBAAuB,CACvB,wBAAyB,CACzB,uBAAwB,CACxB,oBAAqB,CAErB,wBAAyB,CACzB,wBAAyB,CACzB,0BAA2B,CAC3B,sBAAuB,CACvB,2BAA4B,CAE5B,wBAAyB,CACzB,wBAAyB,CACzB,0BAA2B,CAG3B,WAAY,CACZ,iBAAkB,CAClB,gBAAiB,CACjB,iBAAkB,CAClB,cAAe,CACf,iBAAkB,CAClB,gBAAiB,CACjB,cAAe,CACf,iBAAkB,CAClB,eAAgB,CAChB,eAAgB,CAChB,eAAgB,CAChB,eAAgB,CAGhB,eAAgB,CAChB,mBAAoB,CACpB,oBAAqB,CACrB,kBAAmB,CACnB,mBAAoB,CACpB,iBAAkB,CAClB,mBAAoB,CACpB,oBAAqB,CAGrB,iCAA4C,CAC5C,0DAA8E,CAC9E,6DAAiF,CACjF,+DAAmF,CACnF,gEAAoF,CACpF,wCAAmD,CAGnD,iDAAqD,CACrD,mDAAuD,CACvD,iDAAqD,CAGrD,iBAAkB,CAClB,eAAgB,CAChB,cAAe,CACf,uBAAwB,CACxB,cAAe,CACf,gBAAiB,CACjB,gBAAiB,CACjB,cACF,CAKA,KAGE,mBAA+B,CAA/B,8BAA+B,CAF/B,0DAAuC,CAAvC,sCAAuC,CACvC,eAAsC,CAAtC,qCAAsC,CAEtC,0CAAwC,CAAxC,uCACF,CAEA,aACE,kDAAmF,CAAnF,+EAAmF,CACnF,oBAAgC,CAAhC,+BACF,CAEA,mBACE,kDAAmF,CAAnF,+EAAmF,CACnF,oBAAgC,CAAhC,+BAAgC,CAEhC,4DAA4B,CAA5B,2BAA4B,CAD5B,0BAEF,CAEA,MACE,kBAAgC,CAAhC,+BAAgC,CAChC,yDAA4B,CAA5B,2BAA4B,CAC5B,0CAAwC,CAAxC,uCACF,CAEA,YACE,8DAA4B,CAA5B,2BAA4B,CAC5B,0BACF,CAEA,QACE,kCAA2B,CAA3B,0BAA2B,CAC3B,8BACF,CAEA,cAEE,wBAAiC,CAAjC,gCAAiC,CADjC,mBAA+B,CAA/B,8BAA+B,CAE/B,2CAAsC,CAAtC,qCACF,CAEA,oBACE,oBAAgC,CAAhC,+BAAgC,CAChC,gCACF,CAGA,eAGE,6BAAoC,CAFpC,kDAAmF,CAAnF,+EAAmF,CACnF,4BAA6B,CAE7B,oBACF,CAEA,YACE,gDAA8C,CAA9C,6CACF,CAEA,kBACE,0BACF,CAEA,WACE,4BAA2C,CAA3C,yCACF,CAEA,OAEE,mBAA+B,CAA/B,8BAA+B,CAD/B,gBAA4B,CAA5B,2BAEF,CAEA,OACE,kBAA8B,CAA9B,6BACF,CAEA,OACE,YAAuB,CAAvB,sBACF,CCtNA,iBAGE,qBAAsB,CACtB,QAAS,CACT,SACF,CAEA,KAGE,6BAA8B,CAC9B,yBAA0B,CAH1B,cAAe,CACf,sBAGF,CAEA,KAOE,kCAAmC,CACnC,iCAAkC,CAFlC,wBAAyB,CADzB,aAAc,CAMd,aAAc,CAVd,0DAAmE,CACnE,cAAe,CACf,eAAgB,CAChB,eAAgB,CAMhB,iBAAkB,CADlB,iCAGF,CAGA,OAEE,eAAgB,CADhB,WAAY,CAEZ,cACF,CAGA,EAEE,aAAc,CADd,oBAEF,CAGA,MACE,eACF,CAGA,sBACE,mBAAoB,CACpB,iBACF,CAGA,OACE,yBAA0B,CAC1B,kBACF,CAGA,SAOE,kBAAsB,CAEtB,QAAS,CANT,UAAW,CAEX,WAAY,CACZ,eAAgB,CAFhB,SAAU,CAHV,iBAAkB,CAOlB,kBAAmB,CANnB,SAQF,CAGA,kBACE,GAAO,SAAY,CACnB,GAAK,SAAY,CACnB,CAEA,qBACE,GACE,SAAU,CACV,0BACF,CACA,GACE,SAAU,CACV,uBACF,CACF,CAEA,wBACE,GACE,SAAU,CACV,2BACF,CACA,GACE,SAAU,CACV,uBACF,CACF,CAEA,gBACE,6BACF,CAEA,mBACE,gCACF,CAEA,sBACE,mCACF,CAGA,SAOE,iCAAkC,CAFlC,sBAAkC,CAClC,iBAAkB,CADlB,6BAAkC,CAJlC,oBAAqB,CAErB,WAAY,CADZ,UAMF,CAEA,gBACE,GAAK,sBAAyB,CAC9B,GAAO,uBAA2B,CACpC", "sources": ["styles/theme.css", "index.css"], "sourcesContent": ["/**\n * مساعد طبي ذكي - نظام التصميم الموحد\n * Unified Design System for Medical Assistant React App\n * Version: 3.0\n */\n\n/* ========== CSS CUSTOM PROPERTIES (DESIGN TOKENS) ========== */\n:root {\n  /* Primary Brand Colors */\n  --primary-50: #eff6ff;\n  --primary-100: #dbeafe;\n  --primary-200: #bfdbfe;\n  --primary-300: #93c5fd;\n  --primary-400: #60a5fa;\n  --primary-500: #3b82f6;\n  --primary-600: #2563eb;\n  --primary-700: #1d4ed8;\n  --primary-800: #1e40af;\n  --primary-900: #1e3a8a;\n\n  /* Secondary Colors */\n  --secondary-50: #fff7ed;\n  --secondary-100: #ffedd5;\n  --secondary-200: #fed7aa;\n  --secondary-300: #fdba74;\n  --secondary-400: #fb923c;\n  --secondary-500: #f97316;\n  --secondary-600: #ea580c;\n  --secondary-700: #c2410c;\n  --secondary-800: #9a3412;\n  --secondary-900: #7c2d12;\n\n  /* Semantic Colors */\n  --success-50: #ecfdf5;\n  --success-500: #10b981;\n  --success-600: #059669;\n  --success-700: #047857;\n\n  --warning-50: #fffbeb;\n  --warning-500: #f59e0b;\n  --warning-600: #d97706;\n\n  --danger-50: #fef2f2;\n  --danger-500: #ef4444;\n  --danger-600: #dc2626;\n\n  --info-50: #f0f9ff;\n  --info-500: #0ea5e9;\n  --info-600: #0284c7;\n\n  /* Neutral Colors */\n  --gray-50: #f8fafc;\n  --gray-100: #f1f5f9;\n  --gray-200: #e2e8f0;\n  --gray-300: #cbd5e1;\n  --gray-400: #94a3b8;\n  --gray-500: #64748b;\n  --gray-600: #475569;\n  --gray-700: #334155;\n  --gray-800: #1e293b;\n  --gray-900: #0f172a;\n\n  /* Typography Scale */\n  --font-family-primary: 'Cairo', 'Inter', system-ui, -apple-system, sans-serif;\n  --font-family-secondary: 'Plus Jakarta Sans', 'Inter', system-ui, sans-serif;\n  \n  --font-size-xs: 0.75rem;    /* 12px */\n  --font-size-sm: 0.875rem;   /* 14px */\n  --font-size-base: 1rem;     /* 16px */\n  --font-size-lg: 1.125rem;   /* 18px */\n  --font-size-xl: 1.25rem;    /* 20px */\n  --font-size-2xl: 1.5rem;    /* 24px */\n  --font-size-3xl: 1.875rem;  /* 30px */\n  --font-size-4xl: 2.25rem;   /* 36px */\n  --font-size-5xl: 3rem;      /* 48px */\n\n  --font-weight-normal: 400;\n  --font-weight-medium: 500;\n  --font-weight-semibold: 600;\n  --font-weight-bold: 700;\n  --font-weight-extrabold: 800;\n\n  --line-height-tight: 1.25;\n  --line-height-normal: 1.5;\n  --line-height-relaxed: 1.75;\n\n  /* Spacing Scale */\n  --space-0: 0;\n  --space-1: 0.25rem;   /* 4px */\n  --space-2: 0.5rem;    /* 8px */\n  --space-3: 0.75rem;   /* 12px */\n  --space-4: 1rem;      /* 16px */\n  --space-5: 1.25rem;   /* 20px */\n  --space-6: 1.5rem;    /* 24px */\n  --space-8: 2rem;      /* 32px */\n  --space-10: 2.5rem;   /* 40px */\n  --space-12: 3rem;     /* 48px */\n  --space-16: 4rem;     /* 64px */\n  --space-20: 5rem;     /* 80px */\n  --space-24: 6rem;     /* 96px */\n\n  /* Border Radius */\n  --radius-none: 0;\n  --radius-sm: 0.25rem;\n  --radius-md: 0.375rem;\n  --radius-lg: 0.5rem;\n  --radius-xl: 0.75rem;\n  --radius-2xl: 1rem;\n  --radius-3xl: 1.5rem;\n  --radius-full: 9999px;\n\n  /* Shadows */\n  --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);\n  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1);\n  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1);\n  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1);\n  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1);\n  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);\n\n  /* Transitions */\n  --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);\n  --transition-normal: 300ms cubic-bezier(0.4, 0, 0.2, 1);\n  --transition-slow: 500ms cubic-bezier(0.4, 0, 0.2, 1);\n\n  /* Z-Index Scale */\n  --z-dropdown: 1000;\n  --z-sticky: 1020;\n  --z-fixed: 1030;\n  --z-modal-backdrop: 1040;\n  --z-modal: 1050;\n  --z-popover: 1060;\n  --z-tooltip: 1070;\n  --z-toast: 1080;\n}\n\n/* ========== COMPONENT OVERRIDES ========== */\n\n/* Bootstrap RTL Adjustments */\n.btn {\n  font-family: var(--font-family-primary);\n  font-weight: var(--font-weight-medium);\n  border-radius: var(--radius-lg);\n  transition: all var(--transition-normal);\n}\n\n.btn-primary {\n  background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-700) 100%);\n  border-color: var(--primary-600);\n}\n\n.btn-primary:hover {\n  background: linear-gradient(135deg, var(--primary-700) 0%, var(--primary-800) 100%);\n  border-color: var(--primary-700);\n  transform: translateY(-1px);\n  box-shadow: var(--shadow-md);\n}\n\n.card {\n  border-radius: var(--radius-2xl);\n  box-shadow: var(--shadow-sm);\n  transition: all var(--transition-normal);\n}\n\n.card:hover {\n  box-shadow: var(--shadow-lg);\n  transform: translateY(-2px);\n}\n\n.navbar {\n  backdrop-filter: blur(10px);\n  background: rgba(255, 255, 255, 0.95) !important;\n}\n\n.form-control {\n  border-radius: var(--radius-lg);\n  border: 2px solid var(--gray-200);\n  transition: all var(--transition-fast);\n}\n\n.form-control:focus {\n  border-color: var(--primary-500);\n  box-shadow: 0 0 0 0.2rem rgba(59, 130, 246, 0.25);\n}\n\n/* Custom utility classes */\n.text-gradient {\n  background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-700) 100%);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n}\n\n.hover-lift {\n  transition: transform var(--transition-normal);\n}\n\n.hover-lift:hover {\n  transform: translateY(-4px);\n}\n\n.rounded-4 {\n  border-radius: var(--radius-2xl) !important;\n}\n\n.py-20 {\n  padding-top: var(--space-20);\n  padding-bottom: var(--space-20);\n}\n\n.mb-16 {\n  margin-bottom: var(--space-16);\n}\n\n.g-8 > * {\n  padding: var(--space-8);\n}\n\n/* Animation classes */\n.animate-fadeIn {\n  animation: fadeIn 0.6s ease-out;\n}\n\n.animate-slideInUp {\n  animation: slideInUp 0.6s ease-out;\n}\n\n@keyframes fadeIn {\n  from { opacity: 0; }\n  to { opacity: 1; }\n}\n\n@keyframes slideInUp {\n  from {\n    opacity: 0;\n    transform: translateY(30px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n", "/* Global CSS Reset and Base Styles */\n*,\n*::before,\n*::after {\n  box-sizing: border-box;\n  margin: 0;\n  padding: 0;\n}\n\nhtml {\n  font-size: 16px;\n  scroll-behavior: smooth;\n  -webkit-text-size-adjust: 100%;\n  -ms-text-size-adjust: 100%;\n}\n\nbody {\n  font-family: 'Cairo', 'Inter', system-ui, -apple-system, sans-serif;\n  font-size: 1rem;\n  font-weight: 400;\n  line-height: 1.5;\n  color: #1e293b;\n  background-color: #f8fafc;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  text-rendering: optimizeLegibility;\n  overflow-x: hidden;\n  direction: rtl;\n}\n\n/* Remove default button styles */\nbutton {\n  border: none;\n  background: none;\n  cursor: pointer;\n}\n\n/* Remove default link styles */\na {\n  text-decoration: none;\n  color: inherit;\n}\n\n/* Remove default list styles */\nul, ol {\n  list-style: none;\n}\n\n/* Remove default input styles */\ninput, textarea, select {\n  font-family: inherit;\n  font-size: inherit;\n}\n\n/* Focus styles for accessibility */\n*:focus {\n  outline: 2px solid #3b82f6;\n  outline-offset: 2px;\n}\n\n/* Utility classes */\n.sr-only {\n  position: absolute;\n  width: 1px;\n  height: 1px;\n  padding: 0;\n  margin: -1px;\n  overflow: hidden;\n  clip: rect(0, 0, 0, 0);\n  white-space: nowrap;\n  border: 0;\n}\n\n/* Animation classes */\n@keyframes fadeIn {\n  from { opacity: 0; }\n  to { opacity: 1; }\n}\n\n@keyframes slideInUp {\n  from {\n    opacity: 0;\n    transform: translateY(30px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n@keyframes slideInRight {\n  from {\n    opacity: 0;\n    transform: translateX(-30px);\n  }\n  to {\n    opacity: 1;\n    transform: translateX(0);\n  }\n}\n\n.animate-fadeIn {\n  animation: fadeIn 0.6s ease-out;\n}\n\n.animate-slideInUp {\n  animation: slideInUp 0.6s ease-out;\n}\n\n.animate-slideInRight {\n  animation: slideInRight 0.6s ease-out;\n}\n\n/* Loading spinner */\n.spinner {\n  display: inline-block;\n  width: 1rem;\n  height: 1rem;\n  border: 2px solid transparent;\n  border-top: 2px solid currentColor;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n"], "names": [], "sourceRoot": ""}