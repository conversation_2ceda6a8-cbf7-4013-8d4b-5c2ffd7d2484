{"ast": null, "code": "const Mode = require('./mode');\nconst Utils = require('./utils');\nfunction KanjiData(data) {\n  this.mode = Mode.KANJI;\n  this.data = data;\n}\nKanjiData.getBitsLength = function getBitsLength(length) {\n  return length * 13;\n};\nKanjiData.prototype.getLength = function getLength() {\n  return this.data.length;\n};\nKanjiData.prototype.getBitsLength = function getBitsLength() {\n  return KanjiData.getBitsLength(this.data.length);\n};\nKanjiData.prototype.write = function (bitBuffer) {\n  let i;\n\n  // In the Shift JIS system, Kanji characters are represented by a two byte combination.\n  // These byte values are shifted from the JIS X 0208 values.\n  // JIS X 0208 gives details of the shift coded representation.\n  for (i = 0; i < this.data.length; i++) {\n    let value = Utils.toSJIS(this.data[i]);\n\n    // For characters with Shift JIS values from 0x8140 to 0x9FFC:\n    if (value >= 0x8140 && value <= 0x9FFC) {\n      // Subtract 0x8140 from Shift JIS value\n      value -= 0x8140;\n\n      // For characters with Shift JIS values from 0xE040 to 0xEBBF\n    } else if (value >= 0xE040 && value <= 0xEBBF) {\n      // Subtract 0xC140 from Shift JIS value\n      value -= 0xC140;\n    } else {\n      throw new Error('Invalid SJIS character: ' + this.data[i] + '\\n' + 'Make sure your charset is UTF-8');\n    }\n\n    // Multiply most significant byte of result by 0xC0\n    // and add least significant byte to product\n    value = (value >>> 8 & 0xff) * 0xC0 + (value & 0xff);\n\n    // Convert result to a 13-bit binary string\n    bitBuffer.put(value, 13);\n  }\n};\nmodule.exports = KanjiData;", "map": {"version": 3, "names": ["Mode", "require", "Utils", "KanjiData", "data", "mode", "KANJI", "getBitsLength", "length", "prototype", "<PERSON><PERSON><PERSON><PERSON>", "write", "bitBuffer", "i", "value", "toSJIS", "Error", "put", "module", "exports"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/test 3/medical-assistant-react/node_modules/qrcode/lib/core/kanji-data.js"], "sourcesContent": ["const Mode = require('./mode')\nconst Utils = require('./utils')\n\nfunction KanjiData (data) {\n  this.mode = Mode.KANJI\n  this.data = data\n}\n\nKanjiData.getBitsLength = function getBitsLength (length) {\n  return length * 13\n}\n\nKanjiData.prototype.getLength = function getLength () {\n  return this.data.length\n}\n\nKanjiData.prototype.getBitsLength = function getBitsLength () {\n  return KanjiData.getBitsLength(this.data.length)\n}\n\nKanjiData.prototype.write = function (bitBuffer) {\n  let i\n\n  // In the Shift JIS system, Kanji characters are represented by a two byte combination.\n  // These byte values are shifted from the JIS X 0208 values.\n  // JIS X 0208 gives details of the shift coded representation.\n  for (i = 0; i < this.data.length; i++) {\n    let value = Utils.toSJIS(this.data[i])\n\n    // For characters with Shift JIS values from 0x8140 to 0x9FFC:\n    if (value >= 0x8140 && value <= 0x9FFC) {\n      // Subtract 0x8140 from Shift JIS value\n      value -= 0x8140\n\n    // For characters with Shift JIS values from 0xE040 to 0xEBBF\n    } else if (value >= 0xE040 && value <= 0xEBBF) {\n      // Subtract 0xC140 from Shift JIS value\n      value -= 0xC140\n    } else {\n      throw new Error(\n        'Invalid SJIS character: ' + this.data[i] + '\\n' +\n        'Make sure your charset is UTF-8')\n    }\n\n    // Multiply most significant byte of result by 0xC0\n    // and add least significant byte to product\n    value = (((value >>> 8) & 0xff) * 0xC0) + (value & 0xff)\n\n    // Convert result to a 13-bit binary string\n    bitBuffer.put(value, 13)\n  }\n}\n\nmodule.exports = KanjiData\n"], "mappings": "AAAA,MAAMA,IAAI,GAAGC,OAAO,CAAC,QAAQ,CAAC;AAC9B,MAAMC,KAAK,GAAGD,OAAO,CAAC,SAAS,CAAC;AAEhC,SAASE,SAASA,CAAEC,IAAI,EAAE;EACxB,IAAI,CAACC,IAAI,GAAGL,IAAI,CAACM,KAAK;EACtB,IAAI,CAACF,IAAI,GAAGA,IAAI;AAClB;AAEAD,SAAS,CAACI,aAAa,GAAG,SAASA,aAAaA,CAAEC,MAAM,EAAE;EACxD,OAAOA,MAAM,GAAG,EAAE;AACpB,CAAC;AAEDL,SAAS,CAACM,SAAS,CAACC,SAAS,GAAG,SAASA,SAASA,CAAA,EAAI;EACpD,OAAO,IAAI,CAACN,IAAI,CAACI,MAAM;AACzB,CAAC;AAEDL,SAAS,CAACM,SAAS,CAACF,aAAa,GAAG,SAASA,aAAaA,CAAA,EAAI;EAC5D,OAAOJ,SAAS,CAACI,aAAa,CAAC,IAAI,CAACH,IAAI,CAACI,MAAM,CAAC;AAClD,CAAC;AAEDL,SAAS,CAACM,SAAS,CAACE,KAAK,GAAG,UAAUC,SAAS,EAAE;EAC/C,IAAIC,CAAC;;EAEL;EACA;EACA;EACA,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACT,IAAI,CAACI,MAAM,EAAEK,CAAC,EAAE,EAAE;IACrC,IAAIC,KAAK,GAAGZ,KAAK,CAACa,MAAM,CAAC,IAAI,CAACX,IAAI,CAACS,CAAC,CAAC,CAAC;;IAEtC;IACA,IAAIC,KAAK,IAAI,MAAM,IAAIA,KAAK,IAAI,MAAM,EAAE;MACtC;MACAA,KAAK,IAAI,MAAM;;MAEjB;IACA,CAAC,MAAM,IAAIA,KAAK,IAAI,MAAM,IAAIA,KAAK,IAAI,MAAM,EAAE;MAC7C;MACAA,KAAK,IAAI,MAAM;IACjB,CAAC,MAAM;MACL,MAAM,IAAIE,KAAK,CACb,0BAA0B,GAAG,IAAI,CAACZ,IAAI,CAACS,CAAC,CAAC,GAAG,IAAI,GAChD,iCAAiC,CAAC;IACtC;;IAEA;IACA;IACAC,KAAK,GAAI,CAAEA,KAAK,KAAK,CAAC,GAAI,IAAI,IAAI,IAAI,IAAKA,KAAK,GAAG,IAAI,CAAC;;IAExD;IACAF,SAAS,CAACK,GAAG,CAACH,KAAK,EAAE,EAAE,CAAC;EAC1B;AACF,CAAC;AAEDI,MAAM,CAACC,OAAO,GAAGhB,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}