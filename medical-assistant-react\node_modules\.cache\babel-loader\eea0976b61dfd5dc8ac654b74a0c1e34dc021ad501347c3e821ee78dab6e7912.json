{"ast": null, "code": "/**\n * A set of attribute names that are always read/written as camel case.\n */\nconst camelCaseAttributes = new Set([\"baseFrequency\", \"diffuseConstant\", \"kernelMatrix\", \"kernelUnitLength\", \"keySplines\", \"keyTimes\", \"limitingConeAngle\", \"markerHeight\", \"markerWidth\", \"numOctaves\", \"targetX\", \"targetY\", \"surfaceScale\", \"specularConstant\", \"specularExponent\", \"stdDeviation\", \"tableValues\", \"viewBox\", \"gradientTransform\", \"pathLength\", \"startOffset\", \"textLength\", \"lengthAdjust\"]);\nexport { camelCaseAttributes };", "map": {"version": 3, "names": ["camelCaseAttributes", "Set"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/test 3/medical-assistant-react/node_modules/framer-motion/dist/es/render/svg/utils/camel-case-attrs.mjs"], "sourcesContent": ["/**\n * A set of attribute names that are always read/written as camel case.\n */\nconst camelCaseAttributes = new Set([\n    \"baseFrequency\",\n    \"diffuseConstant\",\n    \"kernelMatrix\",\n    \"kernelUnitLength\",\n    \"keySplines\",\n    \"keyTimes\",\n    \"limitingConeAngle\",\n    \"markerHeight\",\n    \"markerWidth\",\n    \"numOctaves\",\n    \"targetX\",\n    \"targetY\",\n    \"surfaceScale\",\n    \"specularConstant\",\n    \"specularExponent\",\n    \"stdDeviation\",\n    \"tableValues\",\n    \"viewBox\",\n    \"gradientTransform\",\n    \"pathLength\",\n    \"startOffset\",\n    \"textLength\",\n    \"lengthAdjust\",\n]);\n\nexport { camelCaseAttributes };\n"], "mappings": "AAAA;AACA;AACA;AACA,MAAMA,mBAAmB,GAAG,IAAIC,GAAG,CAAC,CAChC,eAAe,EACf,iBAAiB,EACjB,cAAc,EACd,kBAAkB,EAClB,YAAY,EACZ,UAAU,EACV,mBAAmB,EACnB,cAAc,EACd,aAAa,EACb,YAAY,EACZ,SAAS,EACT,SAAS,EACT,cAAc,EACd,kBAAkB,EAClB,kBAAkB,EAClB,cAAc,EACd,aAAa,EACb,SAAS,EACT,mBAAmB,EACnB,YAAY,EACZ,aAAa,EACb,YAAY,EACZ,cAAc,CACjB,CAAC;AAEF,SAASD,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}