import React from 'react';
import { Card, Row, Col } from 'react-bootstrap';

interface SkeletonLoaderProps {
  type: 'card' | 'list' | 'profile' | 'chat' | 'doctor-card';
  count?: number;
}

const SkeletonLoader: React.FC<SkeletonLoaderProps> = ({ type, count = 1 }) => {
  const renderSkeleton = () => {
    switch (type) {
      case 'doctor-card':
        return (
          <Card className="h-100 border-0 shadow-sm">
            <div className="loading-skeleton" style={{ height: '200px', borderRadius: '0.5rem 0.5rem 0 0' }} />
            <Card.Body className="p-4">
              <div className="loading-skeleton mb-2" style={{ height: '20px', width: '80%' }} />
              <div className="loading-skeleton mb-3" style={{ height: '16px', width: '60%' }} />
              <div className="d-flex justify-content-between mb-3">
                <div className="loading-skeleton" style={{ height: '16px', width: '40%' }} />
                <div className="loading-skeleton" style={{ height: '16px', width: '30%' }} />
              </div>
              <div className="loading-skeleton mb-3" style={{ height: '16px', width: '70%' }} />
              <div className="loading-skeleton" style={{ height: '40px', width: '100%' }} />
            </Card.Body>
          </Card>
        );

      case 'chat':
        return (
          <div className="d-flex mb-4">
            <div className="loading-skeleton rounded-circle me-3" style={{ width: '45px', height: '45px' }} />
            <div style={{ maxWidth: '70%' }}>
              <div className="loading-skeleton mb-2" style={{ height: '60px', width: '300px', borderRadius: '20px' }} />
              <div className="loading-skeleton" style={{ height: '12px', width: '80px' }} />
            </div>
          </div>
        );

      case 'profile':
        return (
          <Card className="border-0 shadow-sm">
            <Card.Body className="text-center p-4">
              <div className="loading-skeleton rounded-circle mx-auto mb-3" style={{ width: '100px', height: '100px' }} />
              <div className="loading-skeleton mb-2" style={{ height: '24px', width: '60%', margin: '0 auto' }} />
              <div className="loading-skeleton mb-3" style={{ height: '16px', width: '40%', margin: '0 auto' }} />
              <div className="loading-skeleton" style={{ height: '40px', width: '80%', margin: '0 auto' }} />
            </Card.Body>
          </Card>
        );

      case 'list':
        return (
          <div className="d-flex align-items-center p-3 border-bottom">
            <div className="loading-skeleton rounded-circle me-3" style={{ width: '50px', height: '50px' }} />
            <div className="flex-grow-1">
              <div className="loading-skeleton mb-2" style={{ height: '18px', width: '70%' }} />
              <div className="loading-skeleton" style={{ height: '14px', width: '50%' }} />
            </div>
            <div className="loading-skeleton" style={{ height: '32px', width: '80px' }} />
          </div>
        );

      case 'card':
      default:
        return (
          <Card className="border-0 shadow-sm">
            <div className="loading-skeleton" style={{ height: '200px', borderRadius: '0.5rem 0.5rem 0 0' }} />
            <Card.Body>
              <div className="loading-skeleton mb-3" style={{ height: '24px', width: '80%' }} />
              <div className="loading-skeleton mb-2" style={{ height: '16px', width: '100%' }} />
              <div className="loading-skeleton mb-2" style={{ height: '16px', width: '90%' }} />
              <div className="loading-skeleton" style={{ height: '16px', width: '60%' }} />
            </Card.Body>
          </Card>
        );
    }
  };

  return (
    <>
      {Array.from({ length: count }, (_, index) => (
        <div key={index} className={type === 'doctor-card' ? 'col-lg-4 col-md-6 mb-4' : 'mb-3'}>
          {renderSkeleton()}
        </div>
      ))}
      
      <style>{`
        .loading-skeleton {
          background: linear-gradient(90deg, var(--gray-200) 25%, var(--gray-100) 50%, var(--gray-200) 75%);
          background-size: 200% 100%;
          animation: loading 1.5s infinite;
          border-radius: 0.375rem;
        }

        @keyframes loading {
          0% {
            background-position: 200% 0;
          }
          100% {
            background-position: -200% 0;
          }
        }
      `}</style>
    </>
  );
};

export default SkeletonLoader;
