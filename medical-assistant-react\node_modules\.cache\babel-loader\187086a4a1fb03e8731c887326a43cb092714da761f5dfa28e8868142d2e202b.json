{"ast": null, "code": "import { isBezierDefinition } from '../../../easing/utils/is-bezier-definition.mjs';\nfunction isWaapiSupportedEasing(easing) {\n  return Boolean(!easing || typeof easing === \"string\" && supportedWaapiEasing[easing] || isBezierDefinition(easing) || Array.isArray(easing) && easing.every(isWaapiSupportedEasing));\n}\nconst cubicBezierAsString = _ref => {\n  let [a, b, c, d] = _ref;\n  return \"cubic-bezier(\".concat(a, \", \").concat(b, \", \").concat(c, \", \").concat(d, \")\");\n};\nconst supportedWaapiEasing = {\n  linear: \"linear\",\n  ease: \"ease\",\n  easeIn: \"ease-in\",\n  easeOut: \"ease-out\",\n  easeInOut: \"ease-in-out\",\n  circIn: cubicBezierAsString([0, 0.65, 0.55, 1]),\n  circOut: cubicBezierAsString([0.55, 0, 1, 0.45]),\n  backIn: cubicBezierAsString([0.31, 0.01, 0.66, -0.59]),\n  backOut: cubicBezierAsString([0.33, 1.53, 0.69, 0.99])\n};\nfunction mapEasingToNativeEasing(easing) {\n  if (!easing) return undefined;\n  return isBezierDefinition(easing) ? cubicBezierAsString(easing) : Array.isArray(easing) ? easing.map(mapEasingToNativeEasing) : supportedWaapiEasing[easing];\n}\nexport { cubicBezierAsString, isWaapiSupportedEasing, mapEasingToNativeEasing, supportedWaapiEasing };", "map": {"version": 3, "names": ["isBezierDefinition", "isWaapiSupportedEasing", "easing", "Boolean", "supportedWaapiEasing", "Array", "isArray", "every", "cubicBezierAsString", "_ref", "a", "b", "c", "d", "concat", "linear", "ease", "easeIn", "easeOut", "easeInOut", "circIn", "circOut", "backIn", "backOut", "mapEasingToNativeEasing", "undefined", "map"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/test 3/medical-assistant-react/node_modules/framer-motion/dist/es/animation/animators/waapi/easing.mjs"], "sourcesContent": ["import { isBezierDefinition } from '../../../easing/utils/is-bezier-definition.mjs';\n\nfunction isWaapiSupportedEasing(easing) {\n    return Boolean(!easing ||\n        (typeof easing === \"string\" && supportedWaapiEasing[easing]) ||\n        isBezierDefinition(easing) ||\n        (Array.isArray(easing) && easing.every(isWaapiSupportedEasing)));\n}\nconst cubicBezierAsString = ([a, b, c, d]) => `cubic-bezier(${a}, ${b}, ${c}, ${d})`;\nconst supportedWaapiEasing = {\n    linear: \"linear\",\n    ease: \"ease\",\n    easeIn: \"ease-in\",\n    easeOut: \"ease-out\",\n    easeInOut: \"ease-in-out\",\n    circIn: cubicBezierAsString([0, 0.65, 0.55, 1]),\n    circOut: cubicBezierAsString([0.55, 0, 1, 0.45]),\n    backIn: cubicBezierAsString([0.31, 0.01, 0.66, -0.59]),\n    backOut: cubicBezierAsString([0.33, 1.53, 0.69, 0.99]),\n};\nfunction mapEasingToNativeEasing(easing) {\n    if (!easing)\n        return undefined;\n    return isBezierDefinition(easing)\n        ? cubicBezierAsString(easing)\n        : Array.isArray(easing)\n            ? easing.map(mapEasingToNativeEasing)\n            : supportedWaapiEasing[easing];\n}\n\nexport { cubicBezierAsString, isWaapiSupportedEasing, mapEasingToNativeEasing, supportedWaapiEasing };\n"], "mappings": "AAAA,SAASA,kBAAkB,QAAQ,gDAAgD;AAEnF,SAASC,sBAAsBA,CAACC,MAAM,EAAE;EACpC,OAAOC,OAAO,CAAC,CAACD,MAAM,IACjB,OAAOA,MAAM,KAAK,QAAQ,IAAIE,oBAAoB,CAACF,MAAM,CAAE,IAC5DF,kBAAkB,CAACE,MAAM,CAAC,IACzBG,KAAK,CAACC,OAAO,CAACJ,MAAM,CAAC,IAAIA,MAAM,CAACK,KAAK,CAACN,sBAAsB,CAAE,CAAC;AACxE;AACA,MAAMO,mBAAmB,GAAGC,IAAA;EAAA,IAAC,CAACC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC,GAAAJ,IAAA;EAAA,uBAAAK,MAAA,CAAqBJ,CAAC,QAAAI,MAAA,CAAKH,CAAC,QAAAG,MAAA,CAAKF,CAAC,QAAAE,MAAA,CAAKD,CAAC;AAAA,CAAG;AACpF,MAAMT,oBAAoB,GAAG;EACzBW,MAAM,EAAE,QAAQ;EAChBC,IAAI,EAAE,MAAM;EACZC,MAAM,EAAE,SAAS;EACjBC,OAAO,EAAE,UAAU;EACnBC,SAAS,EAAE,aAAa;EACxBC,MAAM,EAAEZ,mBAAmB,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;EAC/Ca,OAAO,EAAEb,mBAAmB,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;EAChDc,MAAM,EAAEd,mBAAmB,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,IAAI,CAAC,CAAC;EACtDe,OAAO,EAAEf,mBAAmB,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;AACzD,CAAC;AACD,SAASgB,uBAAuBA,CAACtB,MAAM,EAAE;EACrC,IAAI,CAACA,MAAM,EACP,OAAOuB,SAAS;EACpB,OAAOzB,kBAAkB,CAACE,MAAM,CAAC,GAC3BM,mBAAmB,CAACN,MAAM,CAAC,GAC3BG,KAAK,CAACC,OAAO,CAACJ,MAAM,CAAC,GACjBA,MAAM,CAACwB,GAAG,CAACF,uBAAuB,CAAC,GACnCpB,oBAAoB,CAACF,MAAM,CAAC;AAC1C;AAEA,SAASM,mBAAmB,EAAEP,sBAAsB,EAAEuB,uBAAuB,EAAEpB,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}