import React from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faStethoscope, faBrain } from '@fortawesome/free-solid-svg-icons';

const TypingIndicator: React.FC = () => {
  return (
    <div className="d-flex mb-4 animate-fadeIn">
      {/* Avatar */}
      <div
        className="rounded-circle d-flex align-items-center justify-content-center text-white me-3 animate-pulse-medical"
        style={{
          width: '45px',
          height: '45px',
          background: 'var(--medical-gradient-secondary)',
          flexShrink: 0,
          boxShadow: '0 4px 15px rgba(34, 197, 94, 0.3)',
          border: '3px solid white'
        }}
      >
        <FontAwesomeIcon icon={faStethoscope} />
      </div>

      {/* Typing Animation */}
      <div
        className="bg-white p-4 rounded-medical shadow-sm border position-relative"
        style={{
          maxWidth: '75%',
          border: '2px solid var(--medical-primary-100)'
        }}
      >
        <div className="d-flex align-items-center">
          <div className="typing-dots me-3">
            <span></span>
            <span></span>
            <span></span>
          </div>
          <div>
            <div className="text-primary fw-medium mb-1">
              <FontAwesomeIcon icon={faBrain} className="me-2" />
              المساعد الطبي يحلل...
            </div>
            <small className="text-muted">جاري معالجة أعراضك بالذكاء الاصطناعي</small>
          </div>
        </div>

        {/* Progress bar */}
        <div className="mt-3">
          <div className="progress" style={{ height: '3px' }}>
            <div
              className="progress-bar bg-primary progress-bar-animated progress-bar-striped"
              style={{ width: '100%' }}
            ></div>
          </div>
        </div>

        {/* Message tail */}
        <div
          className="position-absolute"
          style={{
            bottom: '15px',
            left: '-8px',
            width: '0',
            height: '0',
            borderTop: '8px solid transparent',
            borderBottom: '8px solid transparent',
            borderRight: '8px solid white'
          }}
        />
      </div>

      <style>{`
        .typing-dots {
          display: flex;
          gap: 6px;
          align-items: center;
        }

        .typing-dots span {
          width: 10px;
          height: 10px;
          border-radius: 50%;
          background: var(--medical-primary-500);
          animation: typing 1.6s infinite ease-in-out;
          box-shadow: 0 2px 4px rgba(14, 165, 233, 0.3);
        }

        .typing-dots span:nth-child(1) {
          animation-delay: -0.32s;
        }

        .typing-dots span:nth-child(2) {
          animation-delay: -0.16s;
        }

        .typing-dots span:nth-child(3) {
          animation-delay: 0s;
        }

        @keyframes typing {
          0%, 80%, 100% {
            transform: scale(0.8);
            opacity: 0.5;
          }
          40% {
            transform: scale(1.2);
            opacity: 1;
          }
        }

        .progress-bar-animated {
          animation: progress-bar-stripes 1s linear infinite;
        }

        @keyframes progress-bar-stripes {
          0% {
            background-position: 1rem 0;
          }
          100% {
            background-position: 0 0;
          }
        }
      `}</style>
    </div>
  );
};

export default TypingIndicator;
