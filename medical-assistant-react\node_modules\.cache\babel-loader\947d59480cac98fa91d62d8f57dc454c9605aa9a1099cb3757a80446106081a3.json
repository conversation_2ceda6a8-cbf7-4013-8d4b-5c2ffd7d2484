{"ast": null, "code": "const VersionCheck = require('./version-check');\nconst Regex = require('./regex');\n\n/**\n * Numeric mode encodes data from the decimal digit set (0 - 9)\n * (byte values 30HEX to 39HEX).\n * Normally, 3 data characters are represented by 10 bits.\n *\n * @type {Object}\n */\nexports.NUMERIC = {\n  id: 'Numeric',\n  bit: 1 << 0,\n  ccBits: [10, 12, 14]\n};\n\n/**\n * Alphanumeric mode encodes data from a set of 45 characters,\n * i.e. 10 numeric digits (0 - 9),\n *      26 alphabetic characters (A - Z),\n *   and 9 symbols (SP, $, %, *, +, -, ., /, :).\n * Normally, two input characters are represented by 11 bits.\n *\n * @type {Object}\n */\nexports.ALPHANUMERIC = {\n  id: 'Alphanumeric',\n  bit: 1 << 1,\n  ccBits: [9, 11, 13]\n};\n\n/**\n * In byte mode, data is encoded at 8 bits per character.\n *\n * @type {Object}\n */\nexports.BYTE = {\n  id: 'Byte',\n  bit: 1 << 2,\n  ccBits: [8, 16, 16]\n};\n\n/**\n * The Kanji mode efficiently encodes Kanji characters in accordance with\n * the Shift JIS system based on JIS X 0208.\n * The Shift JIS values are shifted from the JIS X 0208 values.\n * JIS X 0208 gives details of the shift coded representation.\n * Each two-byte character value is compacted to a 13-bit binary codeword.\n *\n * @type {Object}\n */\nexports.KANJI = {\n  id: 'Kanji',\n  bit: 1 << 3,\n  ccBits: [8, 10, 12]\n};\n\n/**\n * Mixed mode will contain a sequences of data in a combination of any of\n * the modes described above\n *\n * @type {Object}\n */\nexports.MIXED = {\n  bit: -1\n};\n\n/**\n * Returns the number of bits needed to store the data length\n * according to QR Code specifications.\n *\n * @param  {Mode}   mode    Data mode\n * @param  {Number} version QR Code version\n * @return {Number}         Number of bits\n */\nexports.getCharCountIndicator = function getCharCountIndicator(mode, version) {\n  if (!mode.ccBits) throw new Error('Invalid mode: ' + mode);\n  if (!VersionCheck.isValid(version)) {\n    throw new Error('Invalid version: ' + version);\n  }\n  if (version >= 1 && version < 10) return mode.ccBits[0];else if (version < 27) return mode.ccBits[1];\n  return mode.ccBits[2];\n};\n\n/**\n * Returns the most efficient mode to store the specified data\n *\n * @param  {String} dataStr Input data string\n * @return {Mode}           Best mode\n */\nexports.getBestModeForData = function getBestModeForData(dataStr) {\n  if (Regex.testNumeric(dataStr)) return exports.NUMERIC;else if (Regex.testAlphanumeric(dataStr)) return exports.ALPHANUMERIC;else if (Regex.testKanji(dataStr)) return exports.KANJI;else return exports.BYTE;\n};\n\n/**\n * Return mode name as string\n *\n * @param {Mode} mode Mode object\n * @returns {String}  Mode name\n */\nexports.toString = function toString(mode) {\n  if (mode && mode.id) return mode.id;\n  throw new Error('Invalid mode');\n};\n\n/**\n * Check if input param is a valid mode object\n *\n * @param   {Mode}    mode Mode object\n * @returns {Boolean} True if valid mode, false otherwise\n */\nexports.isValid = function isValid(mode) {\n  return mode && mode.bit && mode.ccBits;\n};\n\n/**\n * Get mode object from its name\n *\n * @param   {String} string Mode name\n * @returns {Mode}          Mode object\n */\nfunction fromString(string) {\n  if (typeof string !== 'string') {\n    throw new Error('Param is not a string');\n  }\n  const lcStr = string.toLowerCase();\n  switch (lcStr) {\n    case 'numeric':\n      return exports.NUMERIC;\n    case 'alphanumeric':\n      return exports.ALPHANUMERIC;\n    case 'kanji':\n      return exports.KANJI;\n    case 'byte':\n      return exports.BYTE;\n    default:\n      throw new Error('Unknown mode: ' + string);\n  }\n}\n\n/**\n * Returns mode from a value.\n * If value is not a valid mode, returns defaultValue\n *\n * @param  {Mode|String} value        Encoding mode\n * @param  {Mode}        defaultValue Fallback value\n * @return {Mode}                     Encoding mode\n */\nexports.from = function from(value, defaultValue) {\n  if (exports.isValid(value)) {\n    return value;\n  }\n  try {\n    return fromString(value);\n  } catch (e) {\n    return defaultValue;\n  }\n};", "map": {"version": 3, "names": ["VersionCheck", "require", "Regex", "exports", "NUMERIC", "id", "bit", "ccBits", "ALPHANUMERIC", "BYTE", "KANJI", "MIXED", "getCharCountIndicator", "mode", "version", "Error", "<PERSON><PERSON><PERSON><PERSON>", "getBestModeForData", "dataStr", "testNumeric", "testAlphanumeric", "<PERSON><PERSON><PERSON><PERSON>", "toString", "fromString", "string", "lcStr", "toLowerCase", "from", "value", "defaultValue", "e"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/test 3/medical-assistant-react/node_modules/qrcode/lib/core/mode.js"], "sourcesContent": ["const VersionCheck = require('./version-check')\nconst Regex = require('./regex')\n\n/**\n * Numeric mode encodes data from the decimal digit set (0 - 9)\n * (byte values 30HEX to 39HEX).\n * Normally, 3 data characters are represented by 10 bits.\n *\n * @type {Object}\n */\nexports.NUMERIC = {\n  id: 'Numeric',\n  bit: 1 << 0,\n  ccBits: [10, 12, 14]\n}\n\n/**\n * Alphanumeric mode encodes data from a set of 45 characters,\n * i.e. 10 numeric digits (0 - 9),\n *      26 alphabetic characters (A - Z),\n *   and 9 symbols (SP, $, %, *, +, -, ., /, :).\n * Normally, two input characters are represented by 11 bits.\n *\n * @type {Object}\n */\nexports.ALPHANUMERIC = {\n  id: 'Alphanumeric',\n  bit: 1 << 1,\n  ccBits: [9, 11, 13]\n}\n\n/**\n * In byte mode, data is encoded at 8 bits per character.\n *\n * @type {Object}\n */\nexports.BYTE = {\n  id: 'Byte',\n  bit: 1 << 2,\n  ccBits: [8, 16, 16]\n}\n\n/**\n * The Kanji mode efficiently encodes Kanji characters in accordance with\n * the Shift JIS system based on JIS X 0208.\n * The Shift JIS values are shifted from the JIS X 0208 values.\n * JIS X 0208 gives details of the shift coded representation.\n * Each two-byte character value is compacted to a 13-bit binary codeword.\n *\n * @type {Object}\n */\nexports.KANJI = {\n  id: 'Kanji',\n  bit: 1 << 3,\n  ccBits: [8, 10, 12]\n}\n\n/**\n * Mixed mode will contain a sequences of data in a combination of any of\n * the modes described above\n *\n * @type {Object}\n */\nexports.MIXED = {\n  bit: -1\n}\n\n/**\n * Returns the number of bits needed to store the data length\n * according to QR Code specifications.\n *\n * @param  {Mode}   mode    Data mode\n * @param  {Number} version QR Code version\n * @return {Number}         Number of bits\n */\nexports.getCharCountIndicator = function getCharCountIndicator (mode, version) {\n  if (!mode.ccBits) throw new Error('Invalid mode: ' + mode)\n\n  if (!VersionCheck.isValid(version)) {\n    throw new Error('Invalid version: ' + version)\n  }\n\n  if (version >= 1 && version < 10) return mode.ccBits[0]\n  else if (version < 27) return mode.ccBits[1]\n  return mode.ccBits[2]\n}\n\n/**\n * Returns the most efficient mode to store the specified data\n *\n * @param  {String} dataStr Input data string\n * @return {Mode}           Best mode\n */\nexports.getBestModeForData = function getBestModeForData (dataStr) {\n  if (Regex.testNumeric(dataStr)) return exports.NUMERIC\n  else if (Regex.testAlphanumeric(dataStr)) return exports.ALPHANUMERIC\n  else if (Regex.testKanji(dataStr)) return exports.KANJI\n  else return exports.BYTE\n}\n\n/**\n * Return mode name as string\n *\n * @param {Mode} mode Mode object\n * @returns {String}  Mode name\n */\nexports.toString = function toString (mode) {\n  if (mode && mode.id) return mode.id\n  throw new Error('Invalid mode')\n}\n\n/**\n * Check if input param is a valid mode object\n *\n * @param   {Mode}    mode Mode object\n * @returns {Boolean} True if valid mode, false otherwise\n */\nexports.isValid = function isValid (mode) {\n  return mode && mode.bit && mode.ccBits\n}\n\n/**\n * Get mode object from its name\n *\n * @param   {String} string Mode name\n * @returns {Mode}          Mode object\n */\nfunction fromString (string) {\n  if (typeof string !== 'string') {\n    throw new Error('Param is not a string')\n  }\n\n  const lcStr = string.toLowerCase()\n\n  switch (lcStr) {\n    case 'numeric':\n      return exports.NUMERIC\n    case 'alphanumeric':\n      return exports.ALPHANUMERIC\n    case 'kanji':\n      return exports.KANJI\n    case 'byte':\n      return exports.BYTE\n    default:\n      throw new Error('Unknown mode: ' + string)\n  }\n}\n\n/**\n * Returns mode from a value.\n * If value is not a valid mode, returns defaultValue\n *\n * @param  {Mode|String} value        Encoding mode\n * @param  {Mode}        defaultValue Fallback value\n * @return {Mode}                     Encoding mode\n */\nexports.from = function from (value, defaultValue) {\n  if (exports.isValid(value)) {\n    return value\n  }\n\n  try {\n    return fromString(value)\n  } catch (e) {\n    return defaultValue\n  }\n}\n"], "mappings": "AAAA,MAAMA,YAAY,GAAGC,OAAO,CAAC,iBAAiB,CAAC;AAC/C,MAAMC,KAAK,GAAGD,OAAO,CAAC,SAAS,CAAC;;AAEhC;AACA;AACA;AACA;AACA;AACA;AACA;AACAE,OAAO,CAACC,OAAO,GAAG;EAChBC,EAAE,EAAE,SAAS;EACbC,GAAG,EAAE,CAAC,IAAI,CAAC;EACXC,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE;AACrB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAJ,OAAO,CAACK,YAAY,GAAG;EACrBH,EAAE,EAAE,cAAc;EAClBC,GAAG,EAAE,CAAC,IAAI,CAAC;EACXC,MAAM,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE;AACpB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACAJ,OAAO,CAACM,IAAI,GAAG;EACbJ,EAAE,EAAE,MAAM;EACVC,GAAG,EAAE,CAAC,IAAI,CAAC;EACXC,MAAM,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE;AACpB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAJ,OAAO,CAACO,KAAK,GAAG;EACdL,EAAE,EAAE,OAAO;EACXC,GAAG,EAAE,CAAC,IAAI,CAAC;EACXC,MAAM,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE;AACpB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACAJ,OAAO,CAACQ,KAAK,GAAG;EACdL,GAAG,EAAE,CAAC;AACR,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAH,OAAO,CAACS,qBAAqB,GAAG,SAASA,qBAAqBA,CAAEC,IAAI,EAAEC,OAAO,EAAE;EAC7E,IAAI,CAACD,IAAI,CAACN,MAAM,EAAE,MAAM,IAAIQ,KAAK,CAAC,gBAAgB,GAAGF,IAAI,CAAC;EAE1D,IAAI,CAACb,YAAY,CAACgB,OAAO,CAACF,OAAO,CAAC,EAAE;IAClC,MAAM,IAAIC,KAAK,CAAC,mBAAmB,GAAGD,OAAO,CAAC;EAChD;EAEA,IAAIA,OAAO,IAAI,CAAC,IAAIA,OAAO,GAAG,EAAE,EAAE,OAAOD,IAAI,CAACN,MAAM,CAAC,CAAC,CAAC,MAClD,IAAIO,OAAO,GAAG,EAAE,EAAE,OAAOD,IAAI,CAACN,MAAM,CAAC,CAAC,CAAC;EAC5C,OAAOM,IAAI,CAACN,MAAM,CAAC,CAAC,CAAC;AACvB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACAJ,OAAO,CAACc,kBAAkB,GAAG,SAASA,kBAAkBA,CAAEC,OAAO,EAAE;EACjE,IAAIhB,KAAK,CAACiB,WAAW,CAACD,OAAO,CAAC,EAAE,OAAOf,OAAO,CAACC,OAAO,MACjD,IAAIF,KAAK,CAACkB,gBAAgB,CAACF,OAAO,CAAC,EAAE,OAAOf,OAAO,CAACK,YAAY,MAChE,IAAIN,KAAK,CAACmB,SAAS,CAACH,OAAO,CAAC,EAAE,OAAOf,OAAO,CAACO,KAAK,MAClD,OAAOP,OAAO,CAACM,IAAI;AAC1B,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACAN,OAAO,CAACmB,QAAQ,GAAG,SAASA,QAAQA,CAAET,IAAI,EAAE;EAC1C,IAAIA,IAAI,IAAIA,IAAI,CAACR,EAAE,EAAE,OAAOQ,IAAI,CAACR,EAAE;EACnC,MAAM,IAAIU,KAAK,CAAC,cAAc,CAAC;AACjC,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACAZ,OAAO,CAACa,OAAO,GAAG,SAASA,OAAOA,CAAEH,IAAI,EAAE;EACxC,OAAOA,IAAI,IAAIA,IAAI,CAACP,GAAG,IAAIO,IAAI,CAACN,MAAM;AACxC,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,SAASgB,UAAUA,CAAEC,MAAM,EAAE;EAC3B,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;IAC9B,MAAM,IAAIT,KAAK,CAAC,uBAAuB,CAAC;EAC1C;EAEA,MAAMU,KAAK,GAAGD,MAAM,CAACE,WAAW,CAAC,CAAC;EAElC,QAAQD,KAAK;IACX,KAAK,SAAS;MACZ,OAAOtB,OAAO,CAACC,OAAO;IACxB,KAAK,cAAc;MACjB,OAAOD,OAAO,CAACK,YAAY;IAC7B,KAAK,OAAO;MACV,OAAOL,OAAO,CAACO,KAAK;IACtB,KAAK,MAAM;MACT,OAAOP,OAAO,CAACM,IAAI;IACrB;MACE,MAAM,IAAIM,KAAK,CAAC,gBAAgB,GAAGS,MAAM,CAAC;EAC9C;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACArB,OAAO,CAACwB,IAAI,GAAG,SAASA,IAAIA,CAAEC,KAAK,EAAEC,YAAY,EAAE;EACjD,IAAI1B,OAAO,CAACa,OAAO,CAACY,KAAK,CAAC,EAAE;IAC1B,OAAOA,KAAK;EACd;EAEA,IAAI;IACF,OAAOL,UAAU,CAACK,KAAK,CAAC;EAC1B,CAAC,CAAC,OAAOE,CAAC,EAAE;IACV,OAAOD,YAAY;EACrB;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}