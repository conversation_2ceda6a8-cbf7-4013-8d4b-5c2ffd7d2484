{"ast": null, "code": "var supportedTransforms = /^((translate|rotate|scale)(X|Y|Z|3d)?|matrix(3d)?|perspective|skew(X|Y)?)$/i;\nexport default function isTransform(value) {\n  return !!(value && supportedTransforms.test(value));\n}", "map": {"version": 3, "names": ["supportedTransforms", "isTransform", "value", "test"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/test 3/medical-assistant-react/node_modules/dom-helpers/esm/isTransform.js"], "sourcesContent": ["var supportedTransforms = /^((translate|rotate|scale)(X|Y|Z|3d)?|matrix(3d)?|perspective|skew(X|Y)?)$/i;\nexport default function isTransform(value) {\n  return !!(value && supportedTransforms.test(value));\n}"], "mappings": "AAAA,IAAIA,mBAAmB,GAAG,6EAA6E;AACvG,eAAe,SAASC,WAAWA,CAACC,KAAK,EAAE;EACzC,OAAO,CAAC,EAAEA,KAAK,IAAIF,mBAAmB,CAACG,IAAI,CAACD,KAAK,CAAC,CAAC;AACrD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}