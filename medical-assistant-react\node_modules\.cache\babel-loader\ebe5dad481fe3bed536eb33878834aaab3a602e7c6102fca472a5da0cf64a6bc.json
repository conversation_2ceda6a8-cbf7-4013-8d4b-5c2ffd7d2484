{"ast": null, "code": "/**\n * Alignment pattern are fixed reference pattern in defined positions\n * in a matrix symbology, which enables the decode software to re-synchronise\n * the coordinate mapping of the image modules in the event of moderate amounts\n * of distortion of the image.\n *\n * Alignment patterns are present only in QR Code symbols of version 2 or larger\n * and their number depends on the symbol version.\n */\n\nconst getSymbolSize = require('./utils').getSymbolSize;\n\n/**\n * Calculate the row/column coordinates of the center module of each alignment pattern\n * for the specified QR Code version.\n *\n * The alignment patterns are positioned symmetrically on either side of the diagonal\n * running from the top left corner of the symbol to the bottom right corner.\n *\n * Since positions are simmetrical only half of the coordinates are returned.\n * Each item of the array will represent in turn the x and y coordinate.\n * @see {@link getPositions}\n *\n * @param  {Number} version QR Code version\n * @return {Array}          Array of coordinate\n */\nexports.getRowColCoords = function getRowColCoords(version) {\n  if (version === 1) return [];\n  const posCount = Math.floor(version / 7) + 2;\n  const size = getSymbolSize(version);\n  const intervals = size === 145 ? 26 : Math.ceil((size - 13) / (2 * posCount - 2)) * 2;\n  const positions = [size - 7]; // Last coord is always (size - 7)\n\n  for (let i = 1; i < posCount - 1; i++) {\n    positions[i] = positions[i - 1] - intervals;\n  }\n  positions.push(6); // First coord is always 6\n\n  return positions.reverse();\n};\n\n/**\n * Returns an array containing the positions of each alignment pattern.\n * Each array's element represent the center point of the pattern as (x, y) coordinates\n *\n * Coordinates are calculated expanding the row/column coordinates returned by {@link getRowColCoords}\n * and filtering out the items that overlaps with finder pattern\n *\n * @example\n * For a Version 7 symbol {@link getRowColCoords} returns values 6, 22 and 38.\n * The alignment patterns, therefore, are to be centered on (row, column)\n * positions (6,22), (22,6), (22,22), (22,38), (38,22), (38,38).\n * Note that the coordinates (6,6), (6,38), (38,6) are occupied by finder patterns\n * and are not therefore used for alignment patterns.\n *\n * let pos = getPositions(7)\n * // [[6,22], [22,6], [22,22], [22,38], [38,22], [38,38]]\n *\n * @param  {Number} version QR Code version\n * @return {Array}          Array of coordinates\n */\nexports.getPositions = function getPositions(version) {\n  const coords = [];\n  const pos = exports.getRowColCoords(version);\n  const posLength = pos.length;\n  for (let i = 0; i < posLength; i++) {\n    for (let j = 0; j < posLength; j++) {\n      // Skip if position is occupied by finder patterns\n      if (i === 0 && j === 0 ||\n      // top-left\n      i === 0 && j === posLength - 1 ||\n      // bottom-left\n      i === posLength - 1 && j === 0) {\n        // top-right\n        continue;\n      }\n      coords.push([pos[i], pos[j]]);\n    }\n  }\n  return coords;\n};", "map": {"version": 3, "names": ["getSymbolSize", "require", "exports", "getRowColCoords", "version", "posCount", "Math", "floor", "size", "intervals", "ceil", "positions", "i", "push", "reverse", "getPositions", "coords", "pos", "pos<PERSON><PERSON><PERSON>", "length", "j"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/test 3/medical-assistant-react/node_modules/qrcode/lib/core/alignment-pattern.js"], "sourcesContent": ["/**\n * Alignment pattern are fixed reference pattern in defined positions\n * in a matrix symbology, which enables the decode software to re-synchronise\n * the coordinate mapping of the image modules in the event of moderate amounts\n * of distortion of the image.\n *\n * Alignment patterns are present only in QR Code symbols of version 2 or larger\n * and their number depends on the symbol version.\n */\n\nconst getSymbolSize = require('./utils').getSymbolSize\n\n/**\n * Calculate the row/column coordinates of the center module of each alignment pattern\n * for the specified QR Code version.\n *\n * The alignment patterns are positioned symmetrically on either side of the diagonal\n * running from the top left corner of the symbol to the bottom right corner.\n *\n * Since positions are simmetrical only half of the coordinates are returned.\n * Each item of the array will represent in turn the x and y coordinate.\n * @see {@link getPositions}\n *\n * @param  {Number} version QR Code version\n * @return {Array}          Array of coordinate\n */\nexports.getRowColCoords = function getRowColCoords (version) {\n  if (version === 1) return []\n\n  const posCount = Math.floor(version / 7) + 2\n  const size = getSymbolSize(version)\n  const intervals = size === 145 ? 26 : Math.ceil((size - 13) / (2 * posCount - 2)) * 2\n  const positions = [size - 7] // Last coord is always (size - 7)\n\n  for (let i = 1; i < posCount - 1; i++) {\n    positions[i] = positions[i - 1] - intervals\n  }\n\n  positions.push(6) // First coord is always 6\n\n  return positions.reverse()\n}\n\n/**\n * Returns an array containing the positions of each alignment pattern.\n * Each array's element represent the center point of the pattern as (x, y) coordinates\n *\n * Coordinates are calculated expanding the row/column coordinates returned by {@link getRowColCoords}\n * and filtering out the items that overlaps with finder pattern\n *\n * @example\n * For a Version 7 symbol {@link getRowColCoords} returns values 6, 22 and 38.\n * The alignment patterns, therefore, are to be centered on (row, column)\n * positions (6,22), (22,6), (22,22), (22,38), (38,22), (38,38).\n * Note that the coordinates (6,6), (6,38), (38,6) are occupied by finder patterns\n * and are not therefore used for alignment patterns.\n *\n * let pos = getPositions(7)\n * // [[6,22], [22,6], [22,22], [22,38], [38,22], [38,38]]\n *\n * @param  {Number} version QR Code version\n * @return {Array}          Array of coordinates\n */\nexports.getPositions = function getPositions (version) {\n  const coords = []\n  const pos = exports.getRowColCoords(version)\n  const posLength = pos.length\n\n  for (let i = 0; i < posLength; i++) {\n    for (let j = 0; j < posLength; j++) {\n      // Skip if position is occupied by finder patterns\n      if ((i === 0 && j === 0) || // top-left\n          (i === 0 && j === posLength - 1) || // bottom-left\n          (i === posLength - 1 && j === 0)) { // top-right\n        continue\n      }\n\n      coords.push([pos[i], pos[j]])\n    }\n  }\n\n  return coords\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,MAAMA,aAAa,GAAGC,OAAO,CAAC,SAAS,CAAC,CAACD,aAAa;;AAEtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAE,OAAO,CAACC,eAAe,GAAG,SAASA,eAAeA,CAAEC,OAAO,EAAE;EAC3D,IAAIA,OAAO,KAAK,CAAC,EAAE,OAAO,EAAE;EAE5B,MAAMC,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,GAAG,CAAC,CAAC,GAAG,CAAC;EAC5C,MAAMI,IAAI,GAAGR,aAAa,CAACI,OAAO,CAAC;EACnC,MAAMK,SAAS,GAAGD,IAAI,KAAK,GAAG,GAAG,EAAE,GAAGF,IAAI,CAACI,IAAI,CAAC,CAACF,IAAI,GAAG,EAAE,KAAK,CAAC,GAAGH,QAAQ,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;EACrF,MAAMM,SAAS,GAAG,CAACH,IAAI,GAAG,CAAC,CAAC,EAAC;;EAE7B,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,QAAQ,GAAG,CAAC,EAAEO,CAAC,EAAE,EAAE;IACrCD,SAAS,CAACC,CAAC,CAAC,GAAGD,SAAS,CAACC,CAAC,GAAG,CAAC,CAAC,GAAGH,SAAS;EAC7C;EAEAE,SAAS,CAACE,IAAI,CAAC,CAAC,CAAC,EAAC;;EAElB,OAAOF,SAAS,CAACG,OAAO,CAAC,CAAC;AAC5B,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAZ,OAAO,CAACa,YAAY,GAAG,SAASA,YAAYA,CAAEX,OAAO,EAAE;EACrD,MAAMY,MAAM,GAAG,EAAE;EACjB,MAAMC,GAAG,GAAGf,OAAO,CAACC,eAAe,CAACC,OAAO,CAAC;EAC5C,MAAMc,SAAS,GAAGD,GAAG,CAACE,MAAM;EAE5B,KAAK,IAAIP,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGM,SAAS,EAAEN,CAAC,EAAE,EAAE;IAClC,KAAK,IAAIQ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,SAAS,EAAEE,CAAC,EAAE,EAAE;MAClC;MACA,IAAKR,CAAC,KAAK,CAAC,IAAIQ,CAAC,KAAK,CAAC;MAAK;MACvBR,CAAC,KAAK,CAAC,IAAIQ,CAAC,KAAKF,SAAS,GAAG,CAAE;MAAI;MACnCN,CAAC,KAAKM,SAAS,GAAG,CAAC,IAAIE,CAAC,KAAK,CAAE,EAAE;QAAE;QACtC;MACF;MAEAJ,MAAM,CAACH,IAAI,CAAC,CAACI,GAAG,CAACL,CAAC,CAAC,EAAEK,GAAG,CAACG,CAAC,CAAC,CAAC,CAAC;IAC/B;EACF;EAEA,OAAOJ,MAAM;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}