{"ast": null, "code": "const ECLevel = require('./error-correction-level');\nconst EC_BLOCKS_TABLE = [\n// L  M  Q  H\n1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2, 2, 1, 2, 2, 4, 1, 2, 4, 4, 2, 4, 4, 4, 2, 4, 6, 5, 2, 4, 6, 6, 2, 5, 8, 8, 4, 5, 8, 8, 4, 5, 8, 11, 4, 8, 10, 11, 4, 9, 12, 16, 4, 9, 16, 16, 6, 10, 12, 18, 6, 10, 17, 16, 6, 11, 16, 19, 6, 13, 18, 21, 7, 14, 21, 25, 8, 16, 20, 25, 8, 17, 23, 25, 9, 17, 23, 34, 9, 18, 25, 30, 10, 20, 27, 32, 12, 21, 29, 35, 12, 23, 34, 37, 12, 25, 34, 40, 13, 26, 35, 42, 14, 28, 38, 45, 15, 29, 40, 48, 16, 31, 43, 51, 17, 33, 45, 54, 18, 35, 48, 57, 19, 37, 51, 60, 19, 38, 53, 63, 20, 40, 56, 66, 21, 43, 59, 70, 22, 45, 62, 74, 24, 47, 65, 77, 25, 49, 68, 81];\nconst EC_CODEWORDS_TABLE = [\n// L  M  Q  H\n7, 10, 13, 17, 10, 16, 22, 28, 15, 26, 36, 44, 20, 36, 52, 64, 26, 48, 72, 88, 36, 64, 96, 112, 40, 72, 108, 130, 48, 88, 132, 156, 60, 110, 160, 192, 72, 130, 192, 224, 80, 150, 224, 264, 96, 176, 260, 308, 104, 198, 288, 352, 120, 216, 320, 384, 132, 240, 360, 432, 144, 280, 408, 480, 168, 308, 448, 532, 180, 338, 504, 588, 196, 364, 546, 650, 224, 416, 600, 700, 224, 442, 644, 750, 252, 476, 690, 816, 270, 504, 750, 900, 300, 560, 810, 960, 312, 588, 870, 1050, 336, 644, 952, 1110, 360, 700, 1020, 1200, 390, 728, 1050, 1260, 420, 784, 1140, 1350, 450, 812, 1200, 1440, 480, 868, 1290, 1530, 510, 924, 1350, 1620, 540, 980, 1440, 1710, 570, 1036, 1530, 1800, 570, 1064, 1590, 1890, 600, 1120, 1680, 1980, 630, 1204, 1770, 2100, 660, 1260, 1860, 2220, 720, 1316, 1950, 2310, 750, 1372, 2040, 2430];\n\n/**\r\n * Returns the number of error correction block that the QR Code should contain\r\n * for the specified version and error correction level.\r\n *\r\n * @param  {Number} version              QR Code version\r\n * @param  {Number} errorCorrectionLevel Error correction level\r\n * @return {Number}                      Number of error correction blocks\r\n */\nexports.getBlocksCount = function getBlocksCount(version, errorCorrectionLevel) {\n  switch (errorCorrectionLevel) {\n    case ECLevel.L:\n      return EC_BLOCKS_TABLE[(version - 1) * 4 + 0];\n    case ECLevel.M:\n      return EC_BLOCKS_TABLE[(version - 1) * 4 + 1];\n    case ECLevel.Q:\n      return EC_BLOCKS_TABLE[(version - 1) * 4 + 2];\n    case ECLevel.H:\n      return EC_BLOCKS_TABLE[(version - 1) * 4 + 3];\n    default:\n      return undefined;\n  }\n};\n\n/**\r\n * Returns the number of error correction codewords to use for the specified\r\n * version and error correction level.\r\n *\r\n * @param  {Number} version              QR Code version\r\n * @param  {Number} errorCorrectionLevel Error correction level\r\n * @return {Number}                      Number of error correction codewords\r\n */\nexports.getTotalCodewordsCount = function getTotalCodewordsCount(version, errorCorrectionLevel) {\n  switch (errorCorrectionLevel) {\n    case ECLevel.L:\n      return EC_CODEWORDS_TABLE[(version - 1) * 4 + 0];\n    case ECLevel.M:\n      return EC_CODEWORDS_TABLE[(version - 1) * 4 + 1];\n    case ECLevel.Q:\n      return EC_CODEWORDS_TABLE[(version - 1) * 4 + 2];\n    case ECLevel.H:\n      return EC_CODEWORDS_TABLE[(version - 1) * 4 + 3];\n    default:\n      return undefined;\n  }\n};", "map": {"version": 3, "names": ["ECLevel", "require", "EC_BLOCKS_TABLE", "EC_CODEWORDS_TABLE", "exports", "getBlocksCount", "version", "errorCorrectionLevel", "L", "M", "Q", "H", "undefined", "getTotalCodewordsCount"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/test 3/medical-assistant-react/node_modules/qrcode/lib/core/error-correction-code.js"], "sourcesContent": ["const ECLevel = require('./error-correction-level')\r\n\r\nconst EC_BLOCKS_TABLE = [\r\n// L  M  Q  H\r\n  1, 1, 1, 1,\r\n  1, 1, 1, 1,\r\n  1, 1, 2, 2,\r\n  1, 2, 2, 4,\r\n  1, 2, 4, 4,\r\n  2, 4, 4, 4,\r\n  2, 4, 6, 5,\r\n  2, 4, 6, 6,\r\n  2, 5, 8, 8,\r\n  4, 5, 8, 8,\r\n  4, 5, 8, 11,\r\n  4, 8, 10, 11,\r\n  4, 9, 12, 16,\r\n  4, 9, 16, 16,\r\n  6, 10, 12, 18,\r\n  6, 10, 17, 16,\r\n  6, 11, 16, 19,\r\n  6, 13, 18, 21,\r\n  7, 14, 21, 25,\r\n  8, 16, 20, 25,\r\n  8, 17, 23, 25,\r\n  9, 17, 23, 34,\r\n  9, 18, 25, 30,\r\n  10, 20, 27, 32,\r\n  12, 21, 29, 35,\r\n  12, 23, 34, 37,\r\n  12, 25, 34, 40,\r\n  13, 26, 35, 42,\r\n  14, 28, 38, 45,\r\n  15, 29, 40, 48,\r\n  16, 31, 43, 51,\r\n  17, 33, 45, 54,\r\n  18, 35, 48, 57,\r\n  19, 37, 51, 60,\r\n  19, 38, 53, 63,\r\n  20, 40, 56, 66,\r\n  21, 43, 59, 70,\r\n  22, 45, 62, 74,\r\n  24, 47, 65, 77,\r\n  25, 49, 68, 81\r\n]\r\n\r\nconst EC_CODEWORDS_TABLE = [\r\n// L  M  Q  H\r\n  7, 10, 13, 17,\r\n  10, 16, 22, 28,\r\n  15, 26, 36, 44,\r\n  20, 36, 52, 64,\r\n  26, 48, 72, 88,\r\n  36, 64, 96, 112,\r\n  40, 72, 108, 130,\r\n  48, 88, 132, 156,\r\n  60, 110, 160, 192,\r\n  72, 130, 192, 224,\r\n  80, 150, 224, 264,\r\n  96, 176, 260, 308,\r\n  104, 198, 288, 352,\r\n  120, 216, 320, 384,\r\n  132, 240, 360, 432,\r\n  144, 280, 408, 480,\r\n  168, 308, 448, 532,\r\n  180, 338, 504, 588,\r\n  196, 364, 546, 650,\r\n  224, 416, 600, 700,\r\n  224, 442, 644, 750,\r\n  252, 476, 690, 816,\r\n  270, 504, 750, 900,\r\n  300, 560, 810, 960,\r\n  312, 588, 870, 1050,\r\n  336, 644, 952, 1110,\r\n  360, 700, 1020, 1200,\r\n  390, 728, 1050, 1260,\r\n  420, 784, 1140, 1350,\r\n  450, 812, 1200, 1440,\r\n  480, 868, 1290, 1530,\r\n  510, 924, 1350, 1620,\r\n  540, 980, 1440, 1710,\r\n  570, 1036, 1530, 1800,\r\n  570, 1064, 1590, 1890,\r\n  600, 1120, 1680, 1980,\r\n  630, 1204, 1770, 2100,\r\n  660, 1260, 1860, 2220,\r\n  720, 1316, 1950, 2310,\r\n  750, 1372, 2040, 2430\r\n]\r\n\r\n/**\r\n * Returns the number of error correction block that the QR Code should contain\r\n * for the specified version and error correction level.\r\n *\r\n * @param  {Number} version              QR Code version\r\n * @param  {Number} errorCorrectionLevel Error correction level\r\n * @return {Number}                      Number of error correction blocks\r\n */\r\nexports.getBlocksCount = function getBlocksCount (version, errorCorrectionLevel) {\r\n  switch (errorCorrectionLevel) {\r\n    case ECLevel.L:\r\n      return EC_BLOCKS_TABLE[(version - 1) * 4 + 0]\r\n    case ECLevel.M:\r\n      return EC_BLOCKS_TABLE[(version - 1) * 4 + 1]\r\n    case ECLevel.Q:\r\n      return EC_BLOCKS_TABLE[(version - 1) * 4 + 2]\r\n    case ECLevel.H:\r\n      return EC_BLOCKS_TABLE[(version - 1) * 4 + 3]\r\n    default:\r\n      return undefined\r\n  }\r\n}\r\n\r\n/**\r\n * Returns the number of error correction codewords to use for the specified\r\n * version and error correction level.\r\n *\r\n * @param  {Number} version              QR Code version\r\n * @param  {Number} errorCorrectionLevel Error correction level\r\n * @return {Number}                      Number of error correction codewords\r\n */\r\nexports.getTotalCodewordsCount = function getTotalCodewordsCount (version, errorCorrectionLevel) {\r\n  switch (errorCorrectionLevel) {\r\n    case ECLevel.L:\r\n      return EC_CODEWORDS_TABLE[(version - 1) * 4 + 0]\r\n    case ECLevel.M:\r\n      return EC_CODEWORDS_TABLE[(version - 1) * 4 + 1]\r\n    case ECLevel.Q:\r\n      return EC_CODEWORDS_TABLE[(version - 1) * 4 + 2]\r\n    case ECLevel.H:\r\n      return EC_CODEWORDS_TABLE[(version - 1) * 4 + 3]\r\n    default:\r\n      return undefined\r\n  }\r\n}\r\n"], "mappings": "AAAA,MAAMA,OAAO,GAAGC,OAAO,CAAC,0BAA0<PERSON>,CAAC;AAEnD,MAAMC,eAAe,GAAG;AACxB;AACE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EACV,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EACV,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EACV,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EACV,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EACV,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EACV,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EACV,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EACV,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EACV,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EACV,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EACX,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EACZ,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EACZ,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EACZ,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EACb,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EACb,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EACb,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EACb,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EACb,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EACb,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EACb,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EACb,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EACb,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EACd,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EACd,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EACd,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EACd,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EACd,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EACd,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EACd,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EACd,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EACd,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EACd,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EACd,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EACd,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EACd,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EACd,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EACd,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EACd,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CACf;AAED,MAAMC,kBAAkB,GAAG;AAC3B;AACE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EACb,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EACd,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EACd,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EACd,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EACd,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EACf,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAChB,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAChB,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EACjB,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EACjB,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EACjB,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EACjB,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAClB,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAClB,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAClB,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAClB,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAClB,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAClB,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAClB,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAClB,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAClB,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAClB,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAClB,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAClB,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EACnB,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EACnB,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EACpB,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EACpB,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EACpB,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EACpB,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EACpB,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EACpB,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EACpB,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EACrB,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EACrB,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EACrB,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EACrB,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EACrB,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EACrB,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CACtB;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAC,OAAO,CAACC,cAAc,GAAG,SAASA,cAAcA,CAAEC,OAAO,EAAEC,oBAAoB,EAAE;EAC/E,QAAQA,oBAAoB;IAC1B,KAAKP,OAAO,CAACQ,CAAC;MACZ,OAAON,eAAe,CAAC,CAACI,OAAO,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAC/C,KAAKN,OAAO,CAACS,CAAC;MACZ,OAAOP,eAAe,CAAC,CAACI,OAAO,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAC/C,KAAKN,OAAO,CAACU,CAAC;MACZ,OAAOR,eAAe,CAAC,CAACI,OAAO,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAC/C,KAAKN,OAAO,CAACW,CAAC;MACZ,OAAOT,eAAe,CAAC,CAACI,OAAO,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAC/C;MACE,OAAOM,SAAS;EACpB;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAR,OAAO,CAACS,sBAAsB,GAAG,SAASA,sBAAsBA,CAAEP,OAAO,EAAEC,oBAAoB,EAAE;EAC/F,QAAQA,oBAAoB;IAC1B,KAAKP,OAAO,CAACQ,CAAC;MACZ,OAAOL,kBAAkB,CAAC,CAACG,OAAO,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAClD,KAAKN,OAAO,CAACS,CAAC;MACZ,OAAON,kBAAkB,CAAC,CAACG,OAAO,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAClD,KAAKN,OAAO,CAACU,CAAC;MACZ,OAAOP,kBAAkB,CAAC,CAACG,OAAO,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAClD,KAAKN,OAAO,CAACW,CAAC;MACZ,OAAOR,kBAAkB,CAAC,CAACG,OAAO,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAClD;MACE,OAAOM,SAAS;EACpB;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}