{"ast": null, "code": "const Mode = require('./mode');\nfunction ByteData(data) {\n  this.mode = Mode.BYTE;\n  if (typeof data === 'string') {\n    this.data = new TextEncoder().encode(data);\n  } else {\n    this.data = new Uint8Array(data);\n  }\n}\nByteData.getBitsLength = function getBitsLength(length) {\n  return length * 8;\n};\nByteData.prototype.getLength = function getLength() {\n  return this.data.length;\n};\nByteData.prototype.getBitsLength = function getBitsLength() {\n  return ByteData.getBitsLength(this.data.length);\n};\nByteData.prototype.write = function (bitBuffer) {\n  for (let i = 0, l = this.data.length; i < l; i++) {\n    bitBuffer.put(this.data[i], 8);\n  }\n};\nmodule.exports = ByteData;", "map": {"version": 3, "names": ["Mode", "require", "ByteData", "data", "mode", "BYTE", "TextEncoder", "encode", "Uint8Array", "getBitsLength", "length", "prototype", "<PERSON><PERSON><PERSON><PERSON>", "write", "bitBuffer", "i", "l", "put", "module", "exports"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/test 3/medical-assistant-react/node_modules/qrcode/lib/core/byte-data.js"], "sourcesContent": ["const Mode = require('./mode')\n\nfunction ByteData (data) {\n  this.mode = Mode.BYTE\n  if (typeof (data) === 'string') {\n    this.data = new TextEncoder().encode(data)\n  } else {\n    this.data = new Uint8Array(data)\n  }\n}\n\nByteData.getBitsLength = function getBitsLength (length) {\n  return length * 8\n}\n\nByteData.prototype.getLength = function getLength () {\n  return this.data.length\n}\n\nByteData.prototype.getBitsLength = function getBitsLength () {\n  return ByteData.getBitsLength(this.data.length)\n}\n\nByteData.prototype.write = function (bitBuffer) {\n  for (let i = 0, l = this.data.length; i < l; i++) {\n    bitBuffer.put(this.data[i], 8)\n  }\n}\n\nmodule.exports = ByteData\n"], "mappings": "AAAA,MAAMA,IAAI,GAAGC,OAAO,CAAC,QAAQ,CAAC;AAE9B,SAASC,QAAQA,CAAEC,IAAI,EAAE;EACvB,IAAI,CAACC,IAAI,GAAGJ,IAAI,CAACK,IAAI;EACrB,IAAI,OAAQF,IAAK,KAAK,QAAQ,EAAE;IAC9B,IAAI,CAACA,IAAI,GAAG,IAAIG,WAAW,CAAC,CAAC,CAACC,MAAM,CAACJ,IAAI,CAAC;EAC5C,CAAC,MAAM;IACL,IAAI,CAACA,IAAI,GAAG,IAAIK,UAAU,CAACL,IAAI,CAAC;EAClC;AACF;AAEAD,QAAQ,CAACO,aAAa,GAAG,SAASA,aAAaA,CAAEC,MAAM,EAAE;EACvD,OAAOA,MAAM,GAAG,CAAC;AACnB,CAAC;AAEDR,QAAQ,CAACS,SAAS,CAACC,SAAS,GAAG,SAASA,SAASA,CAAA,EAAI;EACnD,OAAO,IAAI,CAACT,IAAI,CAACO,MAAM;AACzB,CAAC;AAEDR,QAAQ,CAACS,SAAS,CAACF,aAAa,GAAG,SAASA,aAAaA,CAAA,EAAI;EAC3D,OAAOP,QAAQ,CAACO,aAAa,CAAC,IAAI,CAACN,IAAI,CAACO,MAAM,CAAC;AACjD,CAAC;AAEDR,QAAQ,CAACS,SAAS,CAACE,KAAK,GAAG,UAAUC,SAAS,EAAE;EAC9C,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAG,IAAI,CAACb,IAAI,CAACO,MAAM,EAAEK,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;IAChDD,SAAS,CAACG,GAAG,CAAC,IAAI,CAACd,IAAI,CAACY,CAAC,CAAC,EAAE,CAAC,CAAC;EAChC;AACF,CAAC;AAEDG,MAAM,CAACC,OAAO,GAAGjB,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}