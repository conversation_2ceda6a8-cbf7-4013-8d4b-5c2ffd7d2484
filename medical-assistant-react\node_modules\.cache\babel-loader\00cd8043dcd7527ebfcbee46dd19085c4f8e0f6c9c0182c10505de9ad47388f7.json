{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive - MOE Student S4M\\\\Desktop\\\\test 3\\\\medical-assistant-react\\\\src\\\\pages\\\\Home\\\\components\\\\HeroSection.tsx\";\nimport React from 'react';\nimport { Container, Row, <PERSON>, But<PERSON> } from 'react-bootstrap';\nimport { Link } from 'react-router-dom';\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\nimport { faStethoscope, faCalendarPlus } from '@fortawesome/free-solid-svg-icons';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst HeroSection = () => {\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    className: \"hero py-5 py-md-6\",\n    children: /*#__PURE__*/_jsxDEV(Container, {\n      children: /*#__PURE__*/_jsxDEV(Row, {\n        className: \"align-items-center\",\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          lg: 6,\n          className: \"mb-5 mb-lg-0\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"hero-content animate-fadeIn\",\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"display-4 fw-bold mb-4\",\n              children: \"\\u0645\\u0633\\u0627\\u0639\\u062F\\u0643 \\u0627\\u0644\\u0637\\u0628\\u064A \\u0627\\u0644\\u0630\\u0643\\u064A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 14,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"lead mb-4\",\n              children: \"\\u062A\\u062D\\u0644\\u064A\\u0644 \\u0630\\u0643\\u064A \\u0644\\u0644\\u0623\\u0639\\u0631\\u0627\\u0636 \\u0628\\u0627\\u0633\\u062A\\u062E\\u062F\\u0627\\u0645 \\u0627\\u0644\\u0630\\u0643\\u0627\\u0621 \\u0627\\u0644\\u0627\\u0635\\u0637\\u0646\\u0627\\u0639\\u064A \\u0648\\u0645\\u0639\\u0627\\u0644\\u062C\\u0629 \\u0627\\u0644\\u0644\\u063A\\u0629 \\u0627\\u0644\\u0637\\u0628\\u064A\\u0639\\u064A\\u0629 \\u0644\\u0644\\u062D\\u0635\\u0648\\u0644 \\u0639\\u0644\\u0649 \\u062A\\u0634\\u062E\\u064A\\u0635 \\u0623\\u0648\\u0644\\u064A \\u062F\\u0642\\u064A\\u0642 \\u0648\\u0633\\u0631\\u064A\\u0639\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 15,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"d-flex flex-column flex-sm-row gap-3\",\n              children: [/*#__PURE__*/_jsxDEV(Link, {\n                to: \"/diagnosis\",\n                className: \"text-decoration-none\",\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"primary\",\n                  size: \"lg\",\n                  className: \"d-flex align-items-center justify-content-center w-100\",\n                  children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                    icon: faStethoscope,\n                    className: \"me-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 26,\n                    columnNumber: 21\n                  }, this), \"\\u0627\\u0628\\u062F\\u0623 \\u0627\\u0644\\u062A\\u0634\\u062E\\u064A\\u0635\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 21,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 20,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/appointments\",\n                className: \"text-decoration-none\",\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"outline-primary\",\n                  size: \"lg\",\n                  className: \"d-flex align-items-center justify-content-center w-100\",\n                  children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                    icon: faCalendarPlus,\n                    className: \"me-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 36,\n                    columnNumber: 21\n                  }, this), \"\\u0627\\u062D\\u062C\\u0632 \\u0645\\u0648\\u0639\\u062F\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 31,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 30,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 19,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 13,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 12,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          lg: 6,\n          className: \"d-none d-lg-block\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"hero-image-container position-relative animate-slideInUp\",\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: \"/images/medical-banner-with-doctor-wearing-goggles.jpg\",\n              className: \"img-fluid rounded-4 shadow-lg\",\n              alt: \"Doctor using medical technology\",\n              onError: e => {\n                // Fallback to a placeholder if image doesn't exist\n                e.currentTarget.src = 'https://via.placeholder.com/600x400/007bff/ffffff?text=Medical+Assistant';\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 45,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 44,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 11,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 10,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 9,\n    columnNumber: 5\n  }, this);\n};\n_c = HeroSection;\nexport default HeroSection;\nvar _c;\n$RefreshReg$(_c, \"HeroSection\");", "map": {"version": 3, "names": ["React", "Container", "Row", "Col", "<PERSON><PERSON>", "Link", "FontAwesomeIcon", "faStethoscope", "faCalendarPlus", "jsxDEV", "_jsxDEV", "HeroSection", "className", "children", "lg", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "variant", "size", "icon", "src", "alt", "onError", "e", "currentTarget", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/test 3/medical-assistant-react/src/pages/Home/components/HeroSection.tsx"], "sourcesContent": ["import React from 'react';\nimport { Container, Row, Col, But<PERSON> } from 'react-bootstrap';\nimport { <PERSON> } from 'react-router-dom';\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\nimport { faStethoscope, faCalendarPlus } from '@fortawesome/free-solid-svg-icons';\n\nconst HeroSection: React.FC = () => {\n  return (\n    <section className=\"hero py-5 py-md-6\">\n      <Container>\n        <Row className=\"align-items-center\">\n          <Col lg={6} className=\"mb-5 mb-lg-0\">\n            <div className=\"hero-content animate-fadeIn\">\n              <h1 className=\"display-4 fw-bold mb-4\">مساعدك الطبي الذكي</h1>\n              <p className=\"lead mb-4\">\n                تحليل ذكي للأعراض باستخدام الذكاء الاصطناعي ومعالجة اللغة الطبيعية \n                للحصول على تشخيص أولي دقيق وسريع\n              </p>\n              <div className=\"d-flex flex-column flex-sm-row gap-3\">\n                <Link to=\"/diagnosis\" className=\"text-decoration-none\">\n                  <Button\n                    variant=\"primary\"\n                    size=\"lg\"\n                    className=\"d-flex align-items-center justify-content-center w-100\"\n                  >\n                    <FontAwesomeIcon icon={faStethoscope} className=\"me-2\" />\n                    ابدأ التشخيص\n                  </Button>\n                </Link>\n                <Link to=\"/appointments\" className=\"text-decoration-none\">\n                  <Button\n                    variant=\"outline-primary\"\n                    size=\"lg\"\n                    className=\"d-flex align-items-center justify-content-center w-100\"\n                  >\n                    <FontAwesomeIcon icon={faCalendarPlus} className=\"me-2\" />\n                    احجز موعد\n                  </Button>\n                </Link>\n              </div>\n            </div>\n          </Col>\n          <Col lg={6} className=\"d-none d-lg-block\">\n            <div className=\"hero-image-container position-relative animate-slideInUp\">\n              <img \n                src=\"/images/medical-banner-with-doctor-wearing-goggles.jpg\" \n                className=\"img-fluid rounded-4 shadow-lg\" \n                alt=\"Doctor using medical technology\"\n                onError={(e) => {\n                  // Fallback to a placeholder if image doesn't exist\n                  e.currentTarget.src = 'https://via.placeholder.com/600x400/007bff/ffffff?text=Medical+Assistant';\n                }}\n              />\n            </div>\n          </Col>\n        </Row>\n      </Container>\n    </section>\n  );\n};\n\nexport default HeroSection;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,SAAS,EAAEC,GAAG,EAAEC,GAAG,EAAEC,MAAM,QAAQ,iBAAiB;AAC7D,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,eAAe,QAAQ,gCAAgC;AAChE,SAASC,aAAa,EAAEC,cAAc,QAAQ,mCAAmC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElF,MAAMC,WAAqB,GAAGA,CAAA,KAAM;EAClC,oBACED,OAAA;IAASE,SAAS,EAAC,mBAAmB;IAAAC,QAAA,eACpCH,OAAA,CAACT,SAAS;MAAAY,QAAA,eACRH,OAAA,CAACR,GAAG;QAACU,SAAS,EAAC,oBAAoB;QAAAC,QAAA,gBACjCH,OAAA,CAACP,GAAG;UAACW,EAAE,EAAE,CAAE;UAACF,SAAS,EAAC,cAAc;UAAAC,QAAA,eAClCH,OAAA;YAAKE,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1CH,OAAA;cAAIE,SAAS,EAAC,wBAAwB;cAAAC,QAAA,EAAC;YAAkB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9DR,OAAA;cAAGE,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAGzB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJR,OAAA;cAAKE,SAAS,EAAC,sCAAsC;cAAAC,QAAA,gBACnDH,OAAA,CAACL,IAAI;gBAACc,EAAE,EAAC,YAAY;gBAACP,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,eACpDH,OAAA,CAACN,MAAM;kBACLgB,OAAO,EAAC,SAAS;kBACjBC,IAAI,EAAC,IAAI;kBACTT,SAAS,EAAC,wDAAwD;kBAAAC,QAAA,gBAElEH,OAAA,CAACJ,eAAe;oBAACgB,IAAI,EAAEf,aAAc;oBAACK,SAAS,EAAC;kBAAM;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,uEAE3D;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACPR,OAAA,CAACL,IAAI;gBAACc,EAAE,EAAC,eAAe;gBAACP,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,eACvDH,OAAA,CAACN,MAAM;kBACLgB,OAAO,EAAC,iBAAiB;kBACzBC,IAAI,EAAC,IAAI;kBACTT,SAAS,EAAC,wDAAwD;kBAAAC,QAAA,gBAElEH,OAAA,CAACJ,eAAe;oBAACgB,IAAI,EAAEd,cAAe;oBAACI,SAAS,EAAC;kBAAM;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,qDAE5D;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNR,OAAA,CAACP,GAAG;UAACW,EAAE,EAAE,CAAE;UAACF,SAAS,EAAC,mBAAmB;UAAAC,QAAA,eACvCH,OAAA;YAAKE,SAAS,EAAC,0DAA0D;YAAAC,QAAA,eACvEH,OAAA;cACEa,GAAG,EAAC,wDAAwD;cAC5DX,SAAS,EAAC,+BAA+B;cACzCY,GAAG,EAAC,iCAAiC;cACrCC,OAAO,EAAGC,CAAC,IAAK;gBACd;gBACAA,CAAC,CAACC,aAAa,CAACJ,GAAG,GAAG,0EAA0E;cAClG;YAAE;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAACU,EAAA,GArDIjB,WAAqB;AAuD3B,eAAeA,WAAW;AAAC,IAAAiB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}