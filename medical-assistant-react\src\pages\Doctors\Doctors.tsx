import React, { useState, useEffect } from 'react';
import { Container, <PERSON>, Col, <PERSON>, Button, Badge, Form, InputGroup } from 'react-bootstrap';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faUserMd,
  faArrowRight,
  faArrowLeft,
  faStar,
  faStarHalfAlt,
  faMapMarkerAlt,
  faCalendarAlt,
  faGraduationCap,
  faAward,
  faHeart,
  faSearch,
  faFilter,
  faPhone,
  faVideo,
  faClock,
  faCheckCircle
} from '@fortawesome/free-solid-svg-icons';
import StepIndicator from '../Appointments/components/StepIndicator';

interface Doctor {
  id: number;
  name: string;
  specialty: string;
  experience: string;
  rating: number;
  reviewCount: number;
  price: string;
  location: string;
  image: string;
  education: string;
  languages: string[];
  availableToday: boolean;
  nextAvailable: string;
  consultationType: ('video' | 'phone' | 'clinic')[];
  verified: boolean;
  description: string;
}

const Doctors: React.FC = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const specialty = searchParams.get('specialty') || 'طب عام';
  const [selectedDoctor, setSelectedDoctor] = useState<number | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState('rating');

  const mockDoctors: Doctor[] = [
    {
      id: 1,
      name: 'د. أحمد محمد الخالدي',
      specialty: 'طبيب قلب وأوعية دموية',
      experience: '15 سنة',
      rating: 4.8,
      reviewCount: 324,
      price: '200 ريال',
      location: 'الرياض - حي الملك فهد',
      image: '/images/AdobeStock_305412791_Preview.jpeg',
      education: 'دكتوراه في طب القلب - جامعة الملك سعود',
      languages: ['العربية', 'الإنجليزية'],
      availableToday: true,
      nextAvailable: 'اليوم 2:00 م',
      consultationType: ['video', 'phone', 'clinic'],
      verified: true,
      description: 'طبيب قلب متخصص مع خبرة واسعة في علاج أمراض القلب والأوعية الدموية'
    },
    {
      id: 2,
      name: 'د. فاطمة علي السالم',
      specialty: 'طبيبة نساء وولادة',
      experience: '12 سنة',
      rating: 4.9,
      reviewCount: 456,
      price: '250 ريال',
      location: 'جدة - حي الزهراء',
      image: '/images/AdobeStock_320744517_Preview.jpeg',
      education: 'ماجستير في طب النساء والولادة - جامعة الملك عبدالعزيز',
      languages: ['العربية', 'الإنجليزية', 'الفرنسية'],
      availableToday: false,
      nextAvailable: 'غداً 10:00 ص',
      consultationType: ['video', 'clinic'],
      verified: true,
      description: 'طبيبة نساء وولادة متخصصة في الحمل عالي الخطورة والجراحات النسائية'
    },
    {
      id: 3,
      name: 'د. محمد سالم العتيبي',
      specialty: 'طبيب أطفال',
      experience: '10 سنوات',
      rating: 4.7,
      reviewCount: 289,
      price: '180 ريال',
      location: 'الدمام - حي الفيصلية',
      image: '/images/AdobeStock_315674262_Preview.jpeg',
      education: 'بكالوريوس طب وجراحة - جامعة الدمام',
      languages: ['العربية', 'الإنجليزية'],
      availableToday: true,
      nextAvailable: 'اليوم 4:30 م',
      consultationType: ['video', 'phone', 'clinic'],
      verified: true,
      description: 'طبيب أطفال متخصص في نمو وتطور الأطفال والتطعيمات'
    },
    {
      id: 4,
      name: 'د. سارة أحمد الزهراني',
      specialty: 'طبيبة جلدية',
      experience: '8 سنوات',
      rating: 4.6,
      reviewCount: 198,
      price: '220 ريال',
      location: 'الرياض - حي العليا',
      image: '/images/AdobeStock_271270515_Preview.jpeg',
      education: 'دبلوم في الأمراض الجلدية - المجلس السعودي',
      languages: ['العربية', 'الإنجليزية'],
      availableToday: true,
      nextAvailable: 'اليوم 6:00 م',
      consultationType: ['video', 'clinic'],
      verified: true,
      description: 'طبيبة جلدية متخصصة في علاج الأمراض الجلدية والتجميل الطبي'
    },
    {
      id: 5,
      name: 'د. عبدالله محمد القحطاني',
      specialty: 'طبيب عظام',
      experience: '18 سنة',
      rating: 4.9,
      reviewCount: 512,
      price: '300 ريال',
      location: 'الرياض - حي الملك عبدالله',
      image: '/images/AdobeStock_282721302_Preview.jpeg',
      education: 'دكتوراه في جراحة العظام - جامعة هارفارد',
      languages: ['العربية', 'الإنجليزية'],
      availableToday: false,
      nextAvailable: 'بعد غد 9:00 ص',
      consultationType: ['clinic'],
      verified: true,
      description: 'جراح عظام متخصص في جراحات العمود الفقري والمفاصل'
    },
    {
      id: 6,
      name: 'د. نورا سعد الغامدي',
      specialty: 'طبيبة نفسية',
      experience: '7 سنوات',
      rating: 4.8,
      reviewCount: 167,
      price: '190 ريال',
      location: 'جدة - حي الروضة',
      image: '/images/AdobeStock_249496776_Preview.jpeg',
      education: 'ماجستير في الطب النفسي - جامعة الملك عبدالعزيز',
      languages: ['العربية', 'الإنجليزية'],
      availableToday: true,
      nextAvailable: 'اليوم 7:00 م',
      consultationType: ['video', 'phone'],
      verified: true,
      description: 'طبيبة نفسية متخصصة في علاج القلق والاكتئاب والاضطرابات النفسية'
    }
  ];

  // Helper function to render star rating
  const renderStars = (rating: number) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;

    for (let i = 0; i < fullStars; i++) {
      stars.push(<FontAwesomeIcon key={i} icon={faStar} className="text-warning" />);
    }

    if (hasHalfStar) {
      stars.push(<FontAwesomeIcon key="half" icon={faStarHalfAlt} className="text-warning" />);
    }

    const emptyStars = 5 - Math.ceil(rating);
    for (let i = 0; i < emptyStars; i++) {
      stars.push(<FontAwesomeIcon key={`empty-${i}`} icon={faStar} className="text-muted" />);
    }

    return stars;
  };

  // Filter and sort doctors
  const filteredDoctors = mockDoctors
    .filter(doctor =>
      doctor.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      doctor.specialty.toLowerCase().includes(searchTerm.toLowerCase())
    )
    .sort((a, b) => {
      switch (sortBy) {
        case 'rating':
          return b.rating - a.rating;
        case 'price':
          return parseInt(a.price) - parseInt(b.price);
        case 'experience':
          return parseInt(b.experience) - parseInt(a.experience);
        default:
          return 0;
      }
    });

  const handleDoctorSelect = (doctorId: number) => {
    setSelectedDoctor(selectedDoctor === doctorId ? null : doctorId);
  };

  const handleBookAppointment = (doctorId: number) => {
    navigate(`/booking?doctorId=${doctorId}`);
  };

  return (
    <Container className="py-5">
      <StepIndicator currentStep={2} />

      {/* Header Section */}
      <div className="text-center mb-5">
        <Badge bg="primary" className="px-3 py-2 rounded-pill mb-3">
          <FontAwesomeIcon icon={faUserMd} className="me-2" />
          أطباء متخصصون
        </Badge>
        <h1 className="display-4 fw-bold mb-3 text-gradient-medical">
          اختر الطبيب المناسب
        </h1>
        <p className="lead text-muted">
          أطباء معتمدون ومتخصصون في {specialty} جاهزون لخدمتك
        </p>
      </div>

      {/* Search and Filter Section */}
      <Row className="mb-4">
        <Col md={8}>
          <InputGroup size="lg">
            <InputGroup.Text>
              <FontAwesomeIcon icon={faSearch} />
            </InputGroup.Text>
            <Form.Control
              type="text"
              placeholder="ابحث عن طبيب أو تخصص..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="form-control-lg"
            />
          </InputGroup>
        </Col>
        <Col md={4}>
          <Form.Select
            size="lg"
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value)}
          >
            <option value="rating">ترتيب حسب التقييم</option>
            <option value="price">ترتيب حسب السعر</option>
            <option value="experience">ترتيب حسب الخبرة</option>
          </Form.Select>
        </Col>
      </Row>

      {/* Doctors Grid */}
      <Row className="g-4">
        {filteredDoctors.map((doctor, index) => (
          <Col lg={6} xl={4} key={doctor.id}>
            <Card
              className={`h-100 border-0 shadow-sm hover-lift card-medical cursor-pointer animate-scaleIn animate-stagger-${(index % 3) + 1} ${
                selectedDoctor === doctor.id ? 'border-primary' : ''
              }`}
              onClick={() => handleDoctorSelect(doctor.id)}
              style={{
                cursor: 'pointer',
                transform: selectedDoctor === doctor.id ? 'translateY(-4px)' : 'none',
                boxShadow: selectedDoctor === doctor.id ? 'var(--medical-shadow-strong)' : undefined
              }}
            >
              {/* Doctor Image */}
              <div className="position-relative">
                <img
                  src={doctor.image}
                  alt={doctor.name}
                  className="card-img-top"
                  style={{
                    height: '200px',
                    objectFit: 'cover',
                    filter: 'brightness(1.1) contrast(1.1)'
                  }}
                  onError={(e) => {
                    e.currentTarget.src = 'https://via.placeholder.com/300x200/0ea5e9/ffffff?text=Doctor';
                  }}
                />

                {/* Status Badges */}
                <div className="position-absolute top-0 start-0 m-3">
                  {doctor.verified && (
                    <Badge bg="success" className="rounded-pill me-2">
                      <FontAwesomeIcon icon={faCheckCircle} className="me-1" />
                      معتمد
                    </Badge>
                  )}
                  {doctor.availableToday && (
                    <Badge bg="primary" className="rounded-pill">
                      <FontAwesomeIcon icon={faClock} className="me-1" />
                      متاح اليوم
                    </Badge>
                  )}
                </div>

                {/* Favorite Button */}
                <div className="position-absolute top-0 end-0 m-3">
                  <Button
                    variant="light"
                    size="sm"
                    className="rounded-circle p-2 hover-scale"
                    onClick={(e) => {
                      e.stopPropagation();
                      // Handle favorite logic
                    }}
                  >
                    <FontAwesomeIcon icon={faHeart} className="text-muted" />
                  </Button>
                </div>
              </div>

              <Card.Body className="p-4">
                {/* Doctor Info */}
                <div className="d-flex justify-content-between align-items-start mb-3">
                  <div>
                    <Card.Title className="h5 fw-bold mb-1 text-primary">
                      {doctor.name}
                    </Card.Title>
                    <p className="text-muted mb-2 small">{doctor.specialty}</p>
                  </div>
                  <div className="text-end">
                    <div className="fw-bold text-success h6 mb-1">{doctor.price}</div>
                    <small className="text-muted">للاستشارة</small>
                  </div>
                </div>

                {/* Rating */}
                <div className="d-flex align-items-center mb-3">
                  <div className="me-2">
                    {renderStars(doctor.rating)}
                  </div>
                  <span className="fw-bold text-warning me-2">{doctor.rating}</span>
                  <small className="text-muted">({doctor.reviewCount} تقييم)</small>
                </div>

                {/* Quick Info */}
                <Row className="g-2 mb-3 small">
                  <Col xs={6}>
                    <div className="d-flex align-items-center">
                      <FontAwesomeIcon icon={faGraduationCap} className="text-muted me-2" />
                      <span className="text-muted">{doctor.experience}</span>
                    </div>
                  </Col>
                  <Col xs={6}>
                    <div className="d-flex align-items-center">
                      <FontAwesomeIcon icon={faMapMarkerAlt} className="text-muted me-2" />
                      <span className="text-muted small">{doctor.location.split(' - ')[0]}</span>
                    </div>
                  </Col>
                </Row>

                {/* Consultation Types */}
                <div className="mb-3">
                  <small className="text-muted d-block mb-2">أنواع الاستشارة:</small>
                  <div className="d-flex gap-2">
                    {doctor.consultationType.map((type) => (
                      <Badge
                        key={type}
                        bg="light"
                        text="dark"
                        className="rounded-pill small"
                      >
                        <FontAwesomeIcon
                          icon={type === 'video' ? faVideo : type === 'phone' ? faPhone : faUserMd}
                          className="me-1"
                        />
                        {type === 'video' ? 'فيديو' : type === 'phone' ? 'هاتف' : 'عيادة'}
                      </Badge>
                    ))}
                  </div>
                </div>

                {/* Next Available */}
                <div className="mb-3">
                  <small className="text-muted">أقرب موعد متاح:</small>
                  <div className="fw-medium text-primary">{doctor.nextAvailable}</div>
                </div>

                {/* Action Buttons */}
                <div className="d-grid gap-2">
                  <Button
                    variant="primary"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleBookAppointment(doctor.id);
                    }}
                    className="hover-lift"
                  >
                    <FontAwesomeIcon icon={faCalendarAlt} className="me-2" />
                    احجز موعد
                  </Button>

                  {selectedDoctor === doctor.id && (
                    <div className="animate-slideInUp">
                      <Button
                        variant="outline-primary"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          // Handle view profile
                        }}
                      >
                        عرض الملف الشخصي
                      </Button>
                    </div>
                  )}
                </div>
              </Card.Body>
            </Card>
          </Col>
        ))}
      </Row>

      {/* No Results */}
      {filteredDoctors.length === 0 && (
        <div className="text-center py-5">
          <FontAwesomeIcon icon={faUserMd} className="text-muted mb-3" style={{ fontSize: '4rem' }} />
          <h4 className="text-muted">لا توجد نتائج</h4>
          <p className="text-muted">جرب تغيير كلمات البحث أو المرشحات</p>
        </div>
      )}

      {/* Navigation */}
      <div className="d-flex justify-content-between mt-5 pt-4 border-top">
        <Button
          variant="outline-secondary"
          size="lg"
          onClick={() => navigate('/appointments')}
          className="hover-lift"
        >
          <FontAwesomeIcon icon={faArrowRight} className="me-2" />
          السابق
        </Button>

        {selectedDoctor && (
          <Button
            variant="primary"
            size="lg"
            onClick={() => handleBookAppointment(selectedDoctor)}
            className="hover-lift animate-pulse-medical"
          >
            متابعة الحجز
            <FontAwesomeIcon icon={faArrowLeft} className="ms-2" />
          </Button>
        )}
      </div>
    </Container>
  );
};

export default Doctors;
