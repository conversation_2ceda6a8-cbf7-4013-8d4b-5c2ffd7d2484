{"ast": null, "code": "import { addUniqueItem, removeItem } from './array.mjs';\nclass SubscriptionManager {\n  constructor() {\n    this.subscriptions = [];\n  }\n  add(handler) {\n    addUniqueItem(this.subscriptions, handler);\n    return () => removeItem(this.subscriptions, handler);\n  }\n  notify(a, b, c) {\n    const numSubscriptions = this.subscriptions.length;\n    if (!numSubscriptions) return;\n    if (numSubscriptions === 1) {\n      /**\n       * If there's only a single handler we can just call it without invoking a loop.\n       */\n      this.subscriptions[0](a, b, c);\n    } else {\n      for (let i = 0; i < numSubscriptions; i++) {\n        /**\n         * Check whether the handler exists before firing as it's possible\n         * the subscriptions were modified during this loop running.\n         */\n        const handler = this.subscriptions[i];\n        handler && handler(a, b, c);\n      }\n    }\n  }\n  getSize() {\n    return this.subscriptions.length;\n  }\n  clear() {\n    this.subscriptions.length = 0;\n  }\n}\nexport { SubscriptionManager };", "map": {"version": 3, "names": ["addUniqueItem", "removeItem", "SubscriptionManager", "constructor", "subscriptions", "add", "handler", "notify", "a", "b", "c", "numSubscriptions", "length", "i", "getSize", "clear"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/test 3/medical-assistant-react/node_modules/framer-motion/dist/es/utils/subscription-manager.mjs"], "sourcesContent": ["import { addUniqueItem, removeItem } from './array.mjs';\n\nclass SubscriptionManager {\n    constructor() {\n        this.subscriptions = [];\n    }\n    add(handler) {\n        addUniqueItem(this.subscriptions, handler);\n        return () => removeItem(this.subscriptions, handler);\n    }\n    notify(a, b, c) {\n        const numSubscriptions = this.subscriptions.length;\n        if (!numSubscriptions)\n            return;\n        if (numSubscriptions === 1) {\n            /**\n             * If there's only a single handler we can just call it without invoking a loop.\n             */\n            this.subscriptions[0](a, b, c);\n        }\n        else {\n            for (let i = 0; i < numSubscriptions; i++) {\n                /**\n                 * Check whether the handler exists before firing as it's possible\n                 * the subscriptions were modified during this loop running.\n                 */\n                const handler = this.subscriptions[i];\n                handler && handler(a, b, c);\n            }\n        }\n    }\n    getSize() {\n        return this.subscriptions.length;\n    }\n    clear() {\n        this.subscriptions.length = 0;\n    }\n}\n\nexport { SubscriptionManager };\n"], "mappings": "AAAA,SAASA,aAAa,EAAEC,UAAU,QAAQ,aAAa;AAEvD,MAAMC,mBAAmB,CAAC;EACtBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,aAAa,GAAG,EAAE;EAC3B;EACAC,GAAGA,CAACC,OAAO,EAAE;IACTN,aAAa,CAAC,IAAI,CAACI,aAAa,EAAEE,OAAO,CAAC;IAC1C,OAAO,MAAML,UAAU,CAAC,IAAI,CAACG,aAAa,EAAEE,OAAO,CAAC;EACxD;EACAC,MAAMA,CAACC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;IACZ,MAAMC,gBAAgB,GAAG,IAAI,CAACP,aAAa,CAACQ,MAAM;IAClD,IAAI,CAACD,gBAAgB,EACjB;IACJ,IAAIA,gBAAgB,KAAK,CAAC,EAAE;MACxB;AACZ;AACA;MACY,IAAI,CAACP,aAAa,CAAC,CAAC,CAAC,CAACI,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC;IAClC,CAAC,MACI;MACD,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,gBAAgB,EAAEE,CAAC,EAAE,EAAE;QACvC;AAChB;AACA;AACA;QACgB,MAAMP,OAAO,GAAG,IAAI,CAACF,aAAa,CAACS,CAAC,CAAC;QACrCP,OAAO,IAAIA,OAAO,CAACE,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC;MAC/B;IACJ;EACJ;EACAI,OAAOA,CAAA,EAAG;IACN,OAAO,IAAI,CAACV,aAAa,CAACQ,MAAM;EACpC;EACAG,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACX,aAAa,CAACQ,MAAM,GAAG,CAAC;EACjC;AACJ;AAEA,SAASV,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}