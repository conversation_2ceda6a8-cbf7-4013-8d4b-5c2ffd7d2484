{"ast": null, "code": "import _objectSpread from \"C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/test 3/medical-assistant-react/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport { extractEventInfo } from '../../events/event-info.mjs';\nimport { secondsToMilliseconds, millisecondsToSeconds } from '../../utils/time-conversion.mjs';\nimport { addPointerEvent } from '../../events/add-pointer-event.mjs';\nimport { pipe } from '../../utils/pipe.mjs';\nimport { distance2D } from '../../utils/distance.mjs';\nimport { isPrimaryPointer } from '../../events/utils/is-primary-pointer.mjs';\nimport { frame, cancelFrame, frameData } from '../../frameloop/frame.mjs';\n\n/**\n * @internal\n */\nclass PanSession {\n  constructor(event, handlers) {\n    let {\n      transformPagePoint,\n      contextWindow,\n      dragSnapToOrigin = false\n    } = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    /**\n     * @internal\n     */\n    this.startEvent = null;\n    /**\n     * @internal\n     */\n    this.lastMoveEvent = null;\n    /**\n     * @internal\n     */\n    this.lastMoveEventInfo = null;\n    /**\n     * @internal\n     */\n    this.handlers = {};\n    /**\n     * @internal\n     */\n    this.contextWindow = window;\n    this.updatePoint = () => {\n      if (!(this.lastMoveEvent && this.lastMoveEventInfo)) return;\n      const info = getPanInfo(this.lastMoveEventInfo, this.history);\n      const isPanStarted = this.startEvent !== null;\n      // Only start panning if the offset is larger than 3 pixels. If we make it\n      // any larger than this we'll want to reset the pointer history\n      // on the first update to avoid visual snapping to the cursoe.\n      const isDistancePastThreshold = distance2D(info.offset, {\n        x: 0,\n        y: 0\n      }) >= 3;\n      if (!isPanStarted && !isDistancePastThreshold) return;\n      const {\n        point\n      } = info;\n      const {\n        timestamp\n      } = frameData;\n      this.history.push(_objectSpread(_objectSpread({}, point), {}, {\n        timestamp\n      }));\n      const {\n        onStart,\n        onMove\n      } = this.handlers;\n      if (!isPanStarted) {\n        onStart && onStart(this.lastMoveEvent, info);\n        this.startEvent = this.lastMoveEvent;\n      }\n      onMove && onMove(this.lastMoveEvent, info);\n    };\n    this.handlePointerMove = (event, info) => {\n      this.lastMoveEvent = event;\n      this.lastMoveEventInfo = transformPoint(info, this.transformPagePoint);\n      // Throttle mouse move event to once per frame\n      frame.update(this.updatePoint, true);\n    };\n    this.handlePointerUp = (event, info) => {\n      this.end();\n      const {\n        onEnd,\n        onSessionEnd,\n        resumeAnimation\n      } = this.handlers;\n      if (this.dragSnapToOrigin) resumeAnimation && resumeAnimation();\n      if (!(this.lastMoveEvent && this.lastMoveEventInfo)) return;\n      const panInfo = getPanInfo(event.type === \"pointercancel\" ? this.lastMoveEventInfo : transformPoint(info, this.transformPagePoint), this.history);\n      if (this.startEvent && onEnd) {\n        onEnd(event, panInfo);\n      }\n      onSessionEnd && onSessionEnd(event, panInfo);\n    };\n    // If we have more than one touch, don't start detecting this gesture\n    if (!isPrimaryPointer(event)) return;\n    this.dragSnapToOrigin = dragSnapToOrigin;\n    this.handlers = handlers;\n    this.transformPagePoint = transformPagePoint;\n    this.contextWindow = contextWindow || window;\n    const info = extractEventInfo(event);\n    const initialInfo = transformPoint(info, this.transformPagePoint);\n    const {\n      point\n    } = initialInfo;\n    const {\n      timestamp\n    } = frameData;\n    this.history = [_objectSpread(_objectSpread({}, point), {}, {\n      timestamp\n    })];\n    const {\n      onSessionStart\n    } = handlers;\n    onSessionStart && onSessionStart(event, getPanInfo(initialInfo, this.history));\n    this.removeListeners = pipe(addPointerEvent(this.contextWindow, \"pointermove\", this.handlePointerMove), addPointerEvent(this.contextWindow, \"pointerup\", this.handlePointerUp), addPointerEvent(this.contextWindow, \"pointercancel\", this.handlePointerUp));\n  }\n  updateHandlers(handlers) {\n    this.handlers = handlers;\n  }\n  end() {\n    this.removeListeners && this.removeListeners();\n    cancelFrame(this.updatePoint);\n  }\n}\nfunction transformPoint(info, transformPagePoint) {\n  return transformPagePoint ? {\n    point: transformPagePoint(info.point)\n  } : info;\n}\nfunction subtractPoint(a, b) {\n  return {\n    x: a.x - b.x,\n    y: a.y - b.y\n  };\n}\nfunction getPanInfo(_ref, history) {\n  let {\n    point\n  } = _ref;\n  return {\n    point,\n    delta: subtractPoint(point, lastDevicePoint(history)),\n    offset: subtractPoint(point, startDevicePoint(history)),\n    velocity: getVelocity(history, 0.1)\n  };\n}\nfunction startDevicePoint(history) {\n  return history[0];\n}\nfunction lastDevicePoint(history) {\n  return history[history.length - 1];\n}\nfunction getVelocity(history, timeDelta) {\n  if (history.length < 2) {\n    return {\n      x: 0,\n      y: 0\n    };\n  }\n  let i = history.length - 1;\n  let timestampedPoint = null;\n  const lastPoint = lastDevicePoint(history);\n  while (i >= 0) {\n    timestampedPoint = history[i];\n    if (lastPoint.timestamp - timestampedPoint.timestamp > secondsToMilliseconds(timeDelta)) {\n      break;\n    }\n    i--;\n  }\n  if (!timestampedPoint) {\n    return {\n      x: 0,\n      y: 0\n    };\n  }\n  const time = millisecondsToSeconds(lastPoint.timestamp - timestampedPoint.timestamp);\n  if (time === 0) {\n    return {\n      x: 0,\n      y: 0\n    };\n  }\n  const currentVelocity = {\n    x: (lastPoint.x - timestampedPoint.x) / time,\n    y: (lastPoint.y - timestampedPoint.y) / time\n  };\n  if (currentVelocity.x === Infinity) {\n    currentVelocity.x = 0;\n  }\n  if (currentVelocity.y === Infinity) {\n    currentVelocity.y = 0;\n  }\n  return currentVelocity;\n}\nexport { PanSession };", "map": {"version": 3, "names": ["extractEventInfo", "secondsToMilliseconds", "millisecondsToSeconds", "addPointerEvent", "pipe", "distance2D", "isPrimaryPointer", "frame", "cancelFrame", "frameData", "PanSession", "constructor", "event", "handlers", "transformPagePoint", "contextWindow", "dragSnapToO<PERSON>in", "arguments", "length", "undefined", "startEvent", "lastMoveEvent", "lastMoveEventInfo", "window", "updatePoint", "info", "getPanInfo", "history", "isPanStarted", "isDistancePastThreshold", "offset", "x", "y", "point", "timestamp", "push", "_objectSpread", "onStart", "onMove", "handlePointerMove", "transformPoint", "update", "handlePointerUp", "end", "onEnd", "onSessionEnd", "resumeAnimation", "panInfo", "type", "initialInfo", "onSessionStart", "removeListeners", "updateHandlers", "subtractPoint", "a", "b", "_ref", "delta", "lastDevicePoint", "startDevicePoint", "velocity", "getVelocity", "<PERSON><PERSON><PERSON><PERSON>", "i", "timestampedPoint", "lastPoint", "time", "currentVelocity", "Infinity"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/test 3/medical-assistant-react/node_modules/framer-motion/dist/es/gestures/pan/PanSession.mjs"], "sourcesContent": ["import { extractEventInfo } from '../../events/event-info.mjs';\nimport { secondsToMilliseconds, millisecondsToSeconds } from '../../utils/time-conversion.mjs';\nimport { addPointerEvent } from '../../events/add-pointer-event.mjs';\nimport { pipe } from '../../utils/pipe.mjs';\nimport { distance2D } from '../../utils/distance.mjs';\nimport { isPrimaryPointer } from '../../events/utils/is-primary-pointer.mjs';\nimport { frame, cancelFrame, frameData } from '../../frameloop/frame.mjs';\n\n/**\n * @internal\n */\nclass PanSession {\n    constructor(event, handlers, { transformPagePoint, contextWindow, dragSnapToOrigin = false } = {}) {\n        /**\n         * @internal\n         */\n        this.startEvent = null;\n        /**\n         * @internal\n         */\n        this.lastMoveEvent = null;\n        /**\n         * @internal\n         */\n        this.lastMoveEventInfo = null;\n        /**\n         * @internal\n         */\n        this.handlers = {};\n        /**\n         * @internal\n         */\n        this.contextWindow = window;\n        this.updatePoint = () => {\n            if (!(this.lastMoveEvent && this.lastMoveEventInfo))\n                return;\n            const info = getPanInfo(this.lastMoveEventInfo, this.history);\n            const isPanStarted = this.startEvent !== null;\n            // Only start panning if the offset is larger than 3 pixels. If we make it\n            // any larger than this we'll want to reset the pointer history\n            // on the first update to avoid visual snapping to the cursoe.\n            const isDistancePastThreshold = distance2D(info.offset, { x: 0, y: 0 }) >= 3;\n            if (!isPanStarted && !isDistancePastThreshold)\n                return;\n            const { point } = info;\n            const { timestamp } = frameData;\n            this.history.push({ ...point, timestamp });\n            const { onStart, onMove } = this.handlers;\n            if (!isPanStarted) {\n                onStart && onStart(this.lastMoveEvent, info);\n                this.startEvent = this.lastMoveEvent;\n            }\n            onMove && onMove(this.lastMoveEvent, info);\n        };\n        this.handlePointerMove = (event, info) => {\n            this.lastMoveEvent = event;\n            this.lastMoveEventInfo = transformPoint(info, this.transformPagePoint);\n            // Throttle mouse move event to once per frame\n            frame.update(this.updatePoint, true);\n        };\n        this.handlePointerUp = (event, info) => {\n            this.end();\n            const { onEnd, onSessionEnd, resumeAnimation } = this.handlers;\n            if (this.dragSnapToOrigin)\n                resumeAnimation && resumeAnimation();\n            if (!(this.lastMoveEvent && this.lastMoveEventInfo))\n                return;\n            const panInfo = getPanInfo(event.type === \"pointercancel\"\n                ? this.lastMoveEventInfo\n                : transformPoint(info, this.transformPagePoint), this.history);\n            if (this.startEvent && onEnd) {\n                onEnd(event, panInfo);\n            }\n            onSessionEnd && onSessionEnd(event, panInfo);\n        };\n        // If we have more than one touch, don't start detecting this gesture\n        if (!isPrimaryPointer(event))\n            return;\n        this.dragSnapToOrigin = dragSnapToOrigin;\n        this.handlers = handlers;\n        this.transformPagePoint = transformPagePoint;\n        this.contextWindow = contextWindow || window;\n        const info = extractEventInfo(event);\n        const initialInfo = transformPoint(info, this.transformPagePoint);\n        const { point } = initialInfo;\n        const { timestamp } = frameData;\n        this.history = [{ ...point, timestamp }];\n        const { onSessionStart } = handlers;\n        onSessionStart &&\n            onSessionStart(event, getPanInfo(initialInfo, this.history));\n        this.removeListeners = pipe(addPointerEvent(this.contextWindow, \"pointermove\", this.handlePointerMove), addPointerEvent(this.contextWindow, \"pointerup\", this.handlePointerUp), addPointerEvent(this.contextWindow, \"pointercancel\", this.handlePointerUp));\n    }\n    updateHandlers(handlers) {\n        this.handlers = handlers;\n    }\n    end() {\n        this.removeListeners && this.removeListeners();\n        cancelFrame(this.updatePoint);\n    }\n}\nfunction transformPoint(info, transformPagePoint) {\n    return transformPagePoint ? { point: transformPagePoint(info.point) } : info;\n}\nfunction subtractPoint(a, b) {\n    return { x: a.x - b.x, y: a.y - b.y };\n}\nfunction getPanInfo({ point }, history) {\n    return {\n        point,\n        delta: subtractPoint(point, lastDevicePoint(history)),\n        offset: subtractPoint(point, startDevicePoint(history)),\n        velocity: getVelocity(history, 0.1),\n    };\n}\nfunction startDevicePoint(history) {\n    return history[0];\n}\nfunction lastDevicePoint(history) {\n    return history[history.length - 1];\n}\nfunction getVelocity(history, timeDelta) {\n    if (history.length < 2) {\n        return { x: 0, y: 0 };\n    }\n    let i = history.length - 1;\n    let timestampedPoint = null;\n    const lastPoint = lastDevicePoint(history);\n    while (i >= 0) {\n        timestampedPoint = history[i];\n        if (lastPoint.timestamp - timestampedPoint.timestamp >\n            secondsToMilliseconds(timeDelta)) {\n            break;\n        }\n        i--;\n    }\n    if (!timestampedPoint) {\n        return { x: 0, y: 0 };\n    }\n    const time = millisecondsToSeconds(lastPoint.timestamp - timestampedPoint.timestamp);\n    if (time === 0) {\n        return { x: 0, y: 0 };\n    }\n    const currentVelocity = {\n        x: (lastPoint.x - timestampedPoint.x) / time,\n        y: (lastPoint.y - timestampedPoint.y) / time,\n    };\n    if (currentVelocity.x === Infinity) {\n        currentVelocity.x = 0;\n    }\n    if (currentVelocity.y === Infinity) {\n        currentVelocity.y = 0;\n    }\n    return currentVelocity;\n}\n\nexport { PanSession };\n"], "mappings": ";AAAA,SAASA,gBAAgB,QAAQ,6BAA6B;AAC9D,SAASC,qBAAqB,EAAEC,qBAAqB,QAAQ,iCAAiC;AAC9F,SAASC,eAAe,QAAQ,oCAAoC;AACpE,SAASC,IAAI,QAAQ,sBAAsB;AAC3C,SAASC,UAAU,QAAQ,0BAA0B;AACrD,SAASC,gBAAgB,QAAQ,2CAA2C;AAC5E,SAASC,KAAK,EAAEC,WAAW,EAAEC,SAAS,QAAQ,2BAA2B;;AAEzE;AACA;AACA;AACA,MAAMC,UAAU,CAAC;EACbC,WAAWA,CAACC,KAAK,EAAEC,QAAQ,EAAwE;IAAA,IAAtE;MAAEC,kBAAkB;MAAEC,aAAa;MAAEC,gBAAgB,GAAG;IAAM,CAAC,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;IAC7F;AACR;AACA;IACQ,IAAI,CAACG,UAAU,GAAG,IAAI;IACtB;AACR;AACA;IACQ,IAAI,CAACC,aAAa,GAAG,IAAI;IACzB;AACR;AACA;IACQ,IAAI,CAACC,iBAAiB,GAAG,IAAI;IAC7B;AACR;AACA;IACQ,IAAI,CAACT,QAAQ,GAAG,CAAC,CAAC;IAClB;AACR;AACA;IACQ,IAAI,CAACE,aAAa,GAAGQ,MAAM;IAC3B,IAAI,CAACC,WAAW,GAAG,MAAM;MACrB,IAAI,EAAE,IAAI,CAACH,aAAa,IAAI,IAAI,CAACC,iBAAiB,CAAC,EAC/C;MACJ,MAAMG,IAAI,GAAGC,UAAU,CAAC,IAAI,CAACJ,iBAAiB,EAAE,IAAI,CAACK,OAAO,CAAC;MAC7D,MAAMC,YAAY,GAAG,IAAI,CAACR,UAAU,KAAK,IAAI;MAC7C;MACA;MACA;MACA,MAAMS,uBAAuB,GAAGxB,UAAU,CAACoB,IAAI,CAACK,MAAM,EAAE;QAAEC,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAC,CAAC,IAAI,CAAC;MAC5E,IAAI,CAACJ,YAAY,IAAI,CAACC,uBAAuB,EACzC;MACJ,MAAM;QAAEI;MAAM,CAAC,GAAGR,IAAI;MACtB,MAAM;QAAES;MAAU,CAAC,GAAGzB,SAAS;MAC/B,IAAI,CAACkB,OAAO,CAACQ,IAAI,CAAAC,aAAA,CAAAA,aAAA,KAAMH,KAAK;QAAEC;MAAS,EAAE,CAAC;MAC1C,MAAM;QAAEG,OAAO;QAAEC;MAAO,CAAC,GAAG,IAAI,CAACzB,QAAQ;MACzC,IAAI,CAACe,YAAY,EAAE;QACfS,OAAO,IAAIA,OAAO,CAAC,IAAI,CAAChB,aAAa,EAAEI,IAAI,CAAC;QAC5C,IAAI,CAACL,UAAU,GAAG,IAAI,CAACC,aAAa;MACxC;MACAiB,MAAM,IAAIA,MAAM,CAAC,IAAI,CAACjB,aAAa,EAAEI,IAAI,CAAC;IAC9C,CAAC;IACD,IAAI,CAACc,iBAAiB,GAAG,CAAC3B,KAAK,EAAEa,IAAI,KAAK;MACtC,IAAI,CAACJ,aAAa,GAAGT,KAAK;MAC1B,IAAI,CAACU,iBAAiB,GAAGkB,cAAc,CAACf,IAAI,EAAE,IAAI,CAACX,kBAAkB,CAAC;MACtE;MACAP,KAAK,CAACkC,MAAM,CAAC,IAAI,CAACjB,WAAW,EAAE,IAAI,CAAC;IACxC,CAAC;IACD,IAAI,CAACkB,eAAe,GAAG,CAAC9B,KAAK,EAAEa,IAAI,KAAK;MACpC,IAAI,CAACkB,GAAG,CAAC,CAAC;MACV,MAAM;QAAEC,KAAK;QAAEC,YAAY;QAAEC;MAAgB,CAAC,GAAG,IAAI,CAACjC,QAAQ;MAC9D,IAAI,IAAI,CAACG,gBAAgB,EACrB8B,eAAe,IAAIA,eAAe,CAAC,CAAC;MACxC,IAAI,EAAE,IAAI,CAACzB,aAAa,IAAI,IAAI,CAACC,iBAAiB,CAAC,EAC/C;MACJ,MAAMyB,OAAO,GAAGrB,UAAU,CAACd,KAAK,CAACoC,IAAI,KAAK,eAAe,GACnD,IAAI,CAAC1B,iBAAiB,GACtBkB,cAAc,CAACf,IAAI,EAAE,IAAI,CAACX,kBAAkB,CAAC,EAAE,IAAI,CAACa,OAAO,CAAC;MAClE,IAAI,IAAI,CAACP,UAAU,IAAIwB,KAAK,EAAE;QAC1BA,KAAK,CAAChC,KAAK,EAAEmC,OAAO,CAAC;MACzB;MACAF,YAAY,IAAIA,YAAY,CAACjC,KAAK,EAAEmC,OAAO,CAAC;IAChD,CAAC;IACD;IACA,IAAI,CAACzC,gBAAgB,CAACM,KAAK,CAAC,EACxB;IACJ,IAAI,CAACI,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACH,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACC,aAAa,GAAGA,aAAa,IAAIQ,MAAM;IAC5C,MAAME,IAAI,GAAGzB,gBAAgB,CAACY,KAAK,CAAC;IACpC,MAAMqC,WAAW,GAAGT,cAAc,CAACf,IAAI,EAAE,IAAI,CAACX,kBAAkB,CAAC;IACjE,MAAM;MAAEmB;IAAM,CAAC,GAAGgB,WAAW;IAC7B,MAAM;MAAEf;IAAU,CAAC,GAAGzB,SAAS;IAC/B,IAAI,CAACkB,OAAO,GAAG,CAAAS,aAAA,CAAAA,aAAA,KAAMH,KAAK;MAAEC;IAAS,GAAG;IACxC,MAAM;MAAEgB;IAAe,CAAC,GAAGrC,QAAQ;IACnCqC,cAAc,IACVA,cAAc,CAACtC,KAAK,EAAEc,UAAU,CAACuB,WAAW,EAAE,IAAI,CAACtB,OAAO,CAAC,CAAC;IAChE,IAAI,CAACwB,eAAe,GAAG/C,IAAI,CAACD,eAAe,CAAC,IAAI,CAACY,aAAa,EAAE,aAAa,EAAE,IAAI,CAACwB,iBAAiB,CAAC,EAAEpC,eAAe,CAAC,IAAI,CAACY,aAAa,EAAE,WAAW,EAAE,IAAI,CAAC2B,eAAe,CAAC,EAAEvC,eAAe,CAAC,IAAI,CAACY,aAAa,EAAE,eAAe,EAAE,IAAI,CAAC2B,eAAe,CAAC,CAAC;EAC/P;EACAU,cAAcA,CAACvC,QAAQ,EAAE;IACrB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;EAC5B;EACA8B,GAAGA,CAAA,EAAG;IACF,IAAI,CAACQ,eAAe,IAAI,IAAI,CAACA,eAAe,CAAC,CAAC;IAC9C3C,WAAW,CAAC,IAAI,CAACgB,WAAW,CAAC;EACjC;AACJ;AACA,SAASgB,cAAcA,CAACf,IAAI,EAAEX,kBAAkB,EAAE;EAC9C,OAAOA,kBAAkB,GAAG;IAAEmB,KAAK,EAAEnB,kBAAkB,CAACW,IAAI,CAACQ,KAAK;EAAE,CAAC,GAAGR,IAAI;AAChF;AACA,SAAS4B,aAAaA,CAACC,CAAC,EAAEC,CAAC,EAAE;EACzB,OAAO;IAAExB,CAAC,EAAEuB,CAAC,CAACvB,CAAC,GAAGwB,CAAC,CAACxB,CAAC;IAAEC,CAAC,EAAEsB,CAAC,CAACtB,CAAC,GAAGuB,CAAC,CAACvB;EAAE,CAAC;AACzC;AACA,SAASN,UAAUA,CAAA8B,IAAA,EAAY7B,OAAO,EAAE;EAAA,IAApB;IAAEM;EAAM,CAAC,GAAAuB,IAAA;EACzB,OAAO;IACHvB,KAAK;IACLwB,KAAK,EAAEJ,aAAa,CAACpB,KAAK,EAAEyB,eAAe,CAAC/B,OAAO,CAAC,CAAC;IACrDG,MAAM,EAAEuB,aAAa,CAACpB,KAAK,EAAE0B,gBAAgB,CAAChC,OAAO,CAAC,CAAC;IACvDiC,QAAQ,EAAEC,WAAW,CAAClC,OAAO,EAAE,GAAG;EACtC,CAAC;AACL;AACA,SAASgC,gBAAgBA,CAAChC,OAAO,EAAE;EAC/B,OAAOA,OAAO,CAAC,CAAC,CAAC;AACrB;AACA,SAAS+B,eAAeA,CAAC/B,OAAO,EAAE;EAC9B,OAAOA,OAAO,CAACA,OAAO,CAACT,MAAM,GAAG,CAAC,CAAC;AACtC;AACA,SAAS2C,WAAWA,CAAClC,OAAO,EAAEmC,SAAS,EAAE;EACrC,IAAInC,OAAO,CAACT,MAAM,GAAG,CAAC,EAAE;IACpB,OAAO;MAAEa,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAC;EACzB;EACA,IAAI+B,CAAC,GAAGpC,OAAO,CAACT,MAAM,GAAG,CAAC;EAC1B,IAAI8C,gBAAgB,GAAG,IAAI;EAC3B,MAAMC,SAAS,GAAGP,eAAe,CAAC/B,OAAO,CAAC;EAC1C,OAAOoC,CAAC,IAAI,CAAC,EAAE;IACXC,gBAAgB,GAAGrC,OAAO,CAACoC,CAAC,CAAC;IAC7B,IAAIE,SAAS,CAAC/B,SAAS,GAAG8B,gBAAgB,CAAC9B,SAAS,GAChDjC,qBAAqB,CAAC6D,SAAS,CAAC,EAAE;MAClC;IACJ;IACAC,CAAC,EAAE;EACP;EACA,IAAI,CAACC,gBAAgB,EAAE;IACnB,OAAO;MAAEjC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAC;EACzB;EACA,MAAMkC,IAAI,GAAGhE,qBAAqB,CAAC+D,SAAS,CAAC/B,SAAS,GAAG8B,gBAAgB,CAAC9B,SAAS,CAAC;EACpF,IAAIgC,IAAI,KAAK,CAAC,EAAE;IACZ,OAAO;MAAEnC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAC;EACzB;EACA,MAAMmC,eAAe,GAAG;IACpBpC,CAAC,EAAE,CAACkC,SAAS,CAAClC,CAAC,GAAGiC,gBAAgB,CAACjC,CAAC,IAAImC,IAAI;IAC5ClC,CAAC,EAAE,CAACiC,SAAS,CAACjC,CAAC,GAAGgC,gBAAgB,CAAChC,CAAC,IAAIkC;EAC5C,CAAC;EACD,IAAIC,eAAe,CAACpC,CAAC,KAAKqC,QAAQ,EAAE;IAChCD,eAAe,CAACpC,CAAC,GAAG,CAAC;EACzB;EACA,IAAIoC,eAAe,CAACnC,CAAC,KAAKoC,QAAQ,EAAE;IAChCD,eAAe,CAACnC,CAAC,GAAG,CAAC;EACzB;EACA,OAAOmC,eAAe;AAC1B;AAEA,SAASzD,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}