import React, { useEffect, useState } from 'react';
import { Container, Row, Col, Card, Badge } from 'react-bootstrap';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faRobot,
  faChartLine,
  faBolt,
  faShieldAlt,
  faStethoscope,
  faBrain,
  faClock,
  faUserMd,
  faHeartbeat,
  faLock,
  faGlobe,
  faMobile
} from '@fortawesome/free-solid-svg-icons';

const features = [
  {
    icon: faRobot,
    title: 'ذكاء اصطناعي متطور',
    description: 'تقنيات الذكاء الاصطناعي الحديثة لتحليل الأعراض وتقديم تشخيص أولي دقيق',
    color: 'var(--medical-primary-500)',
    bgColor: 'var(--medical-primary-50)',
    delay: '0s',
    badge: 'AI'
  },
  {
    icon: faChartLine,
    title: 'دقة عالية 98%',
    description: 'خوارزميات متقدمة مدربة على ملايين الحالات الطبية لضمان أعلى دقة',
    color: 'var(--medical-secondary-500)',
    bgColor: 'var(--medical-secondary-50)',
    delay: '0.1s',
    badge: '98%'
  },
  {
    icon: faBolt,
    title: 'نتائج فورية',
    description: 'احصل على تشخيص أولي في ثوانٍ معدودة مع توصيات طبية مخصصة',
    color: 'var(--medical-accent-500)',
    bgColor: 'var(--medical-accent-50)',
    delay: '0.2s',
    badge: '< 30s'
  },
  {
    icon: faShieldAlt,
    title: 'آمن ومعتمد',
    description: 'حماية كاملة لبياناتك الطبية مع الامتثال لمعايير الخصوصية الدولية',
    color: 'var(--medical-primary-600)',
    bgColor: 'var(--medical-primary-50)',
    delay: '0.3s',
    badge: 'آمن'
  },
  {
    icon: faStethoscope,
    title: 'استشارة طبية',
    description: 'إمكانية التواصل المباشر مع أطباء متخصصين لمتابعة التشخيص',
    color: 'var(--medical-secondary-600)',
    bgColor: 'var(--medical-secondary-50)',
    delay: '0.4s',
    badge: '24/7'
  },
  {
    icon: faBrain,
    title: 'تعلم مستمر',
    description: 'النظام يتطور باستمرار ويتعلم من كل حالة لتحسين دقة التشخيص',
    color: 'var(--medical-accent-600)',
    bgColor: 'var(--medical-accent-50)',
    delay: '0.5s',
    badge: 'تطوير'
  }
];

const FeaturesSection: React.FC = () => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.1 }
    );

    const section = document.getElementById('features-section');
    if (section) {
      observer.observe(section);
    }

    return () => observer.disconnect();
  }, []);

  return (
    <section id="features-section" className="py-5 py-md-6 bg-light position-relative overflow-hidden">
      {/* Background Pattern */}
      <div className="position-absolute top-0 start-0 w-100 h-100 opacity-25">
        <div className="position-absolute" style={{
          top: '20%',
          right: '10%',
          width: '100px',
          height: '100px',
          background: 'var(--medical-primary-100)',
          borderRadius: '50%',
          transform: 'rotate(45deg)'
        }}></div>
        <div className="position-absolute" style={{
          bottom: '20%',
          left: '5%',
          width: '80px',
          height: '80px',
          background: 'var(--medical-accent-100)',
          borderRadius: '50%'
        }}></div>
      </div>

      <Container className="position-relative">
        <Row className="justify-content-center text-center mb-5">
          <Col lg={10}>
            <div className={`${isVisible ? 'animate-fadeIn' : ''}`}>
              <Badge bg="primary" className="px-3 py-2 rounded-pill mb-3">
                <FontAwesomeIcon icon={faHeartbeat} className="me-2" />
                مزايا متقدمة
              </Badge>
              <h2 className="display-4 fw-bold mb-4 text-gradient-medical">
                لماذا تختار مساعدك الطبي الذكي؟
              </h2>
              <p className="lead text-muted mb-0">
                منصة طبية متطورة تجمع بين أحدث تقنيات الذكاء الاصطناعي والخبرة الطبية
                لتقديم خدمة صحية شاملة ومتميزة
              </p>
            </div>
          </Col>
        </Row>

        <Row className="g-4">
          {features.map((feature, index) => (
            <Col md={6} lg={4} key={index}>
              <Card
                className={`h-100 border-0 shadow-sm hover-lift card-medical ${
                  isVisible ? 'animate-scaleIn' : ''
                }`}
                style={{
                  animationDelay: feature.delay,
                  background: `linear-gradient(145deg, white 0%, ${feature.bgColor} 100%)`
                }}
              >
                <Card.Body className="text-center p-4 position-relative">
                  {/* Badge */}
                  <div className="position-absolute top-0 end-0 m-3">
                    <Badge
                      style={{
                        backgroundColor: feature.color,
                        fontSize: '0.7rem'
                      }}
                      className="rounded-pill"
                    >
                      {feature.badge}
                    </Badge>
                  </div>

                  {/* Icon */}
                  <div className="feature-icon mb-4 position-relative">
                    <div
                      className="d-inline-flex align-items-center justify-content-center rounded-circle mb-3"
                      style={{
                        width: '80px',
                        height: '80px',
                        backgroundColor: feature.bgColor,
                        border: `3px solid ${feature.color}20`
                      }}
                    >
                      <FontAwesomeIcon
                        icon={feature.icon}
                        style={{
                          fontSize: '2rem',
                          color: feature.color
                        }}
                      />
                    </div>
                    {/* Pulse effect */}
                    <div
                      className="position-absolute top-50 start-50 translate-middle rounded-circle animate-pulse-medical"
                      style={{
                        width: '80px',
                        height: '80px',
                        backgroundColor: `${feature.color}20`,
                        zIndex: -1
                      }}
                    ></div>
                  </div>

                  <Card.Title className="h5 fw-bold mb-3" style={{ color: feature.color }}>
                    {feature.title}
                  </Card.Title>
                  <Card.Text className="text-muted lh-lg">
                    {feature.description}
                  </Card.Text>
                </Card.Body>
              </Card>
            </Col>
          ))}
        </Row>

        {/* Additional Info Section */}
        <Row className="mt-5 pt-5 border-top">
          <Col lg={12} className="text-center">
            <div className={`${isVisible ? 'animate-slideInUp' : ''}`} style={{ animationDelay: '0.6s' }}>
              <h3 className="h4 fw-bold mb-4">تقنيات متقدمة في خدمتك</h3>
              <Row className="justify-content-center">
                <Col md={3} className="mb-3">
                  <div className="d-flex align-items-center justify-content-center">
                    <FontAwesomeIcon icon={faMobile} className="text-primary me-2" />
                    <span className="small text-muted">تطبيق جوال</span>
                  </div>
                </Col>
                <Col md={3} className="mb-3">
                  <div className="d-flex align-items-center justify-content-center">
                    <FontAwesomeIcon icon={faGlobe} className="text-primary me-2" />
                    <span className="small text-muted">متاح عالمياً</span>
                  </div>
                </Col>
                <Col md={3} className="mb-3">
                  <div className="d-flex align-items-center justify-content-center">
                    <FontAwesomeIcon icon={faLock} className="text-primary me-2" />
                    <span className="small text-muted">حماية البيانات</span>
                  </div>
                </Col>
                <Col md={3} className="mb-3">
                  <div className="d-flex align-items-center justify-content-center">
                    <FontAwesomeIcon icon={faUserMd} className="text-primary me-2" />
                    <span className="small text-muted">أطباء معتمدون</span>
                  </div>
                </Col>
              </Row>
            </div>
          </Col>
        </Row>
      </Container>
    </section>
  );
};

export default FeaturesSection;
