{"ast": null, "code": "const Mode = require('./mode');\nconst NumericData = require('./numeric-data');\nconst AlphanumericData = require('./alphanumeric-data');\nconst ByteData = require('./byte-data');\nconst KanjiData = require('./kanji-data');\nconst Regex = require('./regex');\nconst Utils = require('./utils');\nconst dijkstra = require('dijkstrajs');\n\n/**\n * Returns UTF8 byte length\n *\n * @param  {String} str Input string\n * @return {Number}     Number of byte\n */\nfunction getStringByteLength(str) {\n  return unescape(encodeURIComponent(str)).length;\n}\n\n/**\n * Get a list of segments of the specified mode\n * from a string\n *\n * @param  {Mode}   mode Segment mode\n * @param  {String} str  String to process\n * @return {Array}       Array of object with segments data\n */\nfunction getSegments(regex, mode, str) {\n  const segments = [];\n  let result;\n  while ((result = regex.exec(str)) !== null) {\n    segments.push({\n      data: result[0],\n      index: result.index,\n      mode: mode,\n      length: result[0].length\n    });\n  }\n  return segments;\n}\n\n/**\n * Extracts a series of segments with the appropriate\n * modes from a string\n *\n * @param  {String} dataStr Input string\n * @return {Array}          Array of object with segments data\n */\nfunction getSegmentsFromString(dataStr) {\n  const numSegs = getSegments(Regex.NUMERIC, Mode.NUMERIC, dataStr);\n  const alphaNumSegs = getSegments(Regex.ALPHANUMERIC, Mode.ALPHANUMERIC, dataStr);\n  let byteSegs;\n  let kanjiSegs;\n  if (Utils.isKanjiModeEnabled()) {\n    byteSegs = getSegments(Regex.BYTE, Mode.BYTE, dataStr);\n    kanjiSegs = getSegments(Regex.KANJI, Mode.KANJI, dataStr);\n  } else {\n    byteSegs = getSegments(Regex.BYTE_KANJI, Mode.BYTE, dataStr);\n    kanjiSegs = [];\n  }\n  const segs = numSegs.concat(alphaNumSegs, byteSegs, kanjiSegs);\n  return segs.sort(function (s1, s2) {\n    return s1.index - s2.index;\n  }).map(function (obj) {\n    return {\n      data: obj.data,\n      mode: obj.mode,\n      length: obj.length\n    };\n  });\n}\n\n/**\n * Returns how many bits are needed to encode a string of\n * specified length with the specified mode\n *\n * @param  {Number} length String length\n * @param  {Mode} mode     Segment mode\n * @return {Number}        Bit length\n */\nfunction getSegmentBitsLength(length, mode) {\n  switch (mode) {\n    case Mode.NUMERIC:\n      return NumericData.getBitsLength(length);\n    case Mode.ALPHANUMERIC:\n      return AlphanumericData.getBitsLength(length);\n    case Mode.KANJI:\n      return KanjiData.getBitsLength(length);\n    case Mode.BYTE:\n      return ByteData.getBitsLength(length);\n  }\n}\n\n/**\n * Merges adjacent segments which have the same mode\n *\n * @param  {Array} segs Array of object with segments data\n * @return {Array}      Array of object with segments data\n */\nfunction mergeSegments(segs) {\n  return segs.reduce(function (acc, curr) {\n    const prevSeg = acc.length - 1 >= 0 ? acc[acc.length - 1] : null;\n    if (prevSeg && prevSeg.mode === curr.mode) {\n      acc[acc.length - 1].data += curr.data;\n      return acc;\n    }\n    acc.push(curr);\n    return acc;\n  }, []);\n}\n\n/**\n * Generates a list of all possible nodes combination which\n * will be used to build a segments graph.\n *\n * Nodes are divided by groups. Each group will contain a list of all the modes\n * in which is possible to encode the given text.\n *\n * For example the text '12345' can be encoded as Numeric, Alphanumeric or Byte.\n * The group for '12345' will contain then 3 objects, one for each\n * possible encoding mode.\n *\n * Each node represents a possible segment.\n *\n * @param  {Array} segs Array of object with segments data\n * @return {Array}      Array of object with segments data\n */\nfunction buildNodes(segs) {\n  const nodes = [];\n  for (let i = 0; i < segs.length; i++) {\n    const seg = segs[i];\n    switch (seg.mode) {\n      case Mode.NUMERIC:\n        nodes.push([seg, {\n          data: seg.data,\n          mode: Mode.ALPHANUMERIC,\n          length: seg.length\n        }, {\n          data: seg.data,\n          mode: Mode.BYTE,\n          length: seg.length\n        }]);\n        break;\n      case Mode.ALPHANUMERIC:\n        nodes.push([seg, {\n          data: seg.data,\n          mode: Mode.BYTE,\n          length: seg.length\n        }]);\n        break;\n      case Mode.KANJI:\n        nodes.push([seg, {\n          data: seg.data,\n          mode: Mode.BYTE,\n          length: getStringByteLength(seg.data)\n        }]);\n        break;\n      case Mode.BYTE:\n        nodes.push([{\n          data: seg.data,\n          mode: Mode.BYTE,\n          length: getStringByteLength(seg.data)\n        }]);\n    }\n  }\n  return nodes;\n}\n\n/**\n * Builds a graph from a list of nodes.\n * All segments in each node group will be connected with all the segments of\n * the next group and so on.\n *\n * At each connection will be assigned a weight depending on the\n * segment's byte length.\n *\n * @param  {Array} nodes    Array of object with segments data\n * @param  {Number} version QR Code version\n * @return {Object}         Graph of all possible segments\n */\nfunction buildGraph(nodes, version) {\n  const table = {};\n  const graph = {\n    start: {}\n  };\n  let prevNodeIds = ['start'];\n  for (let i = 0; i < nodes.length; i++) {\n    const nodeGroup = nodes[i];\n    const currentNodeIds = [];\n    for (let j = 0; j < nodeGroup.length; j++) {\n      const node = nodeGroup[j];\n      const key = '' + i + j;\n      currentNodeIds.push(key);\n      table[key] = {\n        node: node,\n        lastCount: 0\n      };\n      graph[key] = {};\n      for (let n = 0; n < prevNodeIds.length; n++) {\n        const prevNodeId = prevNodeIds[n];\n        if (table[prevNodeId] && table[prevNodeId].node.mode === node.mode) {\n          graph[prevNodeId][key] = getSegmentBitsLength(table[prevNodeId].lastCount + node.length, node.mode) - getSegmentBitsLength(table[prevNodeId].lastCount, node.mode);\n          table[prevNodeId].lastCount += node.length;\n        } else {\n          if (table[prevNodeId]) table[prevNodeId].lastCount = node.length;\n          graph[prevNodeId][key] = getSegmentBitsLength(node.length, node.mode) + 4 + Mode.getCharCountIndicator(node.mode, version); // switch cost\n        }\n      }\n    }\n    prevNodeIds = currentNodeIds;\n  }\n  for (let n = 0; n < prevNodeIds.length; n++) {\n    graph[prevNodeIds[n]].end = 0;\n  }\n  return {\n    map: graph,\n    table: table\n  };\n}\n\n/**\n * Builds a segment from a specified data and mode.\n * If a mode is not specified, the more suitable will be used.\n *\n * @param  {String} data             Input data\n * @param  {Mode | String} modesHint Data mode\n * @return {Segment}                 Segment\n */\nfunction buildSingleSegment(data, modesHint) {\n  let mode;\n  const bestMode = Mode.getBestModeForData(data);\n  mode = Mode.from(modesHint, bestMode);\n\n  // Make sure data can be encoded\n  if (mode !== Mode.BYTE && mode.bit < bestMode.bit) {\n    throw new Error('\"' + data + '\"' + ' cannot be encoded with mode ' + Mode.toString(mode) + '.\\n Suggested mode is: ' + Mode.toString(bestMode));\n  }\n\n  // Use Mode.BYTE if Kanji support is disabled\n  if (mode === Mode.KANJI && !Utils.isKanjiModeEnabled()) {\n    mode = Mode.BYTE;\n  }\n  switch (mode) {\n    case Mode.NUMERIC:\n      return new NumericData(data);\n    case Mode.ALPHANUMERIC:\n      return new AlphanumericData(data);\n    case Mode.KANJI:\n      return new KanjiData(data);\n    case Mode.BYTE:\n      return new ByteData(data);\n  }\n}\n\n/**\n * Builds a list of segments from an array.\n * Array can contain Strings or Objects with segment's info.\n *\n * For each item which is a string, will be generated a segment with the given\n * string and the more appropriate encoding mode.\n *\n * For each item which is an object, will be generated a segment with the given\n * data and mode.\n * Objects must contain at least the property \"data\".\n * If property \"mode\" is not present, the more suitable mode will be used.\n *\n * @param  {Array} array Array of objects with segments data\n * @return {Array}       Array of Segments\n */\nexports.fromArray = function fromArray(array) {\n  return array.reduce(function (acc, seg) {\n    if (typeof seg === 'string') {\n      acc.push(buildSingleSegment(seg, null));\n    } else if (seg.data) {\n      acc.push(buildSingleSegment(seg.data, seg.mode));\n    }\n    return acc;\n  }, []);\n};\n\n/**\n * Builds an optimized sequence of segments from a string,\n * which will produce the shortest possible bitstream.\n *\n * @param  {String} data    Input string\n * @param  {Number} version QR Code version\n * @return {Array}          Array of segments\n */\nexports.fromString = function fromString(data, version) {\n  const segs = getSegmentsFromString(data, Utils.isKanjiModeEnabled());\n  const nodes = buildNodes(segs);\n  const graph = buildGraph(nodes, version);\n  const path = dijkstra.find_path(graph.map, 'start', 'end');\n  const optimizedSegs = [];\n  for (let i = 1; i < path.length - 1; i++) {\n    optimizedSegs.push(graph.table[path[i]].node);\n  }\n  return exports.fromArray(mergeSegments(optimizedSegs));\n};\n\n/**\n * Splits a string in various segments with the modes which\n * best represent their content.\n * The produced segments are far from being optimized.\n * The output of this function is only used to estimate a QR Code version\n * which may contain the data.\n *\n * @param  {string} data Input string\n * @return {Array}       Array of segments\n */\nexports.rawSplit = function rawSplit(data) {\n  return exports.fromArray(getSegmentsFromString(data, Utils.isKanjiModeEnabled()));\n};", "map": {"version": 3, "names": ["Mode", "require", "NumericData", "AlphanumericData", "ByteData", "KanjiData", "Regex", "Utils", "<PERSON><PERSON><PERSON>", "getStringByteLength", "str", "unescape", "encodeURIComponent", "length", "getSegments", "regex", "mode", "segments", "result", "exec", "push", "data", "index", "getSegmentsFromString", "dataStr", "numSegs", "NUMERIC", "alphaNumSegs", "ALPHANUMERIC", "byteSegs", "kanjiSegs", "isKanjiModeEnabled", "BYTE", "KANJI", "BYTE_KANJI", "segs", "concat", "sort", "s1", "s2", "map", "obj", "getSegmentBitsLength", "getBitsLength", "mergeSegments", "reduce", "acc", "curr", "prevSeg", "buildNodes", "nodes", "i", "seg", "buildGraph", "version", "table", "graph", "start", "prevNodeIds", "nodeGroup", "currentNodeIds", "j", "node", "key", "lastCount", "n", "prevNodeId", "getCharCountIndicator", "end", "buildSingleSegment", "modesHint", "bestMode", "getBestModeForData", "from", "bit", "Error", "toString", "exports", "fromArray", "array", "fromString", "path", "find_path", "optimizedSegs", "rawSplit"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/test 3/medical-assistant-react/node_modules/qrcode/lib/core/segments.js"], "sourcesContent": ["const Mode = require('./mode')\nconst NumericData = require('./numeric-data')\nconst AlphanumericData = require('./alphanumeric-data')\nconst ByteData = require('./byte-data')\nconst KanjiData = require('./kanji-data')\nconst Regex = require('./regex')\nconst Utils = require('./utils')\nconst dijkstra = require('dijkstrajs')\n\n/**\n * Returns UTF8 byte length\n *\n * @param  {String} str Input string\n * @return {Number}     Number of byte\n */\nfunction getStringByteLength (str) {\n  return unescape(encodeURIComponent(str)).length\n}\n\n/**\n * Get a list of segments of the specified mode\n * from a string\n *\n * @param  {Mode}   mode Segment mode\n * @param  {String} str  String to process\n * @return {Array}       Array of object with segments data\n */\nfunction getSegments (regex, mode, str) {\n  const segments = []\n  let result\n\n  while ((result = regex.exec(str)) !== null) {\n    segments.push({\n      data: result[0],\n      index: result.index,\n      mode: mode,\n      length: result[0].length\n    })\n  }\n\n  return segments\n}\n\n/**\n * Extracts a series of segments with the appropriate\n * modes from a string\n *\n * @param  {String} dataStr Input string\n * @return {Array}          Array of object with segments data\n */\nfunction getSegmentsFromString (dataStr) {\n  const numSegs = getSegments(Regex.NUMERIC, Mode.NUMERIC, dataStr)\n  const alphaNumSegs = getSegments(Regex.ALPHANUMERIC, Mode.ALPHANUMERIC, dataStr)\n  let byteSegs\n  let kanjiSegs\n\n  if (Utils.isKanjiModeEnabled()) {\n    byteSegs = getSegments(Regex.BYTE, Mode.BYTE, dataStr)\n    kanjiSegs = getSegments(Regex.KANJI, Mode.KANJI, dataStr)\n  } else {\n    byteSegs = getSegments(Regex.BYTE_KANJI, Mode.BYTE, dataStr)\n    kanjiSegs = []\n  }\n\n  const segs = numSegs.concat(alphaNumSegs, byteSegs, kanjiSegs)\n\n  return segs\n    .sort(function (s1, s2) {\n      return s1.index - s2.index\n    })\n    .map(function (obj) {\n      return {\n        data: obj.data,\n        mode: obj.mode,\n        length: obj.length\n      }\n    })\n}\n\n/**\n * Returns how many bits are needed to encode a string of\n * specified length with the specified mode\n *\n * @param  {Number} length String length\n * @param  {Mode} mode     Segment mode\n * @return {Number}        Bit length\n */\nfunction getSegmentBitsLength (length, mode) {\n  switch (mode) {\n    case Mode.NUMERIC:\n      return NumericData.getBitsLength(length)\n    case Mode.ALPHANUMERIC:\n      return AlphanumericData.getBitsLength(length)\n    case Mode.KANJI:\n      return KanjiData.getBitsLength(length)\n    case Mode.BYTE:\n      return ByteData.getBitsLength(length)\n  }\n}\n\n/**\n * Merges adjacent segments which have the same mode\n *\n * @param  {Array} segs Array of object with segments data\n * @return {Array}      Array of object with segments data\n */\nfunction mergeSegments (segs) {\n  return segs.reduce(function (acc, curr) {\n    const prevSeg = acc.length - 1 >= 0 ? acc[acc.length - 1] : null\n    if (prevSeg && prevSeg.mode === curr.mode) {\n      acc[acc.length - 1].data += curr.data\n      return acc\n    }\n\n    acc.push(curr)\n    return acc\n  }, [])\n}\n\n/**\n * Generates a list of all possible nodes combination which\n * will be used to build a segments graph.\n *\n * Nodes are divided by groups. Each group will contain a list of all the modes\n * in which is possible to encode the given text.\n *\n * For example the text '12345' can be encoded as Numeric, Alphanumeric or Byte.\n * The group for '12345' will contain then 3 objects, one for each\n * possible encoding mode.\n *\n * Each node represents a possible segment.\n *\n * @param  {Array} segs Array of object with segments data\n * @return {Array}      Array of object with segments data\n */\nfunction buildNodes (segs) {\n  const nodes = []\n  for (let i = 0; i < segs.length; i++) {\n    const seg = segs[i]\n\n    switch (seg.mode) {\n      case Mode.NUMERIC:\n        nodes.push([seg,\n          { data: seg.data, mode: Mode.ALPHANUMERIC, length: seg.length },\n          { data: seg.data, mode: Mode.BYTE, length: seg.length }\n        ])\n        break\n      case Mode.ALPHANUMERIC:\n        nodes.push([seg,\n          { data: seg.data, mode: Mode.BYTE, length: seg.length }\n        ])\n        break\n      case Mode.KANJI:\n        nodes.push([seg,\n          { data: seg.data, mode: Mode.BYTE, length: getStringByteLength(seg.data) }\n        ])\n        break\n      case Mode.BYTE:\n        nodes.push([\n          { data: seg.data, mode: Mode.BYTE, length: getStringByteLength(seg.data) }\n        ])\n    }\n  }\n\n  return nodes\n}\n\n/**\n * Builds a graph from a list of nodes.\n * All segments in each node group will be connected with all the segments of\n * the next group and so on.\n *\n * At each connection will be assigned a weight depending on the\n * segment's byte length.\n *\n * @param  {Array} nodes    Array of object with segments data\n * @param  {Number} version QR Code version\n * @return {Object}         Graph of all possible segments\n */\nfunction buildGraph (nodes, version) {\n  const table = {}\n  const graph = { start: {} }\n  let prevNodeIds = ['start']\n\n  for (let i = 0; i < nodes.length; i++) {\n    const nodeGroup = nodes[i]\n    const currentNodeIds = []\n\n    for (let j = 0; j < nodeGroup.length; j++) {\n      const node = nodeGroup[j]\n      const key = '' + i + j\n\n      currentNodeIds.push(key)\n      table[key] = { node: node, lastCount: 0 }\n      graph[key] = {}\n\n      for (let n = 0; n < prevNodeIds.length; n++) {\n        const prevNodeId = prevNodeIds[n]\n\n        if (table[prevNodeId] && table[prevNodeId].node.mode === node.mode) {\n          graph[prevNodeId][key] =\n            getSegmentBitsLength(table[prevNodeId].lastCount + node.length, node.mode) -\n            getSegmentBitsLength(table[prevNodeId].lastCount, node.mode)\n\n          table[prevNodeId].lastCount += node.length\n        } else {\n          if (table[prevNodeId]) table[prevNodeId].lastCount = node.length\n\n          graph[prevNodeId][key] = getSegmentBitsLength(node.length, node.mode) +\n            4 + Mode.getCharCountIndicator(node.mode, version) // switch cost\n        }\n      }\n    }\n\n    prevNodeIds = currentNodeIds\n  }\n\n  for (let n = 0; n < prevNodeIds.length; n++) {\n    graph[prevNodeIds[n]].end = 0\n  }\n\n  return { map: graph, table: table }\n}\n\n/**\n * Builds a segment from a specified data and mode.\n * If a mode is not specified, the more suitable will be used.\n *\n * @param  {String} data             Input data\n * @param  {Mode | String} modesHint Data mode\n * @return {Segment}                 Segment\n */\nfunction buildSingleSegment (data, modesHint) {\n  let mode\n  const bestMode = Mode.getBestModeForData(data)\n\n  mode = Mode.from(modesHint, bestMode)\n\n  // Make sure data can be encoded\n  if (mode !== Mode.BYTE && mode.bit < bestMode.bit) {\n    throw new Error('\"' + data + '\"' +\n      ' cannot be encoded with mode ' + Mode.toString(mode) +\n      '.\\n Suggested mode is: ' + Mode.toString(bestMode))\n  }\n\n  // Use Mode.BYTE if Kanji support is disabled\n  if (mode === Mode.KANJI && !Utils.isKanjiModeEnabled()) {\n    mode = Mode.BYTE\n  }\n\n  switch (mode) {\n    case Mode.NUMERIC:\n      return new NumericData(data)\n\n    case Mode.ALPHANUMERIC:\n      return new AlphanumericData(data)\n\n    case Mode.KANJI:\n      return new KanjiData(data)\n\n    case Mode.BYTE:\n      return new ByteData(data)\n  }\n}\n\n/**\n * Builds a list of segments from an array.\n * Array can contain Strings or Objects with segment's info.\n *\n * For each item which is a string, will be generated a segment with the given\n * string and the more appropriate encoding mode.\n *\n * For each item which is an object, will be generated a segment with the given\n * data and mode.\n * Objects must contain at least the property \"data\".\n * If property \"mode\" is not present, the more suitable mode will be used.\n *\n * @param  {Array} array Array of objects with segments data\n * @return {Array}       Array of Segments\n */\nexports.fromArray = function fromArray (array) {\n  return array.reduce(function (acc, seg) {\n    if (typeof seg === 'string') {\n      acc.push(buildSingleSegment(seg, null))\n    } else if (seg.data) {\n      acc.push(buildSingleSegment(seg.data, seg.mode))\n    }\n\n    return acc\n  }, [])\n}\n\n/**\n * Builds an optimized sequence of segments from a string,\n * which will produce the shortest possible bitstream.\n *\n * @param  {String} data    Input string\n * @param  {Number} version QR Code version\n * @return {Array}          Array of segments\n */\nexports.fromString = function fromString (data, version) {\n  const segs = getSegmentsFromString(data, Utils.isKanjiModeEnabled())\n\n  const nodes = buildNodes(segs)\n  const graph = buildGraph(nodes, version)\n  const path = dijkstra.find_path(graph.map, 'start', 'end')\n\n  const optimizedSegs = []\n  for (let i = 1; i < path.length - 1; i++) {\n    optimizedSegs.push(graph.table[path[i]].node)\n  }\n\n  return exports.fromArray(mergeSegments(optimizedSegs))\n}\n\n/**\n * Splits a string in various segments with the modes which\n * best represent their content.\n * The produced segments are far from being optimized.\n * The output of this function is only used to estimate a QR Code version\n * which may contain the data.\n *\n * @param  {string} data Input string\n * @return {Array}       Array of segments\n */\nexports.rawSplit = function rawSplit (data) {\n  return exports.fromArray(\n    getSegmentsFromString(data, Utils.isKanjiModeEnabled())\n  )\n}\n"], "mappings": "AAAA,MAAMA,IAAI,GAAGC,OAAO,CAAC,QAAQ,CAAC;AAC9B,MAAMC,WAAW,GAAGD,OAAO,CAAC,gBAAgB,CAAC;AAC7C,MAAME,gBAAgB,GAAGF,OAAO,CAAC,qBAAqB,CAAC;AACvD,MAAMG,QAAQ,GAAGH,OAAO,CAAC,aAAa,CAAC;AACvC,MAAMI,SAAS,GAAGJ,OAAO,CAAC,cAAc,CAAC;AACzC,MAAMK,KAAK,GAAGL,OAAO,CAAC,SAAS,CAAC;AAChC,MAAMM,KAAK,GAAGN,OAAO,CAAC,SAAS,CAAC;AAChC,MAAMO,QAAQ,GAAGP,OAAO,CAAC,YAAY,CAAC;;AAEtC;AACA;AACA;AACA;AACA;AACA;AACA,SAASQ,mBAAmBA,CAAEC,GAAG,EAAE;EACjC,OAAOC,QAAQ,CAACC,kBAAkB,CAACF,GAAG,CAAC,CAAC,CAACG,MAAM;AACjD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,WAAWA,CAAEC,KAAK,EAAEC,IAAI,EAAEN,GAAG,EAAE;EACtC,MAAMO,QAAQ,GAAG,EAAE;EACnB,IAAIC,MAAM;EAEV,OAAO,CAACA,MAAM,GAAGH,KAAK,CAACI,IAAI,CAACT,GAAG,CAAC,MAAM,IAAI,EAAE;IAC1CO,QAAQ,CAACG,IAAI,CAAC;MACZC,IAAI,EAAEH,MAAM,CAAC,CAAC,CAAC;MACfI,KAAK,EAAEJ,MAAM,CAACI,KAAK;MACnBN,IAAI,EAAEA,IAAI;MACVH,MAAM,EAAEK,MAAM,CAAC,CAAC,CAAC,CAACL;IACpB,CAAC,CAAC;EACJ;EAEA,OAAOI,QAAQ;AACjB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASM,qBAAqBA,CAAEC,OAAO,EAAE;EACvC,MAAMC,OAAO,GAAGX,WAAW,CAACR,KAAK,CAACoB,OAAO,EAAE1B,IAAI,CAAC0B,OAAO,EAAEF,OAAO,CAAC;EACjE,MAAMG,YAAY,GAAGb,WAAW,CAACR,KAAK,CAACsB,YAAY,EAAE5B,IAAI,CAAC4B,YAAY,EAAEJ,OAAO,CAAC;EAChF,IAAIK,QAAQ;EACZ,IAAIC,SAAS;EAEb,IAAIvB,KAAK,CAACwB,kBAAkB,CAAC,CAAC,EAAE;IAC9BF,QAAQ,GAAGf,WAAW,CAACR,KAAK,CAAC0B,IAAI,EAAEhC,IAAI,CAACgC,IAAI,EAAER,OAAO,CAAC;IACtDM,SAAS,GAAGhB,WAAW,CAACR,KAAK,CAAC2B,KAAK,EAAEjC,IAAI,CAACiC,KAAK,EAAET,OAAO,CAAC;EAC3D,CAAC,MAAM;IACLK,QAAQ,GAAGf,WAAW,CAACR,KAAK,CAAC4B,UAAU,EAAElC,IAAI,CAACgC,IAAI,EAAER,OAAO,CAAC;IAC5DM,SAAS,GAAG,EAAE;EAChB;EAEA,MAAMK,IAAI,GAAGV,OAAO,CAACW,MAAM,CAACT,YAAY,EAAEE,QAAQ,EAAEC,SAAS,CAAC;EAE9D,OAAOK,IAAI,CACRE,IAAI,CAAC,UAAUC,EAAE,EAAEC,EAAE,EAAE;IACtB,OAAOD,EAAE,CAAChB,KAAK,GAAGiB,EAAE,CAACjB,KAAK;EAC5B,CAAC,CAAC,CACDkB,GAAG,CAAC,UAAUC,GAAG,EAAE;IAClB,OAAO;MACLpB,IAAI,EAAEoB,GAAG,CAACpB,IAAI;MACdL,IAAI,EAAEyB,GAAG,CAACzB,IAAI;MACdH,MAAM,EAAE4B,GAAG,CAAC5B;IACd,CAAC;EACH,CAAC,CAAC;AACN;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS6B,oBAAoBA,CAAE7B,MAAM,EAAEG,IAAI,EAAE;EAC3C,QAAQA,IAAI;IACV,KAAKhB,IAAI,CAAC0B,OAAO;MACf,OAAOxB,WAAW,CAACyC,aAAa,CAAC9B,MAAM,CAAC;IAC1C,KAAKb,IAAI,CAAC4B,YAAY;MACpB,OAAOzB,gBAAgB,CAACwC,aAAa,CAAC9B,MAAM,CAAC;IAC/C,KAAKb,IAAI,CAACiC,KAAK;MACb,OAAO5B,SAAS,CAACsC,aAAa,CAAC9B,MAAM,CAAC;IACxC,KAAKb,IAAI,CAACgC,IAAI;MACZ,OAAO5B,QAAQ,CAACuC,aAAa,CAAC9B,MAAM,CAAC;EACzC;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS+B,aAAaA,CAAET,IAAI,EAAE;EAC5B,OAAOA,IAAI,CAACU,MAAM,CAAC,UAAUC,GAAG,EAAEC,IAAI,EAAE;IACtC,MAAMC,OAAO,GAAGF,GAAG,CAACjC,MAAM,GAAG,CAAC,IAAI,CAAC,GAAGiC,GAAG,CAACA,GAAG,CAACjC,MAAM,GAAG,CAAC,CAAC,GAAG,IAAI;IAChE,IAAImC,OAAO,IAAIA,OAAO,CAAChC,IAAI,KAAK+B,IAAI,CAAC/B,IAAI,EAAE;MACzC8B,GAAG,CAACA,GAAG,CAACjC,MAAM,GAAG,CAAC,CAAC,CAACQ,IAAI,IAAI0B,IAAI,CAAC1B,IAAI;MACrC,OAAOyB,GAAG;IACZ;IAEAA,GAAG,CAAC1B,IAAI,CAAC2B,IAAI,CAAC;IACd,OAAOD,GAAG;EACZ,CAAC,EAAE,EAAE,CAAC;AACR;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,UAAUA,CAAEd,IAAI,EAAE;EACzB,MAAMe,KAAK,GAAG,EAAE;EAChB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGhB,IAAI,CAACtB,MAAM,EAAEsC,CAAC,EAAE,EAAE;IACpC,MAAMC,GAAG,GAAGjB,IAAI,CAACgB,CAAC,CAAC;IAEnB,QAAQC,GAAG,CAACpC,IAAI;MACd,KAAKhB,IAAI,CAAC0B,OAAO;QACfwB,KAAK,CAAC9B,IAAI,CAAC,CAACgC,GAAG,EACb;UAAE/B,IAAI,EAAE+B,GAAG,CAAC/B,IAAI;UAAEL,IAAI,EAAEhB,IAAI,CAAC4B,YAAY;UAAEf,MAAM,EAAEuC,GAAG,CAACvC;QAAO,CAAC,EAC/D;UAAEQ,IAAI,EAAE+B,GAAG,CAAC/B,IAAI;UAAEL,IAAI,EAAEhB,IAAI,CAACgC,IAAI;UAAEnB,MAAM,EAAEuC,GAAG,CAACvC;QAAO,CAAC,CACxD,CAAC;QACF;MACF,KAAKb,IAAI,CAAC4B,YAAY;QACpBsB,KAAK,CAAC9B,IAAI,CAAC,CAACgC,GAAG,EACb;UAAE/B,IAAI,EAAE+B,GAAG,CAAC/B,IAAI;UAAEL,IAAI,EAAEhB,IAAI,CAACgC,IAAI;UAAEnB,MAAM,EAAEuC,GAAG,CAACvC;QAAO,CAAC,CACxD,CAAC;QACF;MACF,KAAKb,IAAI,CAACiC,KAAK;QACbiB,KAAK,CAAC9B,IAAI,CAAC,CAACgC,GAAG,EACb;UAAE/B,IAAI,EAAE+B,GAAG,CAAC/B,IAAI;UAAEL,IAAI,EAAEhB,IAAI,CAACgC,IAAI;UAAEnB,MAAM,EAAEJ,mBAAmB,CAAC2C,GAAG,CAAC/B,IAAI;QAAE,CAAC,CAC3E,CAAC;QACF;MACF,KAAKrB,IAAI,CAACgC,IAAI;QACZkB,KAAK,CAAC9B,IAAI,CAAC,CACT;UAAEC,IAAI,EAAE+B,GAAG,CAAC/B,IAAI;UAAEL,IAAI,EAAEhB,IAAI,CAACgC,IAAI;UAAEnB,MAAM,EAAEJ,mBAAmB,CAAC2C,GAAG,CAAC/B,IAAI;QAAE,CAAC,CAC3E,CAAC;IACN;EACF;EAEA,OAAO6B,KAAK;AACd;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,UAAUA,CAAEH,KAAK,EAAEI,OAAO,EAAE;EACnC,MAAMC,KAAK,GAAG,CAAC,CAAC;EAChB,MAAMC,KAAK,GAAG;IAAEC,KAAK,EAAE,CAAC;EAAE,CAAC;EAC3B,IAAIC,WAAW,GAAG,CAAC,OAAO,CAAC;EAE3B,KAAK,IAAIP,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,KAAK,CAACrC,MAAM,EAAEsC,CAAC,EAAE,EAAE;IACrC,MAAMQ,SAAS,GAAGT,KAAK,CAACC,CAAC,CAAC;IAC1B,MAAMS,cAAc,GAAG,EAAE;IAEzB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,SAAS,CAAC9C,MAAM,EAAEgD,CAAC,EAAE,EAAE;MACzC,MAAMC,IAAI,GAAGH,SAAS,CAACE,CAAC,CAAC;MACzB,MAAME,GAAG,GAAG,EAAE,GAAGZ,CAAC,GAAGU,CAAC;MAEtBD,cAAc,CAACxC,IAAI,CAAC2C,GAAG,CAAC;MACxBR,KAAK,CAACQ,GAAG,CAAC,GAAG;QAAED,IAAI,EAAEA,IAAI;QAAEE,SAAS,EAAE;MAAE,CAAC;MACzCR,KAAK,CAACO,GAAG,CAAC,GAAG,CAAC,CAAC;MAEf,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,WAAW,CAAC7C,MAAM,EAAEoD,CAAC,EAAE,EAAE;QAC3C,MAAMC,UAAU,GAAGR,WAAW,CAACO,CAAC,CAAC;QAEjC,IAAIV,KAAK,CAACW,UAAU,CAAC,IAAIX,KAAK,CAACW,UAAU,CAAC,CAACJ,IAAI,CAAC9C,IAAI,KAAK8C,IAAI,CAAC9C,IAAI,EAAE;UAClEwC,KAAK,CAACU,UAAU,CAAC,CAACH,GAAG,CAAC,GACpBrB,oBAAoB,CAACa,KAAK,CAACW,UAAU,CAAC,CAACF,SAAS,GAAGF,IAAI,CAACjD,MAAM,EAAEiD,IAAI,CAAC9C,IAAI,CAAC,GAC1E0B,oBAAoB,CAACa,KAAK,CAACW,UAAU,CAAC,CAACF,SAAS,EAAEF,IAAI,CAAC9C,IAAI,CAAC;UAE9DuC,KAAK,CAACW,UAAU,CAAC,CAACF,SAAS,IAAIF,IAAI,CAACjD,MAAM;QAC5C,CAAC,MAAM;UACL,IAAI0C,KAAK,CAACW,UAAU,CAAC,EAAEX,KAAK,CAACW,UAAU,CAAC,CAACF,SAAS,GAAGF,IAAI,CAACjD,MAAM;UAEhE2C,KAAK,CAACU,UAAU,CAAC,CAACH,GAAG,CAAC,GAAGrB,oBAAoB,CAACoB,IAAI,CAACjD,MAAM,EAAEiD,IAAI,CAAC9C,IAAI,CAAC,GACnE,CAAC,GAAGhB,IAAI,CAACmE,qBAAqB,CAACL,IAAI,CAAC9C,IAAI,EAAEsC,OAAO,CAAC,EAAC;QACvD;MACF;IACF;IAEAI,WAAW,GAAGE,cAAc;EAC9B;EAEA,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,WAAW,CAAC7C,MAAM,EAAEoD,CAAC,EAAE,EAAE;IAC3CT,KAAK,CAACE,WAAW,CAACO,CAAC,CAAC,CAAC,CAACG,GAAG,GAAG,CAAC;EAC/B;EAEA,OAAO;IAAE5B,GAAG,EAAEgB,KAAK;IAAED,KAAK,EAAEA;EAAM,CAAC;AACrC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASc,kBAAkBA,CAAEhD,IAAI,EAAEiD,SAAS,EAAE;EAC5C,IAAItD,IAAI;EACR,MAAMuD,QAAQ,GAAGvE,IAAI,CAACwE,kBAAkB,CAACnD,IAAI,CAAC;EAE9CL,IAAI,GAAGhB,IAAI,CAACyE,IAAI,CAACH,SAAS,EAAEC,QAAQ,CAAC;;EAErC;EACA,IAAIvD,IAAI,KAAKhB,IAAI,CAACgC,IAAI,IAAIhB,IAAI,CAAC0D,GAAG,GAAGH,QAAQ,CAACG,GAAG,EAAE;IACjD,MAAM,IAAIC,KAAK,CAAC,GAAG,GAAGtD,IAAI,GAAG,GAAG,GAC9B,+BAA+B,GAAGrB,IAAI,CAAC4E,QAAQ,CAAC5D,IAAI,CAAC,GACrD,yBAAyB,GAAGhB,IAAI,CAAC4E,QAAQ,CAACL,QAAQ,CAAC,CAAC;EACxD;;EAEA;EACA,IAAIvD,IAAI,KAAKhB,IAAI,CAACiC,KAAK,IAAI,CAAC1B,KAAK,CAACwB,kBAAkB,CAAC,CAAC,EAAE;IACtDf,IAAI,GAAGhB,IAAI,CAACgC,IAAI;EAClB;EAEA,QAAQhB,IAAI;IACV,KAAKhB,IAAI,CAAC0B,OAAO;MACf,OAAO,IAAIxB,WAAW,CAACmB,IAAI,CAAC;IAE9B,KAAKrB,IAAI,CAAC4B,YAAY;MACpB,OAAO,IAAIzB,gBAAgB,CAACkB,IAAI,CAAC;IAEnC,KAAKrB,IAAI,CAACiC,KAAK;MACb,OAAO,IAAI5B,SAAS,CAACgB,IAAI,CAAC;IAE5B,KAAKrB,IAAI,CAACgC,IAAI;MACZ,OAAO,IAAI5B,QAAQ,CAACiB,IAAI,CAAC;EAC7B;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAwD,OAAO,CAACC,SAAS,GAAG,SAASA,SAASA,CAAEC,KAAK,EAAE;EAC7C,OAAOA,KAAK,CAAClC,MAAM,CAAC,UAAUC,GAAG,EAAEM,GAAG,EAAE;IACtC,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;MAC3BN,GAAG,CAAC1B,IAAI,CAACiD,kBAAkB,CAACjB,GAAG,EAAE,IAAI,CAAC,CAAC;IACzC,CAAC,MAAM,IAAIA,GAAG,CAAC/B,IAAI,EAAE;MACnByB,GAAG,CAAC1B,IAAI,CAACiD,kBAAkB,CAACjB,GAAG,CAAC/B,IAAI,EAAE+B,GAAG,CAACpC,IAAI,CAAC,CAAC;IAClD;IAEA,OAAO8B,GAAG;EACZ,CAAC,EAAE,EAAE,CAAC;AACR,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA+B,OAAO,CAACG,UAAU,GAAG,SAASA,UAAUA,CAAE3D,IAAI,EAAEiC,OAAO,EAAE;EACvD,MAAMnB,IAAI,GAAGZ,qBAAqB,CAACF,IAAI,EAAEd,KAAK,CAACwB,kBAAkB,CAAC,CAAC,CAAC;EAEpE,MAAMmB,KAAK,GAAGD,UAAU,CAACd,IAAI,CAAC;EAC9B,MAAMqB,KAAK,GAAGH,UAAU,CAACH,KAAK,EAAEI,OAAO,CAAC;EACxC,MAAM2B,IAAI,GAAGzE,QAAQ,CAAC0E,SAAS,CAAC1B,KAAK,CAAChB,GAAG,EAAE,OAAO,EAAE,KAAK,CAAC;EAE1D,MAAM2C,aAAa,GAAG,EAAE;EACxB,KAAK,IAAIhC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8B,IAAI,CAACpE,MAAM,GAAG,CAAC,EAAEsC,CAAC,EAAE,EAAE;IACxCgC,aAAa,CAAC/D,IAAI,CAACoC,KAAK,CAACD,KAAK,CAAC0B,IAAI,CAAC9B,CAAC,CAAC,CAAC,CAACW,IAAI,CAAC;EAC/C;EAEA,OAAOe,OAAO,CAACC,SAAS,CAAClC,aAAa,CAACuC,aAAa,CAAC,CAAC;AACxD,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAN,OAAO,CAACO,QAAQ,GAAG,SAASA,QAAQA,CAAE/D,IAAI,EAAE;EAC1C,OAAOwD,OAAO,CAACC,SAAS,CACtBvD,qBAAqB,CAACF,IAAI,EAAEd,KAAK,CAACwB,kBAAkB,CAAC,CAAC,CACxD,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}