{"ast": null, "code": "\"use client\";\n\nimport _objectSpread from \"C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/test 3/medical-assistant-react/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport _objectWithoutProperties from \"C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/test 3/medical-assistant-react/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\";\nconst _excluded = [\"className\", \"bsPrefix\", \"as\"];\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst InputGroupText = /*#__PURE__*/React.forwardRef((_ref, ref) => {\n  let {\n      className,\n      bsPrefix,\n      as: Component = 'span'\n    } = _ref,\n    props = _objectWithoutProperties(_ref, _excluded);\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'input-group-text');\n  return /*#__PURE__*/_jsx(Component, _objectSpread({\n    ref: ref,\n    className: classNames(className, bsPrefix)\n  }, props));\n});\nInputGroupText.displayName = 'InputGroupText';\nexport default InputGroupText;", "map": {"version": 3, "names": ["_objectSpread", "_objectWithoutProperties", "_excluded", "React", "classNames", "useBootstrapPrefix", "jsx", "_jsx", "InputGroupText", "forwardRef", "_ref", "ref", "className", "bsPrefix", "as", "Component", "props", "displayName"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/test 3/medical-assistant-react/node_modules/react-bootstrap/esm/InputGroupText.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst InputGroupText = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'span',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'input-group-text');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nInputGroupText.displayName = 'InputGroupText';\nexport default InputGroupText;"], "mappings": "AAAA,YAAY;;AAAC,OAAAA,aAAA;AAAA,OAAAC,wBAAA;AAAA,MAAAC,SAAA;AAEb,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,cAAc,GAAG,aAAaL,KAAK,CAACM,UAAU,CAAC,CAAAC,IAAA,EAKlDC,GAAG,KAAK;EAAA,IAL2C;MACpDC,SAAS;MACTC,QAAQ;MACRC,EAAE,EAAEC,SAAS,GAAG;IAElB,CAAC,GAAAL,IAAA;IADIM,KAAK,GAAAf,wBAAA,CAAAS,IAAA,EAAAR,SAAA;EAERW,QAAQ,GAAGR,kBAAkB,CAACQ,QAAQ,EAAE,kBAAkB,CAAC;EAC3D,OAAO,aAAaN,IAAI,CAACQ,SAAS,EAAAf,aAAA;IAChCW,GAAG,EAAEA,GAAG;IACRC,SAAS,EAAER,UAAU,CAACQ,SAAS,EAAEC,QAAQ;EAAC,GACvCG,KAAK,CACT,CAAC;AACJ,CAAC,CAAC;AACFR,cAAc,CAACS,WAAW,GAAG,gBAAgB;AAC7C,eAAeT,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}