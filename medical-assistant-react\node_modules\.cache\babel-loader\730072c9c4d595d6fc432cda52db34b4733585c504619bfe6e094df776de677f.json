{"ast": null, "code": "const getSymbolSize = require('./utils').getSymbolSize;\nconst FINDER_PATTERN_SIZE = 7;\n\n/**\n * Returns an array containing the positions of each finder pattern.\n * Each array's element represent the top-left point of the pattern as (x, y) coordinates\n *\n * @param  {Number} version QR Code version\n * @return {Array}          Array of coordinates\n */\nexports.getPositions = function getPositions(version) {\n  const size = getSymbolSize(version);\n  return [\n  // top-left\n  [0, 0],\n  // top-right\n  [size - FINDER_PATTERN_SIZE, 0],\n  // bottom-left\n  [0, size - FINDER_PATTERN_SIZE]];\n};", "map": {"version": 3, "names": ["getSymbolSize", "require", "FINDER_PATTERN_SIZE", "exports", "getPositions", "version", "size"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/test 3/medical-assistant-react/node_modules/qrcode/lib/core/finder-pattern.js"], "sourcesContent": ["const getSymbolSize = require('./utils').getSymbolSize\nconst FINDER_PATTERN_SIZE = 7\n\n/**\n * Returns an array containing the positions of each finder pattern.\n * Each array's element represent the top-left point of the pattern as (x, y) coordinates\n *\n * @param  {Number} version QR Code version\n * @return {Array}          Array of coordinates\n */\nexports.getPositions = function getPositions (version) {\n  const size = getSymbolSize(version)\n\n  return [\n    // top-left\n    [0, 0],\n    // top-right\n    [size - FINDER_PATTERN_SIZE, 0],\n    // bottom-left\n    [0, size - FINDER_PATTERN_SIZE]\n  ]\n}\n"], "mappings": "AAAA,MAAMA,aAAa,GAAGC,OAAO,CAAC,SAAS,CAAC,CAACD,aAAa;AACtD,MAAME,mBAAmB,GAAG,CAAC;;AAE7B;AACA;AACA;AACA;AACA;AACA;AACA;AACAC,OAAO,CAACC,YAAY,GAAG,SAASA,YAAYA,CAAEC,OAAO,EAAE;EACrD,MAAMC,IAAI,GAAGN,aAAa,CAACK,OAAO,CAAC;EAEnC,OAAO;EACL;EACA,CAAC,CAAC,EAAE,CAAC,CAAC;EACN;EACA,CAACC,IAAI,GAAGJ,mBAAmB,EAAE,CAAC,CAAC;EAC/B;EACA,CAAC,CAAC,EAAEI,IAAI,GAAGJ,mBAAmB,CAAC,CAChC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}