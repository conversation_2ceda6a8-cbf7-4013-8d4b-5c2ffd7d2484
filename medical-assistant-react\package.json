{"name": "medical-assistant-react", "version": "1.0.0", "private": true, "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.4.2", "@fortawesome/free-solid-svg-icons": "^6.4.2", "@fortawesome/react-fontawesome": "^0.2.0", "@react-spring/web": "^10.0.1", "@testing-library/jest-dom": "^5.16.4", "@testing-library/react": "^13.3.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.11.56", "@types/qrcode": "^1.5.2", "@types/react": "^18.0.17", "@types/react-dom": "^18.0.6", "aos": "^2.3.4", "axios": "^1.5.0", "bootstrap": "^5.3.2", "date-fns": "^2.30.0", "framer-motion": "^10.16.4", "lottie-react": "^2.4.1", "qrcode": "^1.5.3", "react": "^18.2.0", "react-bootstrap": "^2.8.0", "react-dom": "^18.2.0", "react-hook-form": "^7.45.4", "react-image-gallery": "^1.4.0", "react-intersection-observer": "^9.16.0", "react-lazyload": "^3.2.1", "react-loading-skeleton": "^3.5.0", "react-query": "^3.39.3", "react-router-dom": "^6.4.0", "react-scripts": "5.0.1", "swiper": "^11.2.8", "typescript": "^4.7.4", "web-vitals": "^2.1.4", "zustand": "^4.4.1"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}