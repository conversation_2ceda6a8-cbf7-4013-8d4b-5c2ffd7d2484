{"ast": null, "code": "const Utils = require('./utils');\nconst ECCode = require('./error-correction-code');\nconst ECLevel = require('./error-correction-level');\nconst Mode = require('./mode');\nconst VersionCheck = require('./version-check');\n\n// Generator polynomial used to encode version information\nconst G18 = 1 << 12 | 1 << 11 | 1 << 10 | 1 << 9 | 1 << 8 | 1 << 5 | 1 << 2 | 1 << 0;\nconst G18_BCH = Utils.getBCHDigit(G18);\nfunction getBestVersionForDataLength(mode, length, errorCorrectionLevel) {\n  for (let currentVersion = 1; currentVersion <= 40; currentVersion++) {\n    if (length <= exports.getCapacity(currentVersion, errorCorrectionLevel, mode)) {\n      return currentVersion;\n    }\n  }\n  return undefined;\n}\nfunction getReservedBitsCount(mode, version) {\n  // Character count indicator + mode indicator bits\n  return Mode.getCharCountIndicator(mode, version) + 4;\n}\nfunction getTotalBitsFromDataArray(segments, version) {\n  let totalBits = 0;\n  segments.forEach(function (data) {\n    const reservedBits = getReservedBitsCount(data.mode, version);\n    totalBits += reservedBits + data.getBitsLength();\n  });\n  return totalBits;\n}\nfunction getBestVersionForMixedData(segments, errorCorrectionLevel) {\n  for (let currentVersion = 1; currentVersion <= 40; currentVersion++) {\n    const length = getTotalBitsFromDataArray(segments, currentVersion);\n    if (length <= exports.getCapacity(currentVersion, errorCorrectionLevel, Mode.MIXED)) {\n      return currentVersion;\n    }\n  }\n  return undefined;\n}\n\n/**\n * Returns version number from a value.\n * If value is not a valid version, returns defaultValue\n *\n * @param  {Number|String} value        QR Code version\n * @param  {Number}        defaultValue Fallback value\n * @return {Number}                     QR Code version number\n */\nexports.from = function from(value, defaultValue) {\n  if (VersionCheck.isValid(value)) {\n    return parseInt(value, 10);\n  }\n  return defaultValue;\n};\n\n/**\n * Returns how much data can be stored with the specified QR code version\n * and error correction level\n *\n * @param  {Number} version              QR Code version (1-40)\n * @param  {Number} errorCorrectionLevel Error correction level\n * @param  {Mode}   mode                 Data mode\n * @return {Number}                      Quantity of storable data\n */\nexports.getCapacity = function getCapacity(version, errorCorrectionLevel, mode) {\n  if (!VersionCheck.isValid(version)) {\n    throw new Error('Invalid QR Code version');\n  }\n\n  // Use Byte mode as default\n  if (typeof mode === 'undefined') mode = Mode.BYTE;\n\n  // Total codewords for this QR code version (Data + Error correction)\n  const totalCodewords = Utils.getSymbolTotalCodewords(version);\n\n  // Total number of error correction codewords\n  const ecTotalCodewords = ECCode.getTotalCodewordsCount(version, errorCorrectionLevel);\n\n  // Total number of data codewords\n  const dataTotalCodewordsBits = (totalCodewords - ecTotalCodewords) * 8;\n  if (mode === Mode.MIXED) return dataTotalCodewordsBits;\n  const usableBits = dataTotalCodewordsBits - getReservedBitsCount(mode, version);\n\n  // Return max number of storable codewords\n  switch (mode) {\n    case Mode.NUMERIC:\n      return Math.floor(usableBits / 10 * 3);\n    case Mode.ALPHANUMERIC:\n      return Math.floor(usableBits / 11 * 2);\n    case Mode.KANJI:\n      return Math.floor(usableBits / 13);\n    case Mode.BYTE:\n    default:\n      return Math.floor(usableBits / 8);\n  }\n};\n\n/**\n * Returns the minimum version needed to contain the amount of data\n *\n * @param  {Segment} data                    Segment of data\n * @param  {Number} [errorCorrectionLevel=H] Error correction level\n * @param  {Mode} mode                       Data mode\n * @return {Number}                          QR Code version\n */\nexports.getBestVersionForData = function getBestVersionForData(data, errorCorrectionLevel) {\n  let seg;\n  const ecl = ECLevel.from(errorCorrectionLevel, ECLevel.M);\n  if (Array.isArray(data)) {\n    if (data.length > 1) {\n      return getBestVersionForMixedData(data, ecl);\n    }\n    if (data.length === 0) {\n      return 1;\n    }\n    seg = data[0];\n  } else {\n    seg = data;\n  }\n  return getBestVersionForDataLength(seg.mode, seg.getLength(), ecl);\n};\n\n/**\n * Returns version information with relative error correction bits\n *\n * The version information is included in QR Code symbols of version 7 or larger.\n * It consists of an 18-bit sequence containing 6 data bits,\n * with 12 error correction bits calculated using the (18, 6) Golay code.\n *\n * @param  {Number} version QR Code version\n * @return {Number}         Encoded version info bits\n */\nexports.getEncodedBits = function getEncodedBits(version) {\n  if (!VersionCheck.isValid(version) || version < 7) {\n    throw new Error('Invalid QR Code version');\n  }\n  let d = version << 12;\n  while (Utils.getBCHDigit(d) - G18_BCH >= 0) {\n    d ^= G18 << Utils.getBCHDigit(d) - G18_BCH;\n  }\n  return version << 12 | d;\n};", "map": {"version": 3, "names": ["Utils", "require", "ECCode", "ECLevel", "Mode", "VersionCheck", "G18", "G18_BCH", "getBCHDigit", "getBestVersionForDataLength", "mode", "length", "errorCorrectionLevel", "currentVersion", "exports", "getCapacity", "undefined", "getReservedBitsCount", "version", "getCharCountIndicator", "getTotalBitsFromDataArray", "segments", "totalBits", "for<PERSON>ach", "data", "reservedBits", "getBitsLength", "getBestVersionForMixedData", "MIXED", "from", "value", "defaultValue", "<PERSON><PERSON><PERSON><PERSON>", "parseInt", "Error", "BYTE", "totalCodewords", "getSymbolTotalCodewords", "ecTotalCodewords", "getTotalCodewordsCount", "dataTotalCodewordsBits", "usableBits", "NUMERIC", "Math", "floor", "ALPHANUMERIC", "KANJI", "getBestVersionForData", "seg", "ecl", "M", "Array", "isArray", "<PERSON><PERSON><PERSON><PERSON>", "getEncodedBits", "d"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/test 3/medical-assistant-react/node_modules/qrcode/lib/core/version.js"], "sourcesContent": ["const Utils = require('./utils')\nconst ECCode = require('./error-correction-code')\nconst ECLevel = require('./error-correction-level')\nconst Mode = require('./mode')\nconst VersionCheck = require('./version-check')\n\n// Generator polynomial used to encode version information\nconst G18 = (1 << 12) | (1 << 11) | (1 << 10) | (1 << 9) | (1 << 8) | (1 << 5) | (1 << 2) | (1 << 0)\nconst G18_BCH = Utils.getBCHDigit(G18)\n\nfunction getBestVersionForDataLength (mode, length, errorCorrectionLevel) {\n  for (let currentVersion = 1; currentVersion <= 40; currentVersion++) {\n    if (length <= exports.getCapacity(currentVersion, errorCorrectionLevel, mode)) {\n      return currentVersion\n    }\n  }\n\n  return undefined\n}\n\nfunction getReservedBitsCount (mode, version) {\n  // Character count indicator + mode indicator bits\n  return Mode.getCharCountIndicator(mode, version) + 4\n}\n\nfunction getTotalBitsFromDataArray (segments, version) {\n  let totalBits = 0\n\n  segments.forEach(function (data) {\n    const reservedBits = getReservedBitsCount(data.mode, version)\n    totalBits += reservedBits + data.getBitsLength()\n  })\n\n  return totalBits\n}\n\nfunction getBestVersionForMixedData (segments, errorCorrectionLevel) {\n  for (let currentVersion = 1; currentVersion <= 40; currentVersion++) {\n    const length = getTotalBitsFromDataArray(segments, currentVersion)\n    if (length <= exports.getCapacity(currentVersion, errorCorrectionLevel, Mode.MIXED)) {\n      return currentVersion\n    }\n  }\n\n  return undefined\n}\n\n/**\n * Returns version number from a value.\n * If value is not a valid version, returns defaultValue\n *\n * @param  {Number|String} value        QR Code version\n * @param  {Number}        defaultValue Fallback value\n * @return {Number}                     QR Code version number\n */\nexports.from = function from (value, defaultValue) {\n  if (VersionCheck.isValid(value)) {\n    return parseInt(value, 10)\n  }\n\n  return defaultValue\n}\n\n/**\n * Returns how much data can be stored with the specified QR code version\n * and error correction level\n *\n * @param  {Number} version              QR Code version (1-40)\n * @param  {Number} errorCorrectionLevel Error correction level\n * @param  {Mode}   mode                 Data mode\n * @return {Number}                      Quantity of storable data\n */\nexports.getCapacity = function getCapacity (version, errorCorrectionLevel, mode) {\n  if (!VersionCheck.isValid(version)) {\n    throw new Error('Invalid QR Code version')\n  }\n\n  // Use Byte mode as default\n  if (typeof mode === 'undefined') mode = Mode.BYTE\n\n  // Total codewords for this QR code version (Data + Error correction)\n  const totalCodewords = Utils.getSymbolTotalCodewords(version)\n\n  // Total number of error correction codewords\n  const ecTotalCodewords = ECCode.getTotalCodewordsCount(version, errorCorrectionLevel)\n\n  // Total number of data codewords\n  const dataTotalCodewordsBits = (totalCodewords - ecTotalCodewords) * 8\n\n  if (mode === Mode.MIXED) return dataTotalCodewordsBits\n\n  const usableBits = dataTotalCodewordsBits - getReservedBitsCount(mode, version)\n\n  // Return max number of storable codewords\n  switch (mode) {\n    case Mode.NUMERIC:\n      return Math.floor((usableBits / 10) * 3)\n\n    case Mode.ALPHANUMERIC:\n      return Math.floor((usableBits / 11) * 2)\n\n    case Mode.KANJI:\n      return Math.floor(usableBits / 13)\n\n    case Mode.BYTE:\n    default:\n      return Math.floor(usableBits / 8)\n  }\n}\n\n/**\n * Returns the minimum version needed to contain the amount of data\n *\n * @param  {Segment} data                    Segment of data\n * @param  {Number} [errorCorrectionLevel=H] Error correction level\n * @param  {Mode} mode                       Data mode\n * @return {Number}                          QR Code version\n */\nexports.getBestVersionForData = function getBestVersionForData (data, errorCorrectionLevel) {\n  let seg\n\n  const ecl = ECLevel.from(errorCorrectionLevel, ECLevel.M)\n\n  if (Array.isArray(data)) {\n    if (data.length > 1) {\n      return getBestVersionForMixedData(data, ecl)\n    }\n\n    if (data.length === 0) {\n      return 1\n    }\n\n    seg = data[0]\n  } else {\n    seg = data\n  }\n\n  return getBestVersionForDataLength(seg.mode, seg.getLength(), ecl)\n}\n\n/**\n * Returns version information with relative error correction bits\n *\n * The version information is included in QR Code symbols of version 7 or larger.\n * It consists of an 18-bit sequence containing 6 data bits,\n * with 12 error correction bits calculated using the (18, 6) Golay code.\n *\n * @param  {Number} version QR Code version\n * @return {Number}         Encoded version info bits\n */\nexports.getEncodedBits = function getEncodedBits (version) {\n  if (!VersionCheck.isValid(version) || version < 7) {\n    throw new Error('Invalid QR Code version')\n  }\n\n  let d = version << 12\n\n  while (Utils.getBCHDigit(d) - G18_BCH >= 0) {\n    d ^= (G18 << (Utils.getBCHDigit(d) - G18_BCH))\n  }\n\n  return (version << 12) | d\n}\n"], "mappings": "AAAA,MAAMA,KAAK,GAAGC,OAAO,CAAC,SAAS,CAAC;AAChC,MAAMC,MAAM,GAAGD,OAAO,CAAC,yBAAyB,CAAC;AACjD,MAAME,OAAO,GAAGF,OAAO,CAAC,0BAA0B,CAAC;AACnD,MAAMG,IAAI,GAAGH,OAAO,CAAC,QAAQ,CAAC;AAC9B,MAAMI,YAAY,GAAGJ,OAAO,CAAC,iBAAiB,CAAC;;AAE/C;AACA,MAAMK,GAAG,GAAI,CAAC,IAAI,EAAE,GAAK,CAAC,IAAI,EAAG,GAAI,CAAC,IAAI,EAAG,GAAI,CAAC,IAAI,CAAE,GAAI,CAAC,IAAI,CAAE,GAAI,CAAC,IAAI,CAAE,GAAI,CAAC,IAAI,CAAE,GAAI,CAAC,IAAI,CAAE;AACpG,MAAMC,OAAO,GAAGP,KAAK,CAACQ,WAAW,CAACF,GAAG,CAAC;AAEtC,SAASG,2BAA2BA,CAAEC,IAAI,EAAEC,MAAM,EAAEC,oBAAoB,EAAE;EACxE,KAAK,IAAIC,cAAc,GAAG,CAAC,EAAEA,cAAc,IAAI,EAAE,EAAEA,cAAc,EAAE,EAAE;IACnE,IAAIF,MAAM,IAAIG,OAAO,CAACC,WAAW,CAACF,cAAc,EAAED,oBAAoB,EAAEF,IAAI,CAAC,EAAE;MAC7E,OAAOG,cAAc;IACvB;EACF;EAEA,OAAOG,SAAS;AAClB;AAEA,SAASC,oBAAoBA,CAAEP,IAAI,EAAEQ,OAAO,EAAE;EAC5C;EACA,OAAOd,IAAI,CAACe,qBAAqB,CAACT,IAAI,EAAEQ,OAAO,CAAC,GAAG,CAAC;AACtD;AAEA,SAASE,yBAAyBA,CAAEC,QAAQ,EAAEH,OAAO,EAAE;EACrD,IAAII,SAAS,GAAG,CAAC;EAEjBD,QAAQ,CAACE,OAAO,CAAC,UAAUC,IAAI,EAAE;IAC/B,MAAMC,YAAY,GAAGR,oBAAoB,CAACO,IAAI,CAACd,IAAI,EAAEQ,OAAO,CAAC;IAC7DI,SAAS,IAAIG,YAAY,GAAGD,IAAI,CAACE,aAAa,CAAC,CAAC;EAClD,CAAC,CAAC;EAEF,OAAOJ,SAAS;AAClB;AAEA,SAASK,0BAA0BA,CAAEN,QAAQ,EAAET,oBAAoB,EAAE;EACnE,KAAK,IAAIC,cAAc,GAAG,CAAC,EAAEA,cAAc,IAAI,EAAE,EAAEA,cAAc,EAAE,EAAE;IACnE,MAAMF,MAAM,GAAGS,yBAAyB,CAACC,QAAQ,EAAER,cAAc,CAAC;IAClE,IAAIF,MAAM,IAAIG,OAAO,CAACC,WAAW,CAACF,cAAc,EAAED,oBAAoB,EAAER,IAAI,CAACwB,KAAK,CAAC,EAAE;MACnF,OAAOf,cAAc;IACvB;EACF;EAEA,OAAOG,SAAS;AAClB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAF,OAAO,CAACe,IAAI,GAAG,SAASA,IAAIA,CAAEC,KAAK,EAAEC,YAAY,EAAE;EACjD,IAAI1B,YAAY,CAAC2B,OAAO,CAACF,KAAK,CAAC,EAAE;IAC/B,OAAOG,QAAQ,CAACH,KAAK,EAAE,EAAE,CAAC;EAC5B;EAEA,OAAOC,YAAY;AACrB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAjB,OAAO,CAACC,WAAW,GAAG,SAASA,WAAWA,CAAEG,OAAO,EAAEN,oBAAoB,EAAEF,IAAI,EAAE;EAC/E,IAAI,CAACL,YAAY,CAAC2B,OAAO,CAACd,OAAO,CAAC,EAAE;IAClC,MAAM,IAAIgB,KAAK,CAAC,yBAAyB,CAAC;EAC5C;;EAEA;EACA,IAAI,OAAOxB,IAAI,KAAK,WAAW,EAAEA,IAAI,GAAGN,IAAI,CAAC+B,IAAI;;EAEjD;EACA,MAAMC,cAAc,GAAGpC,KAAK,CAACqC,uBAAuB,CAACnB,OAAO,CAAC;;EAE7D;EACA,MAAMoB,gBAAgB,GAAGpC,MAAM,CAACqC,sBAAsB,CAACrB,OAAO,EAAEN,oBAAoB,CAAC;;EAErF;EACA,MAAM4B,sBAAsB,GAAG,CAACJ,cAAc,GAAGE,gBAAgB,IAAI,CAAC;EAEtE,IAAI5B,IAAI,KAAKN,IAAI,CAACwB,KAAK,EAAE,OAAOY,sBAAsB;EAEtD,MAAMC,UAAU,GAAGD,sBAAsB,GAAGvB,oBAAoB,CAACP,IAAI,EAAEQ,OAAO,CAAC;;EAE/E;EACA,QAAQR,IAAI;IACV,KAAKN,IAAI,CAACsC,OAAO;MACf,OAAOC,IAAI,CAACC,KAAK,CAAEH,UAAU,GAAG,EAAE,GAAI,CAAC,CAAC;IAE1C,KAAKrC,IAAI,CAACyC,YAAY;MACpB,OAAOF,IAAI,CAACC,KAAK,CAAEH,UAAU,GAAG,EAAE,GAAI,CAAC,CAAC;IAE1C,KAAKrC,IAAI,CAAC0C,KAAK;MACb,OAAOH,IAAI,CAACC,KAAK,CAACH,UAAU,GAAG,EAAE,CAAC;IAEpC,KAAKrC,IAAI,CAAC+B,IAAI;IACd;MACE,OAAOQ,IAAI,CAACC,KAAK,CAACH,UAAU,GAAG,CAAC,CAAC;EACrC;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA3B,OAAO,CAACiC,qBAAqB,GAAG,SAASA,qBAAqBA,CAAEvB,IAAI,EAAEZ,oBAAoB,EAAE;EAC1F,IAAIoC,GAAG;EAEP,MAAMC,GAAG,GAAG9C,OAAO,CAAC0B,IAAI,CAACjB,oBAAoB,EAAET,OAAO,CAAC+C,CAAC,CAAC;EAEzD,IAAIC,KAAK,CAACC,OAAO,CAAC5B,IAAI,CAAC,EAAE;IACvB,IAAIA,IAAI,CAACb,MAAM,GAAG,CAAC,EAAE;MACnB,OAAOgB,0BAA0B,CAACH,IAAI,EAAEyB,GAAG,CAAC;IAC9C;IAEA,IAAIzB,IAAI,CAACb,MAAM,KAAK,CAAC,EAAE;MACrB,OAAO,CAAC;IACV;IAEAqC,GAAG,GAAGxB,IAAI,CAAC,CAAC,CAAC;EACf,CAAC,MAAM;IACLwB,GAAG,GAAGxB,IAAI;EACZ;EAEA,OAAOf,2BAA2B,CAACuC,GAAG,CAACtC,IAAI,EAAEsC,GAAG,CAACK,SAAS,CAAC,CAAC,EAAEJ,GAAG,CAAC;AACpE,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAnC,OAAO,CAACwC,cAAc,GAAG,SAASA,cAAcA,CAAEpC,OAAO,EAAE;EACzD,IAAI,CAACb,YAAY,CAAC2B,OAAO,CAACd,OAAO,CAAC,IAAIA,OAAO,GAAG,CAAC,EAAE;IACjD,MAAM,IAAIgB,KAAK,CAAC,yBAAyB,CAAC;EAC5C;EAEA,IAAIqB,CAAC,GAAGrC,OAAO,IAAI,EAAE;EAErB,OAAOlB,KAAK,CAACQ,WAAW,CAAC+C,CAAC,CAAC,GAAGhD,OAAO,IAAI,CAAC,EAAE;IAC1CgD,CAAC,IAAKjD,GAAG,IAAKN,KAAK,CAACQ,WAAW,CAAC+C,CAAC,CAAC,GAAGhD,OAAS;EAChD;EAEA,OAAQW,OAAO,IAAI,EAAE,GAAIqC,CAAC;AAC5B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}