import React from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faHeartbeat, faStethoscope, faPills } from '@fortawesome/free-solid-svg-icons';

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg';
  text?: string;
  variant?: 'primary' | 'secondary' | 'medical';
}

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({ 
  size = 'md', 
  text = 'جاري التحميل...', 
  variant = 'medical' 
}) => {
  const sizeClasses = {
    sm: 'spinner-sm',
    md: 'spinner-md',
    lg: 'spinner-lg'
  };

  const iconSizes = {
    sm: '1.5rem',
    md: '2rem',
    lg: '3rem'
  };

  if (variant === 'medical') {
    return (
      <div className="text-center">
        <div className="medical-loading-container mb-3">
          <div className="medical-pulse-1">
            <FontAwesomeIcon 
              icon={faHeartbeat} 
              style={{ fontSize: iconSizes[size], color: 'var(--medical-primary-500)' }}
            />
          </div>
          <div className="medical-pulse-2">
            <FontAwesomeIcon 
              icon={faStethoscope} 
              style={{ fontSize: iconSizes[size], color: 'var(--medical-secondary-500)' }}
            />
          </div>
          <div className="medical-pulse-3">
            <FontAwesomeIcon 
              icon={faPills} 
              style={{ fontSize: iconSizes[size], color: 'var(--medical-accent-500)' }}
            />
          </div>
        </div>
        <div className="text-muted">{text}</div>
        
        <style>{`
          .medical-loading-container {
            position: relative;
            display: inline-block;
            width: ${iconSizes[size]};
            height: ${iconSizes[size]};
          }
          
          .medical-pulse-1, .medical-pulse-2, .medical-pulse-3 {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            animation: medicalPulse 2s infinite ease-in-out;
          }
          
          .medical-pulse-2 {
            animation-delay: 0.5s;
          }
          
          .medical-pulse-3 {
            animation-delay: 1s;
          }
          
          @keyframes medicalPulse {
            0%, 100% {
              opacity: 0;
              transform: translate(-50%, -50%) scale(0.8);
            }
            50% {
              opacity: 1;
              transform: translate(-50%, -50%) scale(1.2);
            }
          }
        `}</style>
      </div>
    );
  }

  return (
    <div className="text-center">
      <div className={`spinner-border text-${variant} ${sizeClasses[size]} mb-3`} role="status">
        <span className="visually-hidden">{text}</span>
      </div>
      <div className="text-muted">{text}</div>
    </div>
  );
};

export default LoadingSpinner;
