import React from 'react';
import { Container, <PERSON>, <PERSON>, But<PERSON> } from 'react-bootstrap';
import { Link } from 'react-router-dom';

const CTASection: React.FC = () => {
  return (
    <section className="bg-primary py-5 py-md-6">
      <Container>
        <Row className="justify-content-center text-center">
          <Col lg={8}>
            <h2 className="fw-bold text-white mb-4">
              هل أنت مستعد لتجربة مساعدنا الطبي الذكي؟
            </h2>
            <p className="lead text-white-50 mb-5">
              ابدأ تشخيصك الأول - إنه مجاني ويستغرق أقل من 5 دقائق
            </p>
            <Link to="/diagnosis" className="text-decoration-none">
              <Button
                variant="light"
                size="lg"
                className="px-5 py-3 fw-semibold"
                style={{ borderRadius: '50px' }}
              >
                ابدأ تشخيصك
              </Button>
            </Link>
          </Col>
        </Row>
      </Container>
    </section>
  );
};

export default CTASection;
