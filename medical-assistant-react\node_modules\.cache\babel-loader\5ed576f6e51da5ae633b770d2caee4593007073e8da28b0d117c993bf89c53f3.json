{"ast": null, "code": "const Utils = require('./utils');\nfunction clearCanvas(ctx, canvas, size) {\n  ctx.clearRect(0, 0, canvas.width, canvas.height);\n  if (!canvas.style) canvas.style = {};\n  canvas.height = size;\n  canvas.width = size;\n  canvas.style.height = size + 'px';\n  canvas.style.width = size + 'px';\n}\nfunction getCanvasElement() {\n  try {\n    return document.createElement('canvas');\n  } catch (e) {\n    throw new Error('You need to specify a canvas element');\n  }\n}\nexports.render = function render(qrData, canvas, options) {\n  let opts = options;\n  let canvasEl = canvas;\n  if (typeof opts === 'undefined' && (!canvas || !canvas.getContext)) {\n    opts = canvas;\n    canvas = undefined;\n  }\n  if (!canvas) {\n    canvasEl = getCanvasElement();\n  }\n  opts = Utils.getOptions(opts);\n  const size = Utils.getImageWidth(qrData.modules.size, opts);\n  const ctx = canvasEl.getContext('2d');\n  const image = ctx.createImageData(size, size);\n  Utils.qrToImageData(image.data, qrData, opts);\n  clearCanvas(ctx, canvasEl, size);\n  ctx.putImageData(image, 0, 0);\n  return canvasEl;\n};\nexports.renderToDataURL = function renderToDataURL(qrData, canvas, options) {\n  let opts = options;\n  if (typeof opts === 'undefined' && (!canvas || !canvas.getContext)) {\n    opts = canvas;\n    canvas = undefined;\n  }\n  if (!opts) opts = {};\n  const canvasEl = exports.render(qrData, canvas, opts);\n  const type = opts.type || 'image/png';\n  const rendererOpts = opts.rendererOpts || {};\n  return canvasEl.toDataURL(type, rendererOpts.quality);\n};", "map": {"version": 3, "names": ["Utils", "require", "clearCanvas", "ctx", "canvas", "size", "clearRect", "width", "height", "style", "getCanvasElement", "document", "createElement", "e", "Error", "exports", "render", "qrData", "options", "opts", "canvasEl", "getContext", "undefined", "getOptions", "getImageWidth", "modules", "image", "createImageData", "qrToImageData", "data", "putImageData", "renderToDataURL", "type", "rendererOpts", "toDataURL", "quality"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/test 3/medical-assistant-react/node_modules/qrcode/lib/renderer/canvas.js"], "sourcesContent": ["const Utils = require('./utils')\n\nfunction clearCanvas (ctx, canvas, size) {\n  ctx.clearRect(0, 0, canvas.width, canvas.height)\n\n  if (!canvas.style) canvas.style = {}\n  canvas.height = size\n  canvas.width = size\n  canvas.style.height = size + 'px'\n  canvas.style.width = size + 'px'\n}\n\nfunction getCanvasElement () {\n  try {\n    return document.createElement('canvas')\n  } catch (e) {\n    throw new Error('You need to specify a canvas element')\n  }\n}\n\nexports.render = function render (qrData, canvas, options) {\n  let opts = options\n  let canvasEl = canvas\n\n  if (typeof opts === 'undefined' && (!canvas || !canvas.getContext)) {\n    opts = canvas\n    canvas = undefined\n  }\n\n  if (!canvas) {\n    canvasEl = getCanvasElement()\n  }\n\n  opts = Utils.getOptions(opts)\n  const size = Utils.getImageWidth(qrData.modules.size, opts)\n\n  const ctx = canvasEl.getContext('2d')\n  const image = ctx.createImageData(size, size)\n  Utils.qrToImageData(image.data, qrData, opts)\n\n  clearCanvas(ctx, canvasEl, size)\n  ctx.putImageData(image, 0, 0)\n\n  return canvasEl\n}\n\nexports.renderToDataURL = function renderToDataURL (qrData, canvas, options) {\n  let opts = options\n\n  if (typeof opts === 'undefined' && (!canvas || !canvas.getContext)) {\n    opts = canvas\n    canvas = undefined\n  }\n\n  if (!opts) opts = {}\n\n  const canvasEl = exports.render(qrData, canvas, opts)\n\n  const type = opts.type || 'image/png'\n  const rendererOpts = opts.rendererOpts || {}\n\n  return canvasEl.toDataURL(type, rendererOpts.quality)\n}\n"], "mappings": "AAAA,MAAMA,KAAK,GAAGC,OAAO,CAAC,SAAS,CAAC;AAEhC,SAASC,WAAWA,CAAEC,GAAG,EAAEC,MAAM,EAAEC,IAAI,EAAE;EACvCF,GAAG,CAACG,SAAS,CAAC,CAAC,EAAE,CAAC,EAAEF,MAAM,CAACG,KAAK,EAAEH,MAAM,CAACI,MAAM,CAAC;EAEhD,IAAI,CAACJ,MAAM,CAACK,KAAK,EAAEL,MAAM,CAACK,KAAK,GAAG,CAAC,CAAC;EACpCL,MAAM,CAACI,MAAM,GAAGH,IAAI;EACpBD,MAAM,CAACG,KAAK,GAAGF,IAAI;EACnBD,MAAM,CAACK,KAAK,CAACD,MAAM,GAAGH,IAAI,GAAG,IAAI;EACjCD,MAAM,CAACK,KAAK,CAACF,KAAK,GAAGF,IAAI,GAAG,IAAI;AAClC;AAEA,SAASK,gBAAgBA,CAAA,EAAI;EAC3B,IAAI;IACF,OAAOC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;EACzC,CAAC,CAAC,OAAOC,CAAC,EAAE;IACV,MAAM,IAAIC,KAAK,CAAC,sCAAsC,CAAC;EACzD;AACF;AAEAC,OAAO,CAACC,MAAM,GAAG,SAASA,MAAMA,CAAEC,MAAM,EAAEb,MAAM,EAAEc,OAAO,EAAE;EACzD,IAAIC,IAAI,GAAGD,OAAO;EAClB,IAAIE,QAAQ,GAAGhB,MAAM;EAErB,IAAI,OAAOe,IAAI,KAAK,WAAW,KAAK,CAACf,MAAM,IAAI,CAACA,MAAM,CAACiB,UAAU,CAAC,EAAE;IAClEF,IAAI,GAAGf,MAAM;IACbA,MAAM,GAAGkB,SAAS;EACpB;EAEA,IAAI,CAAClB,MAAM,EAAE;IACXgB,QAAQ,GAAGV,gBAAgB,CAAC,CAAC;EAC/B;EAEAS,IAAI,GAAGnB,KAAK,CAACuB,UAAU,CAACJ,IAAI,CAAC;EAC7B,MAAMd,IAAI,GAAGL,KAAK,CAACwB,aAAa,CAACP,MAAM,CAACQ,OAAO,CAACpB,IAAI,EAAEc,IAAI,CAAC;EAE3D,MAAMhB,GAAG,GAAGiB,QAAQ,CAACC,UAAU,CAAC,IAAI,CAAC;EACrC,MAAMK,KAAK,GAAGvB,GAAG,CAACwB,eAAe,CAACtB,IAAI,EAAEA,IAAI,CAAC;EAC7CL,KAAK,CAAC4B,aAAa,CAACF,KAAK,CAACG,IAAI,EAAEZ,MAAM,EAAEE,IAAI,CAAC;EAE7CjB,WAAW,CAACC,GAAG,EAAEiB,QAAQ,EAAEf,IAAI,CAAC;EAChCF,GAAG,CAAC2B,YAAY,CAACJ,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC;EAE7B,OAAON,QAAQ;AACjB,CAAC;AAEDL,OAAO,CAACgB,eAAe,GAAG,SAASA,eAAeA,CAAEd,MAAM,EAAEb,MAAM,EAAEc,OAAO,EAAE;EAC3E,IAAIC,IAAI,GAAGD,OAAO;EAElB,IAAI,OAAOC,IAAI,KAAK,WAAW,KAAK,CAACf,MAAM,IAAI,CAACA,MAAM,CAACiB,UAAU,CAAC,EAAE;IAClEF,IAAI,GAAGf,MAAM;IACbA,MAAM,GAAGkB,SAAS;EACpB;EAEA,IAAI,CAACH,IAAI,EAAEA,IAAI,GAAG,CAAC,CAAC;EAEpB,MAAMC,QAAQ,GAAGL,OAAO,CAACC,MAAM,CAACC,MAAM,EAAEb,MAAM,EAAEe,IAAI,CAAC;EAErD,MAAMa,IAAI,GAAGb,IAAI,CAACa,IAAI,IAAI,WAAW;EACrC,MAAMC,YAAY,GAAGd,IAAI,CAACc,YAAY,IAAI,CAAC,CAAC;EAE5C,OAAOb,QAAQ,CAACc,SAAS,CAACF,IAAI,EAAEC,YAAY,CAACE,OAAO,CAAC;AACvD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}