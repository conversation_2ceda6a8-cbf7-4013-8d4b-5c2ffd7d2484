{"ast": null, "code": "/*!\n\tCopyright (c) 2018 <PERSON>.\n\tLicensed under the MIT License (MIT), see\n\thttp://jedwatson.github.io/classnames\n*/\n/* global define */\n\n(function () {\n  'use strict';\n\n  var hasOwn = {}.hasOwnProperty;\n  function classNames() {\n    var classes = '';\n    for (var i = 0; i < arguments.length; i++) {\n      var arg = arguments[i];\n      if (arg) {\n        classes = appendClass(classes, parseValue(arg));\n      }\n    }\n    return classes;\n  }\n  function parseValue(arg) {\n    if (typeof arg === 'string' || typeof arg === 'number') {\n      return arg;\n    }\n    if (typeof arg !== 'object') {\n      return '';\n    }\n    if (Array.isArray(arg)) {\n      return classNames.apply(null, arg);\n    }\n    if (arg.toString !== Object.prototype.toString && !arg.toString.toString().includes('[native code]')) {\n      return arg.toString();\n    }\n    var classes = '';\n    for (var key in arg) {\n      if (hasOwn.call(arg, key) && arg[key]) {\n        classes = appendClass(classes, key);\n      }\n    }\n    return classes;\n  }\n  function appendClass(value, newClass) {\n    if (!newClass) {\n      return value;\n    }\n    if (value) {\n      return value + ' ' + newClass;\n    }\n    return value + newClass;\n  }\n  if (typeof module !== 'undefined' && module.exports) {\n    classNames.default = classNames;\n    module.exports = classNames;\n  } else if (typeof define === 'function' && typeof define.amd === 'object' && define.amd) {\n    // register as 'classnames', consistent with npm package name\n    define('classnames', [], function () {\n      return classNames;\n    });\n  } else {\n    window.classNames = classNames;\n  }\n})();", "map": {"version": 3, "names": ["hasOwn", "hasOwnProperty", "classNames", "classes", "i", "arguments", "length", "arg", "appendClass", "parseValue", "Array", "isArray", "apply", "toString", "Object", "prototype", "includes", "key", "call", "value", "newClass", "module", "exports", "default", "define", "amd", "window"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/test 3/medical-assistant-react/node_modules/classnames/index.js"], "sourcesContent": ["/*!\n\tCopyright (c) 2018 <PERSON>.\n\tLicensed under the MIT License (MIT), see\n\thttp://jedwatson.github.io/classnames\n*/\n/* global define */\n\n(function () {\n\t'use strict';\n\n\tvar hasOwn = {}.hasOwnProperty;\n\n\tfunction classNames () {\n\t\tvar classes = '';\n\n\t\tfor (var i = 0; i < arguments.length; i++) {\n\t\t\tvar arg = arguments[i];\n\t\t\tif (arg) {\n\t\t\t\tclasses = appendClass(classes, parseValue(arg));\n\t\t\t}\n\t\t}\n\n\t\treturn classes;\n\t}\n\n\tfunction parseValue (arg) {\n\t\tif (typeof arg === 'string' || typeof arg === 'number') {\n\t\t\treturn arg;\n\t\t}\n\n\t\tif (typeof arg !== 'object') {\n\t\t\treturn '';\n\t\t}\n\n\t\tif (Array.isArray(arg)) {\n\t\t\treturn classNames.apply(null, arg);\n\t\t}\n\n\t\tif (arg.toString !== Object.prototype.toString && !arg.toString.toString().includes('[native code]')) {\n\t\t\treturn arg.toString();\n\t\t}\n\n\t\tvar classes = '';\n\n\t\tfor (var key in arg) {\n\t\t\tif (hasOwn.call(arg, key) && arg[key]) {\n\t\t\t\tclasses = appendClass(classes, key);\n\t\t\t}\n\t\t}\n\n\t\treturn classes;\n\t}\n\n\tfunction appendClass (value, newClass) {\n\t\tif (!newClass) {\n\t\t\treturn value;\n\t\t}\n\t\n\t\tif (value) {\n\t\t\treturn value + ' ' + newClass;\n\t\t}\n\t\n\t\treturn value + newClass;\n\t}\n\n\tif (typeof module !== 'undefined' && module.exports) {\n\t\tclassNames.default = classNames;\n\t\tmodule.exports = classNames;\n\t} else if (typeof define === 'function' && typeof define.amd === 'object' && define.amd) {\n\t\t// register as 'classnames', consistent with npm package name\n\t\tdefine('classnames', [], function () {\n\t\t\treturn classNames;\n\t\t});\n\t} else {\n\t\twindow.classNames = classNames;\n\t}\n}());\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEC,aAAY;EACZ,YAAY;;EAEZ,IAAIA,MAAM,GAAG,CAAC,CAAC,CAACC,cAAc;EAE9B,SAASC,UAAUA,CAAA,EAAI;IACtB,IAAIC,OAAO,GAAG,EAAE;IAEhB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;MAC1C,IAAIG,GAAG,GAAGF,SAAS,CAACD,CAAC,CAAC;MACtB,IAAIG,GAAG,EAAE;QACRJ,OAAO,GAAGK,WAAW,CAACL,OAAO,EAAEM,UAAU,CAACF,GAAG,CAAC,CAAC;MAChD;IACD;IAEA,OAAOJ,OAAO;EACf;EAEA,SAASM,UAAUA,CAAEF,GAAG,EAAE;IACzB,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;MACvD,OAAOA,GAAG;IACX;IAEA,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;MAC5B,OAAO,EAAE;IACV;IAEA,IAAIG,KAAK,CAACC,OAAO,CAACJ,GAAG,CAAC,EAAE;MACvB,OAAOL,UAAU,CAACU,KAAK,CAAC,IAAI,EAAEL,GAAG,CAAC;IACnC;IAEA,IAAIA,GAAG,CAACM,QAAQ,KAAKC,MAAM,CAACC,SAAS,CAACF,QAAQ,IAAI,CAACN,GAAG,CAACM,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAACG,QAAQ,CAAC,eAAe,CAAC,EAAE;MACrG,OAAOT,GAAG,CAACM,QAAQ,CAAC,CAAC;IACtB;IAEA,IAAIV,OAAO,GAAG,EAAE;IAEhB,KAAK,IAAIc,GAAG,IAAIV,GAAG,EAAE;MACpB,IAAIP,MAAM,CAACkB,IAAI,CAACX,GAAG,EAAEU,GAAG,CAAC,IAAIV,GAAG,CAACU,GAAG,CAAC,EAAE;QACtCd,OAAO,GAAGK,WAAW,CAACL,OAAO,EAAEc,GAAG,CAAC;MACpC;IACD;IAEA,OAAOd,OAAO;EACf;EAEA,SAASK,WAAWA,CAAEW,KAAK,EAAEC,QAAQ,EAAE;IACtC,IAAI,CAACA,QAAQ,EAAE;MACd,OAAOD,KAAK;IACb;IAEA,IAAIA,KAAK,EAAE;MACV,OAAOA,KAAK,GAAG,GAAG,GAAGC,QAAQ;IAC9B;IAEA,OAAOD,KAAK,GAAGC,QAAQ;EACxB;EAEA,IAAI,OAAOC,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACC,OAAO,EAAE;IACpDpB,UAAU,CAACqB,OAAO,GAAGrB,UAAU;IAC/BmB,MAAM,CAACC,OAAO,GAAGpB,UAAU;EAC5B,CAAC,MAAM,IAAI,OAAOsB,MAAM,KAAK,UAAU,IAAI,OAAOA,MAAM,CAACC,GAAG,KAAK,QAAQ,IAAID,MAAM,CAACC,GAAG,EAAE;IACxF;IACAD,MAAM,CAAC,YAAY,EAAE,EAAE,EAAE,YAAY;MACpC,OAAOtB,UAAU;IAClB,CAAC,CAAC;EACH,CAAC,MAAM;IACNwB,MAAM,CAACxB,UAAU,GAAGA,UAAU;EAC/B;AACD,CAAC,EAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}