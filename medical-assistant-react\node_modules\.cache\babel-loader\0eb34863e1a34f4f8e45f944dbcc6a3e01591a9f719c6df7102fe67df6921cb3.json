{"ast": null, "code": "import _objectSpread from \"C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/test 3/medical-assistant-react/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport { isSVGComponent } from './is-svg-component.mjs';\nimport { createUseRender } from '../use-render.mjs';\nimport { svgMotionConfig } from '../../svg/config-motion.mjs';\nimport { htmlMotionConfig } from '../../html/config-motion.mjs';\nfunction createDomMotionConfig(Component, _ref, preloadedFeatures, createVisualElement) {\n  let {\n    forwardMotionProps = false\n  } = _ref;\n  const baseConfig = isSVGComponent(Component) ? svgMotionConfig : htmlMotionConfig;\n  return _objectSpread(_objectSpread({}, baseConfig), {}, {\n    preloadedFeatures,\n    useRender: createUseRender(forwardMotionProps),\n    createVisualElement,\n    Component\n  });\n}\nexport { createDomMotionConfig };", "map": {"version": 3, "names": ["isSVGComponent", "createUseRender", "svgMotionConfig", "htmlMotionConfig", "createDomMotionConfig", "Component", "_ref", "preloadedFeatures", "createVisualElement", "forwardMotionProps", "baseConfig", "_objectSpread", "useRender"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/test 3/medical-assistant-react/node_modules/framer-motion/dist/es/render/dom/utils/create-config.mjs"], "sourcesContent": ["import { isSVGComponent } from './is-svg-component.mjs';\nimport { createUseRender } from '../use-render.mjs';\nimport { svgMotionConfig } from '../../svg/config-motion.mjs';\nimport { htmlMotionConfig } from '../../html/config-motion.mjs';\n\nfunction createDomMotionConfig(Component, { forwardMotionProps = false }, preloadedFeatures, createVisualElement) {\n    const baseConfig = isSVGComponent(Component)\n        ? svgMotionConfig\n        : htmlMotionConfig;\n    return {\n        ...baseConfig,\n        preloadedFeatures,\n        useRender: createUseRender(forwardMotionProps),\n        createVisualElement,\n        Component,\n    };\n}\n\nexport { createDomMotionConfig };\n"], "mappings": ";AAAA,SAASA,cAAc,QAAQ,wBAAwB;AACvD,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,eAAe,QAAQ,6BAA6B;AAC7D,SAASC,gBAAgB,QAAQ,8BAA8B;AAE/D,SAASC,qBAAqBA,CAACC,SAAS,EAAAC,IAAA,EAAkCC,iBAAiB,EAAEC,mBAAmB,EAAE;EAAA,IAAxE;IAAEC,kBAAkB,GAAG;EAAM,CAAC,GAAAH,IAAA;EACpE,MAAMI,UAAU,GAAGV,cAAc,CAACK,SAAS,CAAC,GACtCH,eAAe,GACfC,gBAAgB;EACtB,OAAAQ,aAAA,CAAAA,aAAA,KACOD,UAAU;IACbH,iBAAiB;IACjBK,SAAS,EAAEX,eAAe,CAACQ,kBAAkB,CAAC;IAC9CD,mBAAmB;IACnBH;EAAS;AAEjB;AAEA,SAASD,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}