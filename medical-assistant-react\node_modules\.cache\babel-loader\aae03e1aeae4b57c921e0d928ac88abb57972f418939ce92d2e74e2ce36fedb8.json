{"ast": null, "code": "const Mode = require('./mode');\n\n/**\n * Array of characters available in alphanumeric mode\n *\n * As per QR Code specification, to each character\n * is assigned a value from 0 to 44 which in this case coincides\n * with the array index\n *\n * @type {Array}\n */\nconst ALPHA_NUM_CHARS = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z', ' ', '$', '%', '*', '+', '-', '.', '/', ':'];\nfunction AlphanumericData(data) {\n  this.mode = Mode.ALPHANUMERIC;\n  this.data = data;\n}\nAlphanumericData.getBitsLength = function getBitsLength(length) {\n  return 11 * Math.floor(length / 2) + 6 * (length % 2);\n};\nAlphanumericData.prototype.getLength = function getLength() {\n  return this.data.length;\n};\nAlphanumericData.prototype.getBitsLength = function getBitsLength() {\n  return AlphanumericData.getBitsLength(this.data.length);\n};\nAlphanumericData.prototype.write = function write(bitBuffer) {\n  let i;\n\n  // Input data characters are divided into groups of two characters\n  // and encoded as 11-bit binary codes.\n  for (i = 0; i + 2 <= this.data.length; i += 2) {\n    // The character value of the first character is multiplied by 45\n    let value = ALPHA_NUM_CHARS.indexOf(this.data[i]) * 45;\n\n    // The character value of the second digit is added to the product\n    value += ALPHA_NUM_CHARS.indexOf(this.data[i + 1]);\n\n    // The sum is then stored as 11-bit binary number\n    bitBuffer.put(value, 11);\n  }\n\n  // If the number of input data characters is not a multiple of two,\n  // the character value of the final character is encoded as a 6-bit binary number.\n  if (this.data.length % 2) {\n    bitBuffer.put(ALPHA_NUM_CHARS.indexOf(this.data[i]), 6);\n  }\n};\nmodule.exports = AlphanumericData;", "map": {"version": 3, "names": ["Mode", "require", "ALPHA_NUM_CHARS", "AlphanumericData", "data", "mode", "ALPHANUMERIC", "getBitsLength", "length", "Math", "floor", "prototype", "<PERSON><PERSON><PERSON><PERSON>", "write", "bitBuffer", "i", "value", "indexOf", "put", "module", "exports"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/test 3/medical-assistant-react/node_modules/qrcode/lib/core/alphanumeric-data.js"], "sourcesContent": ["const Mode = require('./mode')\n\n/**\n * Array of characters available in alphanumeric mode\n *\n * As per QR Code specification, to each character\n * is assigned a value from 0 to 44 which in this case coincides\n * with the array index\n *\n * @type {Array}\n */\nconst ALPHA_NUM_CHARS = [\n  '0', '1', '2', '3', '4', '5', '6', '7', '8', '9',\n  'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M',\n  'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z',\n  ' ', '$', '%', '*', '+', '-', '.', '/', ':'\n]\n\nfunction AlphanumericData (data) {\n  this.mode = Mode.ALPHANUMERIC\n  this.data = data\n}\n\nAlphanumericData.getBitsLength = function getBitsLength (length) {\n  return 11 * Math.floor(length / 2) + 6 * (length % 2)\n}\n\nAlphanumericData.prototype.getLength = function getLength () {\n  return this.data.length\n}\n\nAlphanumericData.prototype.getBitsLength = function getBitsLength () {\n  return AlphanumericData.getBitsLength(this.data.length)\n}\n\nAlphanumericData.prototype.write = function write (bitBuffer) {\n  let i\n\n  // Input data characters are divided into groups of two characters\n  // and encoded as 11-bit binary codes.\n  for (i = 0; i + 2 <= this.data.length; i += 2) {\n    // The character value of the first character is multiplied by 45\n    let value = ALPHA_NUM_CHARS.indexOf(this.data[i]) * 45\n\n    // The character value of the second digit is added to the product\n    value += ALPHA_NUM_CHARS.indexOf(this.data[i + 1])\n\n    // The sum is then stored as 11-bit binary number\n    bitBuffer.put(value, 11)\n  }\n\n  // If the number of input data characters is not a multiple of two,\n  // the character value of the final character is encoded as a 6-bit binary number.\n  if (this.data.length % 2) {\n    bitBuffer.put(ALPHA_NUM_CHARS.indexOf(this.data[i]), 6)\n  }\n}\n\nmodule.exports = AlphanumericData\n"], "mappings": "AAAA,MAAMA,IAAI,GAAGC,OAAO,CAAC,QAAQ,CAAC;;AAE9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,eAAe,GAAG,CACtB,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAChD,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAC/D,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAC/D,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAC5C;AAED,SAASC,gBAAgBA,CAAEC,IAAI,EAAE;EAC/B,IAAI,CAACC,IAAI,GAAGL,IAAI,CAACM,YAAY;EAC7B,IAAI,CAACF,IAAI,GAAGA,IAAI;AAClB;AAEAD,gBAAgB,CAACI,aAAa,GAAG,SAASA,aAAaA,CAAEC,MAAM,EAAE;EAC/D,OAAO,EAAE,GAAGC,IAAI,CAACC,KAAK,CAACF,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,IAAIA,MAAM,GAAG,CAAC,CAAC;AACvD,CAAC;AAEDL,gBAAgB,CAACQ,SAAS,CAACC,SAAS,GAAG,SAASA,SAASA,CAAA,EAAI;EAC3D,OAAO,IAAI,CAACR,IAAI,CAACI,MAAM;AACzB,CAAC;AAEDL,gBAAgB,CAACQ,SAAS,CAACJ,aAAa,GAAG,SAASA,aAAaA,CAAA,EAAI;EACnE,OAAOJ,gBAAgB,CAACI,aAAa,CAAC,IAAI,CAACH,IAAI,CAACI,MAAM,CAAC;AACzD,CAAC;AAEDL,gBAAgB,CAACQ,SAAS,CAACE,KAAK,GAAG,SAASA,KAAKA,CAAEC,SAAS,EAAE;EAC5D,IAAIC,CAAC;;EAEL;EACA;EACA,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,IAAI,IAAI,CAACX,IAAI,CAACI,MAAM,EAAEO,CAAC,IAAI,CAAC,EAAE;IAC7C;IACA,IAAIC,KAAK,GAAGd,eAAe,CAACe,OAAO,CAAC,IAAI,CAACb,IAAI,CAACW,CAAC,CAAC,CAAC,GAAG,EAAE;;IAEtD;IACAC,KAAK,IAAId,eAAe,CAACe,OAAO,CAAC,IAAI,CAACb,IAAI,CAACW,CAAC,GAAG,CAAC,CAAC,CAAC;;IAElD;IACAD,SAAS,CAACI,GAAG,CAACF,KAAK,EAAE,EAAE,CAAC;EAC1B;;EAEA;EACA;EACA,IAAI,IAAI,CAACZ,IAAI,CAACI,MAAM,GAAG,CAAC,EAAE;IACxBM,SAAS,CAACI,GAAG,CAAChB,eAAe,CAACe,OAAO,CAAC,IAAI,CAACb,IAAI,CAACW,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;EACzD;AACF,CAAC;AAEDI,MAAM,CAACC,OAAO,GAAGjB,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}