{"ast": null, "code": "import _objectSpread from \"C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/test 3/medical-assistant-react/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport { spring } from '../../animation/generators/spring/index.mjs';\nimport { calcGeneratorDuration, maxGeneratorDuration } from '../../animation/generators/utils/calc-duration.mjs';\nimport { millisecondsToSeconds } from '../../utils/time-conversion.mjs';\n\n/**\n * Create a progress => progress easing function from a generator.\n */\nfunction createGeneratorEasing(options) {\n  let scale = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 100;\n  const generator = spring(_objectSpread({\n    keyframes: [0, scale]\n  }, options));\n  const duration = Math.min(calcGeneratorDuration(generator), maxGeneratorDuration);\n  return {\n    type: \"keyframes\",\n    ease: progress => generator.next(duration * progress).value / scale,\n    duration: millisecondsToSeconds(duration)\n  };\n}\nexport { createGeneratorEasing };", "map": {"version": 3, "names": ["spring", "calcGeneratorDuration", "maxGeneratorDuration", "millisecondsToSeconds", "createGeneratorEasing", "options", "scale", "arguments", "length", "undefined", "generator", "_objectSpread", "keyframes", "duration", "Math", "min", "type", "ease", "progress", "next", "value"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/test 3/medical-assistant-react/node_modules/framer-motion/dist/es/easing/utils/create-generator-easing.mjs"], "sourcesContent": ["import { spring } from '../../animation/generators/spring/index.mjs';\nimport { calcGeneratorDuration, maxGeneratorDuration } from '../../animation/generators/utils/calc-duration.mjs';\nimport { millisecondsToSeconds } from '../../utils/time-conversion.mjs';\n\n/**\n * Create a progress => progress easing function from a generator.\n */\nfunction createGeneratorEasing(options, scale = 100) {\n    const generator = spring({ keyframes: [0, scale], ...options });\n    const duration = Math.min(calcGeneratorDuration(generator), maxGeneratorDuration);\n    return {\n        type: \"keyframes\",\n        ease: (progress) => generator.next(duration * progress).value / scale,\n        duration: millisecondsToSeconds(duration),\n    };\n}\n\nexport { createGeneratorEasing };\n"], "mappings": ";AAAA,SAASA,MAAM,QAAQ,6CAA6C;AACpE,SAASC,qBAAqB,EAAEC,oBAAoB,QAAQ,oDAAoD;AAChH,SAASC,qBAAqB,QAAQ,iCAAiC;;AAEvE;AACA;AACA;AACA,SAASC,qBAAqBA,CAACC,OAAO,EAAe;EAAA,IAAbC,KAAK,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,GAAG;EAC/C,MAAMG,SAAS,GAAGV,MAAM,CAAAW,aAAA;IAAGC,SAAS,EAAE,CAAC,CAAC,EAAEN,KAAK;EAAC,GAAKD,OAAO,CAAE,CAAC;EAC/D,MAAMQ,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAACd,qBAAqB,CAACS,SAAS,CAAC,EAAER,oBAAoB,CAAC;EACjF,OAAO;IACHc,IAAI,EAAE,WAAW;IACjBC,IAAI,EAAGC,QAAQ,IAAKR,SAAS,CAACS,IAAI,CAACN,QAAQ,GAAGK,QAAQ,CAAC,CAACE,KAAK,GAAGd,KAAK;IACrEO,QAAQ,EAAEV,qBAAqB,CAACU,QAAQ;EAC5C,CAAC;AACL;AAEA,SAAST,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}