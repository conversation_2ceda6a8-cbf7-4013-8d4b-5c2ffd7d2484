{"ast": null, "code": "const Polynomial = require('./polynomial');\nfunction ReedSolomonEncoder(degree) {\n  this.genPoly = undefined;\n  this.degree = degree;\n  if (this.degree) this.initialize(this.degree);\n}\n\n/**\n * Initialize the encoder.\n * The input param should correspond to the number of error correction codewords.\n *\n * @param  {Number} degree\n */\nReedSolomonEncoder.prototype.initialize = function initialize(degree) {\n  // create an irreducible generator polynomial\n  this.degree = degree;\n  this.genPoly = Polynomial.generateECPolynomial(this.degree);\n};\n\n/**\n * Encodes a chunk of data\n *\n * @param  {Uint8Array} data Buffer containing input data\n * @return {Uint8Array}      Buffer containing encoded data\n */\nReedSolomonEncoder.prototype.encode = function encode(data) {\n  if (!this.genPoly) {\n    throw new Error('Encoder not initialized');\n  }\n\n  // Calculate EC for this data block\n  // extends data size to data+genPoly size\n  const paddedData = new Uint8Array(data.length + this.degree);\n  paddedData.set(data);\n\n  // The error correction codewords are the remainder after dividing the data codewords\n  // by a generator polynomial\n  const remainder = Polynomial.mod(paddedData, this.genPoly);\n\n  // return EC data blocks (last n byte, where n is the degree of genPoly)\n  // If coefficients number in remainder are less than genPoly degree,\n  // pad with 0s to the left to reach the needed number of coefficients\n  const start = this.degree - remainder.length;\n  if (start > 0) {\n    const buff = new Uint8Array(this.degree);\n    buff.set(remainder, start);\n    return buff;\n  }\n  return remainder;\n};\nmodule.exports = ReedSolomonEncoder;", "map": {"version": 3, "names": ["Polynomial", "require", "ReedSolomonEncoder", "degree", "genPoly", "undefined", "initialize", "prototype", "generateECPolynomial", "encode", "data", "Error", "paddedData", "Uint8Array", "length", "set", "remainder", "mod", "start", "buff", "module", "exports"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/test 3/medical-assistant-react/node_modules/qrcode/lib/core/reed-solomon-encoder.js"], "sourcesContent": ["const Polynomial = require('./polynomial')\n\nfunction ReedSolomonEncoder (degree) {\n  this.genPoly = undefined\n  this.degree = degree\n\n  if (this.degree) this.initialize(this.degree)\n}\n\n/**\n * Initialize the encoder.\n * The input param should correspond to the number of error correction codewords.\n *\n * @param  {Number} degree\n */\nReedSolomonEncoder.prototype.initialize = function initialize (degree) {\n  // create an irreducible generator polynomial\n  this.degree = degree\n  this.genPoly = Polynomial.generateECPolynomial(this.degree)\n}\n\n/**\n * Encodes a chunk of data\n *\n * @param  {Uint8Array} data Buffer containing input data\n * @return {Uint8Array}      Buffer containing encoded data\n */\nReedSolomonEncoder.prototype.encode = function encode (data) {\n  if (!this.genPoly) {\n    throw new Error('Encoder not initialized')\n  }\n\n  // Calculate EC for this data block\n  // extends data size to data+genPoly size\n  const paddedData = new Uint8Array(data.length + this.degree)\n  paddedData.set(data)\n\n  // The error correction codewords are the remainder after dividing the data codewords\n  // by a generator polynomial\n  const remainder = Polynomial.mod(paddedData, this.genPoly)\n\n  // return EC data blocks (last n byte, where n is the degree of genPoly)\n  // If coefficients number in remainder are less than genPoly degree,\n  // pad with 0s to the left to reach the needed number of coefficients\n  const start = this.degree - remainder.length\n  if (start > 0) {\n    const buff = new Uint8Array(this.degree)\n    buff.set(remainder, start)\n\n    return buff\n  }\n\n  return remainder\n}\n\nmodule.exports = ReedSolomonEncoder\n"], "mappings": "AAAA,MAAMA,UAAU,GAAGC,OAAO,CAAC,cAAc,CAAC;AAE1C,SAASC,kBAAkBA,CAAEC,MAAM,EAAE;EACnC,IAAI,CAACC,OAAO,GAAGC,SAAS;EACxB,IAAI,CAACF,MAAM,GAAGA,MAAM;EAEpB,IAAI,IAAI,CAACA,MAAM,EAAE,IAAI,CAACG,UAAU,CAAC,IAAI,CAACH,MAAM,CAAC;AAC/C;;AAEA;AACA;AACA;AACA;AACA;AACA;AACAD,kBAAkB,CAACK,SAAS,CAACD,UAAU,GAAG,SAASA,UAAUA,CAAEH,MAAM,EAAE;EACrE;EACA,IAAI,CAACA,MAAM,GAAGA,MAAM;EACpB,IAAI,CAACC,OAAO,GAAGJ,UAAU,CAACQ,oBAAoB,CAAC,IAAI,CAACL,MAAM,CAAC;AAC7D,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACAD,kBAAkB,CAACK,SAAS,CAACE,MAAM,GAAG,SAASA,MAAMA,CAAEC,IAAI,EAAE;EAC3D,IAAI,CAAC,IAAI,CAACN,OAAO,EAAE;IACjB,MAAM,IAAIO,KAAK,CAAC,yBAAyB,CAAC;EAC5C;;EAEA;EACA;EACA,MAAMC,UAAU,GAAG,IAAIC,UAAU,CAACH,IAAI,CAACI,MAAM,GAAG,IAAI,CAACX,MAAM,CAAC;EAC5DS,UAAU,CAACG,GAAG,CAACL,IAAI,CAAC;;EAEpB;EACA;EACA,MAAMM,SAAS,GAAGhB,UAAU,CAACiB,GAAG,CAACL,UAAU,EAAE,IAAI,CAACR,OAAO,CAAC;;EAE1D;EACA;EACA;EACA,MAAMc,KAAK,GAAG,IAAI,CAACf,MAAM,GAAGa,SAAS,CAACF,MAAM;EAC5C,IAAII,KAAK,GAAG,CAAC,EAAE;IACb,MAAMC,IAAI,GAAG,IAAIN,UAAU,CAAC,IAAI,CAACV,MAAM,CAAC;IACxCgB,IAAI,CAACJ,GAAG,CAACC,SAAS,EAAEE,KAAK,CAAC;IAE1B,OAAOC,IAAI;EACb;EAEA,OAAOH,SAAS;AAClB,CAAC;AAEDI,MAAM,CAACC,OAAO,GAAGnB,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}