{"ast": null, "code": "const Mode = require('./mode');\nfunction NumericData(data) {\n  this.mode = Mode.NUMERIC;\n  this.data = data.toString();\n}\nNumericData.getBitsLength = function getBitsLength(length) {\n  return 10 * Math.floor(length / 3) + (length % 3 ? length % 3 * 3 + 1 : 0);\n};\nNumericData.prototype.getLength = function getLength() {\n  return this.data.length;\n};\nNumericData.prototype.getBitsLength = function getBitsLength() {\n  return NumericData.getBitsLength(this.data.length);\n};\nNumericData.prototype.write = function write(bitBuffer) {\n  let i, group, value;\n\n  // The input data string is divided into groups of three digits,\n  // and each group is converted to its 10-bit binary equivalent.\n  for (i = 0; i + 3 <= this.data.length; i += 3) {\n    group = this.data.substr(i, 3);\n    value = parseInt(group, 10);\n    bitBuffer.put(value, 10);\n  }\n\n  // If the number of input digits is not an exact multiple of three,\n  // the final one or two digits are converted to 4 or 7 bits respectively.\n  const remainingNum = this.data.length - i;\n  if (remainingNum > 0) {\n    group = this.data.substr(i);\n    value = parseInt(group, 10);\n    bitBuffer.put(value, remainingNum * 3 + 1);\n  }\n};\nmodule.exports = NumericData;", "map": {"version": 3, "names": ["Mode", "require", "NumericData", "data", "mode", "NUMERIC", "toString", "getBitsLength", "length", "Math", "floor", "prototype", "<PERSON><PERSON><PERSON><PERSON>", "write", "bitBuffer", "i", "group", "value", "substr", "parseInt", "put", "remainingNum", "module", "exports"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/test 3/medical-assistant-react/node_modules/qrcode/lib/core/numeric-data.js"], "sourcesContent": ["const Mode = require('./mode')\n\nfunction NumericData (data) {\n  this.mode = Mode.NUMERIC\n  this.data = data.toString()\n}\n\nNumericData.getBitsLength = function getBitsLength (length) {\n  return 10 * Math.floor(length / 3) + ((length % 3) ? ((length % 3) * 3 + 1) : 0)\n}\n\nNumericData.prototype.getLength = function getLength () {\n  return this.data.length\n}\n\nNumericData.prototype.getBitsLength = function getBitsLength () {\n  return NumericData.getBitsLength(this.data.length)\n}\n\nNumericData.prototype.write = function write (bitBuffer) {\n  let i, group, value\n\n  // The input data string is divided into groups of three digits,\n  // and each group is converted to its 10-bit binary equivalent.\n  for (i = 0; i + 3 <= this.data.length; i += 3) {\n    group = this.data.substr(i, 3)\n    value = parseInt(group, 10)\n\n    bitBuffer.put(value, 10)\n  }\n\n  // If the number of input digits is not an exact multiple of three,\n  // the final one or two digits are converted to 4 or 7 bits respectively.\n  const remainingNum = this.data.length - i\n  if (remainingNum > 0) {\n    group = this.data.substr(i)\n    value = parseInt(group, 10)\n\n    bitBuffer.put(value, remainingNum * 3 + 1)\n  }\n}\n\nmodule.exports = NumericData\n"], "mappings": "AAAA,MAAMA,IAAI,GAAGC,OAAO,CAAC,QAAQ,CAAC;AAE9B,SAASC,WAAWA,CAAEC,IAAI,EAAE;EAC1B,IAAI,CAACC,IAAI,GAAGJ,IAAI,CAACK,OAAO;EACxB,IAAI,CAACF,IAAI,GAAGA,IAAI,CAACG,QAAQ,CAAC,CAAC;AAC7B;AAEAJ,WAAW,CAACK,aAAa,GAAG,SAASA,aAAaA,CAAEC,MAAM,EAAE;EAC1D,OAAO,EAAE,GAAGC,IAAI,CAACC,KAAK,CAACF,MAAM,GAAG,CAAC,CAAC,IAAKA,MAAM,GAAG,CAAC,GAAMA,MAAM,GAAG,CAAC,GAAI,CAAC,GAAG,CAAC,GAAI,CAAC,CAAC;AAClF,CAAC;AAEDN,WAAW,CAACS,SAAS,CAACC,SAAS,GAAG,SAASA,SAASA,CAAA,EAAI;EACtD,OAAO,IAAI,CAACT,IAAI,CAACK,MAAM;AACzB,CAAC;AAEDN,WAAW,CAACS,SAAS,CAACJ,aAAa,GAAG,SAASA,aAAaA,CAAA,EAAI;EAC9D,OAAOL,WAAW,CAACK,aAAa,CAAC,IAAI,CAACJ,IAAI,CAACK,MAAM,CAAC;AACpD,CAAC;AAEDN,WAAW,CAACS,SAAS,CAACE,KAAK,GAAG,SAASA,KAAKA,CAAEC,SAAS,EAAE;EACvD,IAAIC,CAAC,EAAEC,KAAK,EAAEC,KAAK;;EAEnB;EACA;EACA,KAAKF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,IAAI,IAAI,CAACZ,IAAI,CAACK,MAAM,EAAEO,CAAC,IAAI,CAAC,EAAE;IAC7CC,KAAK,GAAG,IAAI,CAACb,IAAI,CAACe,MAAM,CAACH,CAAC,EAAE,CAAC,CAAC;IAC9BE,KAAK,GAAGE,QAAQ,CAACH,KAAK,EAAE,EAAE,CAAC;IAE3BF,SAAS,CAACM,GAAG,CAACH,KAAK,EAAE,EAAE,CAAC;EAC1B;;EAEA;EACA;EACA,MAAMI,YAAY,GAAG,IAAI,CAAClB,IAAI,CAACK,MAAM,GAAGO,CAAC;EACzC,IAAIM,YAAY,GAAG,CAAC,EAAE;IACpBL,KAAK,GAAG,IAAI,CAACb,IAAI,CAACe,MAAM,CAACH,CAAC,CAAC;IAC3BE,KAAK,GAAGE,QAAQ,CAACH,KAAK,EAAE,EAAE,CAAC;IAE3BF,SAAS,CAACM,GAAG,CAACH,KAAK,EAAEI,YAAY,GAAG,CAAC,GAAG,CAAC,CAAC;EAC5C;AACF,CAAC;AAEDC,MAAM,CAACC,OAAO,GAAGrB,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}