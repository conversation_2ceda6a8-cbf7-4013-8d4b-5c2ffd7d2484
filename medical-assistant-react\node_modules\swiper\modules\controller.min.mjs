import{n as nextTick,l as elementTransitionEnd}from"../shared/utils.min.mjs";function Controller(t){let{swiper:r,extendParams:e,on:n}=t;function o(t,r){const e=function(){let t,r,e;return(n,o)=>{for(r=-1,t=n.length;t-r>1;)e=t+r>>1,n[e]<=o?r=e:t=e;return t}}();let n,o;return this.x=t,this.y=r,this.lastIndex=t.length-1,this.interpolate=function(t){return t?(o=e(this.x,t),n=o-1,(t-this.x[n])*(this.y[o]-this.y[n])/(this.x[o]-this.x[n])+this.y[n]):0},this}function l(){r.controller.control&&r.controller.spline&&(r.controller.spline=void 0,delete r.controller.spline)}e({controller:{control:void 0,inverse:!1,by:"slide"}}),r.controller={control:void 0},n("beforeInit",(()=>{if("undefined"!=typeof window&&("string"==typeof r.params.controller.control||r.params.controller.control instanceof HTMLElement)){("string"==typeof r.params.controller.control?[...document.querySelectorAll(r.params.controller.control)]:[r.params.controller.control]).forEach((t=>{if(r.controller.control||(r.controller.control=[]),t&&t.swiper)r.controller.control.push(t.swiper);else if(t){const e=`${r.params.eventsPrefix}init`,n=o=>{r.controller.control.push(o.detail[0]),r.update(),t.removeEventListener(e,n)};t.addEventListener(e,n)}}))}else r.controller.control=r.params.controller.control})),n("update",(()=>{l()})),n("resize",(()=>{l()})),n("observerUpdate",(()=>{l()})),n("setTranslate",((t,e,n)=>{r.controller.control&&!r.controller.control.destroyed&&r.controller.setTranslate(e,n)})),n("setTransition",((t,e,n)=>{r.controller.control&&!r.controller.control.destroyed&&r.controller.setTransition(e,n)})),Object.assign(r.controller,{setTranslate:function(t,e){const n=r.controller.control;let l,s;const i=r.constructor;function a(t){if(t.destroyed)return;const e=r.rtlTranslate?-r.translate:r.translate;"slide"===r.params.controller.by&&(!function(t){r.controller.spline=r.params.loop?new o(r.slidesGrid,t.slidesGrid):new o(r.snapGrid,t.snapGrid)}(t),s=-r.controller.spline.interpolate(-e)),s&&"container"!==r.params.controller.by||(l=(t.maxTranslate()-t.minTranslate())/(r.maxTranslate()-r.minTranslate()),!Number.isNaN(l)&&Number.isFinite(l)||(l=1),s=(e-r.minTranslate())*l+t.minTranslate()),r.params.controller.inverse&&(s=t.maxTranslate()-s),t.updateProgress(s),t.setTranslate(s,r),t.updateActiveIndex(),t.updateSlidesClasses()}if(Array.isArray(n))for(let t=0;t<n.length;t+=1)n[t]!==e&&n[t]instanceof i&&a(n[t]);else n instanceof i&&e!==n&&a(n)},setTransition:function(t,e){const n=r.constructor,o=r.controller.control;let l;function s(e){e.destroyed||(e.setTransition(t,r),0!==t&&(e.transitionStart(),e.params.autoHeight&&nextTick((()=>{e.updateAutoHeight()})),elementTransitionEnd(e.wrapperEl,(()=>{o&&e.transitionEnd()}))))}if(Array.isArray(o))for(l=0;l<o.length;l+=1)o[l]!==e&&o[l]instanceof n&&s(o[l]);else o instanceof n&&e!==o&&s(o)}})}export{Controller as default};
//# sourceMappingURL=controller.min.mjs.map