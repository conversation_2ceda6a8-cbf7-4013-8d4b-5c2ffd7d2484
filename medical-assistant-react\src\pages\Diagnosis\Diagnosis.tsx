import React, { useState, useRef, useEffect } from 'react';
import { Container, Row, Col, Card, Button, Form, Modal, Alert } from 'react-bootstrap';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
  faRobot, faTrash, faDownload, faPaperPlane, faStethoscope,
  faHeadSideVirus, faThermometerHalf, faDizzy, faFire,
  faExclamationTriangle, faAmbulance, faPhone, faHospital
} from '@fortawesome/free-solid-svg-icons';
import ChatMessage from './components/ChatMessage';
import TypingIndicator from './components/TypingIndicator';
import { useToast } from '../../contexts/ToastContext';

interface Message {
  id: string;
  text: string;
  isUser: boolean;
  timestamp: Date;
}

const quickActions = [
  { icon: faHeadSideVirus, text: 'أعاني من صداع شديد', message: 'أعاني من صداع شديد' },
  { icon: faThermometerHalf, text: 'حمى', message: 'عندي حمى وأعراض برد' },
  { icon: faDizzy, text: 'إغماء', message: 'أعمل إيه لو حد اغمى عليه؟' },
  { icon: faFire, text: 'حروق', message: 'إزاي أتعامل مع الحروق؟' }
];

const emergencyKeywords = [
  'طوارئ', 'emergency', 'نزيف', 'bleeding', 'اختناق', 'choking',
  'توقف القلب', 'cardiac arrest', 'فقدان الوعي', 'unconscious',
  'حادث', 'accident', 'سكتة', 'stroke', 'نوبة قلبية', 'heart attack',
  'تسمم', 'poisoning', 'حريق', 'fire', 'غرق', 'drowning'
];

const Diagnosis: React.FC = () => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputText, setInputText] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [showWelcome, setShowWelcome] = useState(true);
  const [showEmergencyModal, setShowEmergencyModal] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const { showToast } = useToast();

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const containsEmergencyKeyword = (text: string): boolean => {
    return emergencyKeywords.some(keyword => 
      text.toLowerCase().includes(keyword.toLowerCase())
    );
  };

  const generateBotResponse = async (userMessage: string): Promise<string> => {
    // Simulate AI processing delay
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Simple response logic (in real app, this would call an AI API)
    const lowerMessage = userMessage.toLowerCase();
    
    if (lowerMessage.includes('صداع')) {
      return 'بناءً على وصفك للصداع، قد يكون السبب:\n\n• التوتر والإجهاد\n• قلة النوم\n• الجفاف\n• التهاب الجيوب الأنفية\n\nالتوصيات:\n• اشرب كمية كافية من الماء\n• احصل على راحة كافية\n• تجنب الضوضاء والأضواء الساطعة\n• إذا استمر الصداع أكثر من 24 ساعة، استشر طبيباً';
    }
    
    if (lowerMessage.includes('حمى') || lowerMessage.includes('برد')) {
      return 'أعراض الحمى والبرد قد تشير إلى:\n\n• عدوى فيروسية (نزلة برد)\n• التهاب في الجهاز التنفسي\n• الإنفلونزا\n\nالتوصيات:\n• الراحة التامة\n• شرب السوائل الدافئة\n• تناول خافض الحرارة حسب الحاجة\n• إذا ارتفعت الحرارة عن 39°، راجع الطبيب فوراً';
    }

    return 'شكراً لك على وصف أعراضك. بناءً على المعلومات المتوفرة، أنصحك بـ:\n\n• مراقبة الأعراض عن كثب\n• الحصول على راحة كافية\n• شرب كمية كافية من السوائل\n• إذا تفاقمت الأعراض، استشر طبيباً مختصاً\n\nتذكر: هذا تقييم أولي ولا يغني عن استشارة طبية متخصصة.';
  };

  const handleSendMessage = async () => {
    if (!inputText.trim() || isTyping) return;

    // Check for emergency keywords
    if (containsEmergencyKeyword(inputText)) {
      setShowEmergencyModal(true);
    }

    // Hide welcome section
    setShowWelcome(false);

    // Add user message
    const userMessage: Message = {
      id: Date.now().toString(),
      text: inputText,
      isUser: true,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    const currentInput = inputText;
    setInputText('');
    setIsTyping(true);

    try {
      // Generate bot response
      const botResponse = await generateBotResponse(currentInput);
      
      const botMessage: Message = {
        id: (Date.now() + 1).toString(),
        text: botResponse,
        isUser: false,
        timestamp: new Date()
      };

      setMessages(prev => [...prev, botMessage]);
    } catch (error) {
      showToast('حدث خطأ في الحصول على الرد', 'danger');
    } finally {
      setIsTyping(false);
    }
  };

  const handleQuickAction = (message: string) => {
    setInputText(message);
    setTimeout(() => handleSendMessage(), 200);
  };

  const clearChat = () => {
    setMessages([]);
    setShowWelcome(true);
    showToast('تم مسح المحادثة', 'info');
  };

  const exportChat = () => {
    const chatText = messages.map(msg => 
      `${msg.isUser ? 'أنت' : 'المساعد الطبي'}: ${msg.text}\n`
    ).join('\n');
    
    const blob = new Blob([chatText], { type: 'text/plain;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `medical-chat-${new Date().toISOString().split('T')[0]}.txt`;
    a.click();
    URL.revokeObjectURL(url);
    
    showToast('تم تصدير المحادثة', 'success');
  };

  return (
    <>
      <Container className="py-5">
        <Row className="justify-content-center">
          <Col lg={10}>
            <Card className="shadow-lg border-0 overflow-hidden card-medical" style={{ borderRadius: '25px' }}>
              {/* Chat Header */}
              <Card.Header
                className="text-white p-4 position-relative"
                style={{
                  background: 'var(--medical-gradient-primary)',
                  borderBottom: '3px solid var(--medical-primary-300)'
                }}
              >
                {/* Background pattern */}
                <div
                  className="position-absolute top-0 start-0 w-100 h-100 opacity-10"
                  style={{
                    backgroundImage: 'radial-gradient(circle, white 1px, transparent 1px)',
                    backgroundSize: '20px 20px'
                  }}
                />

                <div className="d-flex align-items-center justify-content-between position-relative">
                  <div className="d-flex align-items-center">
                    <div
                      className="rounded-circle d-flex align-items-center justify-content-center me-3 animate-pulse-medical"
                      style={{
                        width: '60px',
                        height: '60px',
                        background: 'rgba(255, 255, 255, 0.2)',
                        border: '3px solid rgba(255, 255, 255, 0.3)'
                      }}
                    >
                      <FontAwesomeIcon icon={faStethoscope} className="fs-2" />
                    </div>
                    <div>
                      <h4 className="mb-1 fw-bold">المساعد الطبي الذكي</h4>
                      <p className="mb-0 opacity-90">
                        <FontAwesomeIcon icon={faRobot} className="me-2" />
                        مدعوم بالذكاء الاصطناعي المتطور
                      </p>
                    </div>
                  </div>
                  <div className="d-flex gap-2">
                    <Button
                      variant="outline-light"
                      size="sm"
                      onClick={clearChat}
                      title="مسح المحادثة"
                      className="hover-lift rounded-pill"
                    >
                      <FontAwesomeIcon icon={faTrash} />
                    </Button>
                    <Button
                      variant="outline-light"
                      size="sm"
                      onClick={exportChat}
                      title="تصدير المحادثة"
                      disabled={messages.length === 0}
                      className="hover-lift rounded-pill"
                    >
                      <FontAwesomeIcon icon={faDownload} />
                    </Button>
                  </div>
                </div>
              </Card.Header>

              {/* Welcome Section */}
              {showWelcome && (
                <div className="p-5 text-center border-bottom bg-medical-soft animate-fadeIn">
                  <div className="mb-4">
                    <div
                      className="d-inline-flex align-items-center justify-content-center rounded-circle mb-3 animate-pulse-medical"
                      style={{
                        width: '100px',
                        height: '100px',
                        background: 'var(--medical-gradient-primary)',
                        color: 'white'
                      }}
                    >
                      <FontAwesomeIcon icon={faStethoscope} style={{ fontSize: '3rem' }} />
                    </div>
                  </div>

                  <h3 className="h3 mb-3 text-gradient-medical fw-bold">
                    مرحباً بك في المساعد الطبي الذكي
                  </h3>
                  <p className="text-muted mb-4 lead">
                    أنا هنا لمساعدتك في تحليل الأعراض وتقديم الإرشادات الطبية الأولية
                  </p>

                  <div className="mb-4">
                    <small className="text-muted">
                      <FontAwesomeIcon icon={faExclamationTriangle} className="me-2 text-warning" />
                      اختر من الأعراض الشائعة أو اكتب أعراضك بالتفصيل
                    </small>
                  </div>

                  <Row className="g-3 justify-content-center">
                    {quickActions.map((action, index) => (
                      <Col md={3} xs={6} key={index}>
                        <Button
                          variant="outline-primary"
                          className={`w-100 h-100 p-4 hover-lift card-medical animate-scaleIn animate-stagger-${index + 1}`}
                          style={{
                            borderRadius: '20px',
                            border: '2px solid var(--medical-primary-200)',
                            background: 'white',
                            minHeight: '120px'
                          }}
                          onClick={() => handleQuickAction(action.message)}
                        >
                          <div
                            className="rounded-circle d-inline-flex align-items-center justify-content-center mb-3"
                            style={{
                              width: '50px',
                              height: '50px',
                              background: 'var(--medical-primary-50)',
                              color: 'var(--medical-primary-600)'
                            }}
                          >
                            <FontAwesomeIcon icon={action.icon} className="fs-5" />
                          </div>
                          <div className="small fw-medium text-primary">{action.text}</div>
                        </Button>
                      </Col>
                    ))}
                  </Row>

                  <div className="mt-4 pt-3 border-top">
                    <small className="text-muted">
                      💡 نصيحة: كن محدداً في وصف أعراضك للحصول على أفضل مساعدة
                    </small>
                  </div>
                </div>
              )}

              {/* Chat Messages */}
              <div 
                className="p-4" 
                style={{ 
                  minHeight: '400px', 
                  maxHeight: '500px', 
                  overflowY: 'auto' 
                }}
              >
                {messages.map(message => (
                  <ChatMessage key={message.id} message={message} />
                ))}
                {isTyping && <TypingIndicator />}
                <div ref={messagesEndRef} />
              </div>

              {/* Chat Input */}
              <Card.Footer
                className="border-top p-4"
                style={{
                  background: 'var(--medical-primary-50)',
                  borderTop: '2px solid var(--medical-primary-200)'
                }}
              >
                <Form onSubmit={(e) => { e.preventDefault(); handleSendMessage(); }}>
                  <div className="d-flex align-items-end gap-3">
                    <div className="flex-grow-1">
                      <Form.Control
                        as="textarea"
                        rows={1}
                        value={inputText}
                        onChange={(e) => setInputText(e.target.value)}
                        placeholder="اكتب أعراضك بالتفصيل... (مثال: أعاني من صداع شديد منذ ساعتين مع غثيان)"
                        className="form-control-lg"
                        style={{
                          resize: 'none',
                          maxHeight: '120px',
                          borderRadius: '25px',
                          border: '2px solid var(--medical-primary-200)',
                          background: 'white',
                          fontSize: '1rem',
                          padding: '15px 20px'
                        }}
                        onKeyPress={(e) => {
                          if (e.key === 'Enter' && !e.shiftKey) {
                            e.preventDefault();
                            handleSendMessage();
                          }
                        }}
                      />
                    </div>
                    <Button
                      type="submit"
                      variant="primary"
                      disabled={!inputText.trim() || isTyping}
                      className="hover-lift animate-pulse-medical"
                      style={{
                        borderRadius: '50%',
                        width: '60px',
                        height: '60px',
                        background: 'var(--medical-gradient-primary)',
                        border: 'none',
                        boxShadow: 'var(--medical-shadow-medium)'
                      }}
                    >
                      <FontAwesomeIcon
                        icon={faPaperPlane}
                        className={isTyping ? 'animate-pulse' : ''}
                      />
                    </Button>
                  </div>

                  <div className="mt-3 d-flex justify-content-between align-items-center">
                    <small className="text-muted">
                      <FontAwesomeIcon icon={faExclamationTriangle} className="me-2 text-warning" />
                      هذا المساعد لا يغني عن استشارة الطبيب المختص
                    </small>
                    <small className="text-muted">
                      {inputText.length > 0 && (
                        <span className="text-primary">
                          {inputText.length}/500 حرف
                        </span>
                      )}
                    </small>
                  </div>

                  {/* Quick suggestions when typing */}
                  {inputText.length > 0 && (
                    <div className="mt-2">
                      <small className="text-muted d-block mb-2">اقتراحات سريعة:</small>
                      <div className="d-flex gap-2 flex-wrap">
                        {['منذ متى بدأت الأعراض؟', 'هل تتناول أي أدوية؟', 'هل لديك تاريخ مرضي؟'].map((suggestion, index) => (
                          <Button
                            key={index}
                            variant="outline-primary"
                            size="sm"
                            className="rounded-pill"
                            onClick={() => setInputText(inputText + ' ' + suggestion)}
                          >
                            {suggestion}
                          </Button>
                        ))}
                      </div>
                    </div>
                  )}
                </Form>
              </Card.Footer>
            </Card>

            {/* Tips Card */}
            <Card className="border-0 shadow-sm mt-4" style={{ borderRadius: '20px' }}>
              <Card.Body className="p-4">
                <h5 className="text-primary fw-bold mb-3">
                  <FontAwesomeIcon icon={faStethoscope} className="me-2" />
                  نصائح للحصول على أفضل مساعدة:
                </h5>
                <Row>
                  <Col md={6}>
                    <ul className="list-unstyled">
                      <li className="mb-2">
                        <FontAwesomeIcon icon={faExclamationTriangle} className="text-success me-2" />
                        كن محدداً في وصف أعراضك
                      </li>
                      <li className="mb-2">
                        <FontAwesomeIcon icon={faExclamationTriangle} className="text-success me-2" />
                        اذكر متى بدأت الأعراض
                      </li>
                    </ul>
                  </Col>
                  <Col md={6}>
                    <ul className="list-unstyled">
                      <li className="mb-2">
                        <FontAwesomeIcon icon={faExclamationTriangle} className="text-success me-2" />
                        اذكر أي تاريخ طبي ذي صلة
                      </li>
                      <li className="mb-2">
                        <FontAwesomeIcon icon={faExclamationTriangle} className="text-success me-2" />
                        صف شدة الأعراض
                      </li>
                    </ul>
                  </Col>
                </Row>
              </Card.Body>
            </Card>
          </Col>
        </Row>
      </Container>

      {/* Emergency Modal */}
      <Modal show={showEmergencyModal} onHide={() => setShowEmergencyModal(false)} centered>
        <Modal.Header closeButton className="bg-danger text-white">
          <Modal.Title>
            <FontAwesomeIcon icon={faAmbulance} className="me-2" />
            حالة طوارئ؟
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <p className="mb-3">إذا كانت هذه حالة طوارئ طبية، يرجى الاتصال فوراً بـ:</p>
          <Row className="g-3">
            <Col xs={6}>
              <div className="d-flex align-items-center p-3 bg-light rounded">
                <FontAwesomeIcon icon={faPhone} className="text-danger fs-4 me-3" />
                <div>
                  <strong>الإسعاف</strong>
                  <div className="text-danger fw-bold">123</div>
                </div>
              </div>
            </Col>
            <Col xs={6}>
              <div className="d-flex align-items-center p-3 bg-light rounded">
                <FontAwesomeIcon icon={faHospital} className="text-danger fs-4 me-3" />
                <div>
                  <strong>الطوارئ</strong>
                  <div className="text-danger fw-bold">16123</div>
                </div>
              </div>
            </Col>
          </Row>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="primary" onClick={() => setShowEmergencyModal(false)}>
            متابعة المحادثة
          </Button>
        </Modal.Footer>
      </Modal>
    </>
  );
};

export default Diagnosis;
