{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive - MOE Student S4M\\\\Desktop\\\\test 3\\\\medical-assistant-react\\\\src\\\\components\\\\Auth\\\\AuthModal.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Modal, Tab, Tabs } from 'react-bootstrap';\nimport LoginForm from './LoginForm';\nimport RegisterForm from './RegisterForm';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthModal = ({\n  show,\n  onHide,\n  defaultTab = 'login'\n}) => {\n  _s();\n  const [activeTab, setActiveTab] = useState(defaultTab);\n  const handleClose = () => {\n    onHide();\n    // Reset to login tab when modal closes\n    setTimeout(() => setActiveTab('login'), 300);\n  };\n  return /*#__PURE__*/_jsxDEV(Modal, {\n    show: show,\n    onHide: handleClose,\n    centered: true,\n    size: \"lg\",\n    children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n      closeButton: true,\n      className: \"border-0 pb-0\",\n      children: /*#__PURE__*/_jsxDEV(Tabs, {\n        activeKey: activeTab,\n        onSelect: k => setActiveTab(k),\n        className: \"nav-fill w-100\",\n        id: \"auth-tabs\",\n        children: [/*#__PURE__*/_jsxDEV(Tab, {\n          eventKey: \"login\",\n          title: \"\\u062A\\u0633\\u062C\\u064A\\u0644 \\u0627\\u0644\\u062F\\u062E\\u0648\\u0644\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 35,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tab, {\n          eventKey: \"register\",\n          title: \"\\u0625\\u0646\\u0634\\u0627\\u0621 \\u062D\\u0633\\u0627\\u0628\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n      className: \"pt-0\",\n      children: activeTab === 'login' ? /*#__PURE__*/_jsxDEV(LoginForm, {\n        onSuccess: handleClose\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(RegisterForm, {\n        onSuccess: handleClose\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 40,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 22,\n    columnNumber: 5\n  }, this);\n};\n_s(AuthModal, \"9WmCEOvw9OrQO0Lak90Gk/ssVsU=\");\n_c = AuthModal;\nexport default AuthModal;\nvar _c;\n$RefreshReg$(_c, \"AuthModal\");", "map": {"version": 3, "names": ["React", "useState", "Modal", "Tab", "Tabs", "LoginForm", "RegisterForm", "jsxDEV", "_jsxDEV", "AuthModal", "show", "onHide", "defaultTab", "_s", "activeTab", "setActiveTab", "handleClose", "setTimeout", "centered", "size", "children", "Header", "closeButton", "className", "active<PERSON><PERSON>", "onSelect", "k", "id", "eventKey", "title", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Body", "onSuccess", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/test 3/medical-assistant-react/src/components/Auth/AuthModal.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Modal, Tab, Tabs } from 'react-bootstrap';\nimport LoginForm from './LoginForm';\nimport RegisterForm from './RegisterForm';\n\ninterface AuthModalProps {\n  show: boolean;\n  onHide: () => void;\n  defaultTab?: 'login' | 'register';\n}\n\nconst AuthModal: React.FC<AuthModalProps> = ({ show, onHide, defaultTab = 'login' }) => {\n  const [activeTab, setActiveTab] = useState(defaultTab);\n\n  const handleClose = () => {\n    onHide();\n    // Reset to login tab when modal closes\n    setTimeout(() => setActiveTab('login'), 300);\n  };\n\n  return (\n    <Modal\n      show={show}\n      onHide={handleClose}\n      centered\n      size=\"lg\"\n    >\n      <Modal.Header closeButton className=\"border-0 pb-0\">\n        <Tabs\n          activeKey={activeTab}\n          onSelect={(k) => setActiveTab(k as 'login' | 'register')}\n          className=\"nav-fill w-100\"\n          id=\"auth-tabs\"\n        >\n          <Tab eventKey=\"login\" title=\"تسجيل الدخول\" />\n          <Tab eventKey=\"register\" title=\"إنشاء حساب\" />\n        </Tabs>\n      </Modal.Header>\n      \n      <Modal.Body className=\"pt-0\">\n        {activeTab === 'login' ? (\n          <LoginForm onSuccess={handleClose} />\n        ) : (\n          <RegisterForm onSuccess={handleClose} />\n        )}\n      </Modal.Body>\n    </Modal>\n  );\n};\n\nexport default AuthModal;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,KAAK,EAAEC,GAAG,EAAEC,IAAI,QAAQ,iBAAiB;AAClD,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAOC,YAAY,MAAM,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAQ1C,MAAMC,SAAmC,GAAGA,CAAC;EAAEC,IAAI;EAAEC,MAAM;EAAEC,UAAU,GAAG;AAAQ,CAAC,KAAK;EAAAC,EAAA;EACtF,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGd,QAAQ,CAACW,UAAU,CAAC;EAEtD,MAAMI,WAAW,GAAGA,CAAA,KAAM;IACxBL,MAAM,CAAC,CAAC;IACR;IACAM,UAAU,CAAC,MAAMF,YAAY,CAAC,OAAO,CAAC,EAAE,GAAG,CAAC;EAC9C,CAAC;EAED,oBACEP,OAAA,CAACN,KAAK;IACJQ,IAAI,EAAEA,IAAK;IACXC,MAAM,EAAEK,WAAY;IACpBE,QAAQ;IACRC,IAAI,EAAC,IAAI;IAAAC,QAAA,gBAETZ,OAAA,CAACN,KAAK,CAACmB,MAAM;MAACC,WAAW;MAACC,SAAS,EAAC,eAAe;MAAAH,QAAA,eACjDZ,OAAA,CAACJ,IAAI;QACHoB,SAAS,EAAEV,SAAU;QACrBW,QAAQ,EAAGC,CAAC,IAAKX,YAAY,CAACW,CAAyB,CAAE;QACzDH,SAAS,EAAC,gBAAgB;QAC1BI,EAAE,EAAC,WAAW;QAAAP,QAAA,gBAEdZ,OAAA,CAACL,GAAG;UAACyB,QAAQ,EAAC,OAAO;UAACC,KAAK,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC7CzB,OAAA,CAACL,GAAG;UAACyB,QAAQ,EAAC,UAAU;UAACC,KAAK,EAAC;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1C;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC,eAEfzB,OAAA,CAACN,KAAK,CAACgC,IAAI;MAACX,SAAS,EAAC,MAAM;MAAAH,QAAA,EACzBN,SAAS,KAAK,OAAO,gBACpBN,OAAA,CAACH,SAAS;QAAC8B,SAAS,EAAEnB;MAAY;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,gBAErCzB,OAAA,CAACF,YAAY;QAAC6B,SAAS,EAAEnB;MAAY;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IACxC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACS,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR,CAAC;AAEZ,CAAC;AAACpB,EAAA,CArCIJ,SAAmC;AAAA2B,EAAA,GAAnC3B,SAAmC;AAuCzC,eAAeA,SAAS;AAAC,IAAA2B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}