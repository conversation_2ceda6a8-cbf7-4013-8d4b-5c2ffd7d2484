{"ast": null, "code": "import * as React from 'react';\nimport { useId, useRef, useInsertionEffect } from 'react';\n\n/**\n * Measurement functionality has to be within a separate component\n * to leverage snapshot lifecycle.\n */\nclass PopChildMeasure extends React.Component {\n  getSnapshotBeforeUpdate(prevProps) {\n    const element = this.props.childRef.current;\n    if (element && prevProps.isPresent && !this.props.isPresent) {\n      const size = this.props.sizeRef.current;\n      size.height = element.offsetHeight || 0;\n      size.width = element.offsetWidth || 0;\n      size.top = element.offsetTop;\n      size.left = element.offsetLeft;\n    }\n    return null;\n  }\n  /**\n   * Required with getSnapshotBeforeUpdate to stop React complaining.\n   */\n  componentDidUpdate() {}\n  render() {\n    return this.props.children;\n  }\n}\nfunction PopChild(_ref) {\n  let {\n    children,\n    isPresent\n  } = _ref;\n  const id = useId();\n  const ref = useRef(null);\n  const size = useRef({\n    width: 0,\n    height: 0,\n    top: 0,\n    left: 0\n  });\n  /**\n   * We create and inject a style block so we can apply this explicit\n   * sizing in a non-destructive manner by just deleting the style block.\n   *\n   * We can't apply size via render as the measurement happens\n   * in getSnapshotBeforeUpdate (post-render), likewise if we apply the\n   * styles directly on the DOM node, we might be overwriting\n   * styles set via the style prop.\n   */\n  useInsertionEffect(() => {\n    const {\n      width,\n      height,\n      top,\n      left\n    } = size.current;\n    if (isPresent || !ref.current || !width || !height) return;\n    ref.current.dataset.motionPopId = id;\n    const style = document.createElement(\"style\");\n    document.head.appendChild(style);\n    if (style.sheet) {\n      style.sheet.insertRule(\"\\n          [data-motion-pop-id=\\\"\".concat(id, \"\\\"] {\\n            position: absolute !important;\\n            width: \").concat(width, \"px !important;\\n            height: \").concat(height, \"px !important;\\n            top: \").concat(top, \"px !important;\\n            left: \").concat(left, \"px !important;\\n          }\\n        \"));\n    }\n    return () => {\n      document.head.removeChild(style);\n    };\n  }, [isPresent]);\n  return React.createElement(PopChildMeasure, {\n    isPresent: isPresent,\n    childRef: ref,\n    sizeRef: size\n  }, React.cloneElement(children, {\n    ref\n  }));\n}\nexport { PopChild };", "map": {"version": 3, "names": ["React", "useId", "useRef", "useInsertionEffect", "PopChildMeasure", "Component", "getSnapshotBeforeUpdate", "prevProps", "element", "props", "childRef", "current", "isPresent", "size", "sizeRef", "height", "offsetHeight", "width", "offsetWidth", "top", "offsetTop", "left", "offsetLeft", "componentDidUpdate", "render", "children", "PopChild", "_ref", "id", "ref", "dataset", "motionPopId", "style", "document", "createElement", "head", "append<PERSON><PERSON><PERSON>", "sheet", "insertRule", "concat", "<PERSON><PERSON><PERSON><PERSON>", "cloneElement"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/test 3/medical-assistant-react/node_modules/framer-motion/dist/es/components/AnimatePresence/PopChild.mjs"], "sourcesContent": ["import * as React from 'react';\nimport { useId, useRef, useInsertionEffect } from 'react';\n\n/**\n * Measurement functionality has to be within a separate component\n * to leverage snapshot lifecycle.\n */\nclass PopChildMeasure extends React.Component {\n    getSnapshotBeforeUpdate(prevProps) {\n        const element = this.props.childRef.current;\n        if (element && prevProps.isPresent && !this.props.isPresent) {\n            const size = this.props.sizeRef.current;\n            size.height = element.offsetHeight || 0;\n            size.width = element.offsetWidth || 0;\n            size.top = element.offsetTop;\n            size.left = element.offsetLeft;\n        }\n        return null;\n    }\n    /**\n     * Required with getSnapshotBeforeUpdate to stop React complaining.\n     */\n    componentDidUpdate() { }\n    render() {\n        return this.props.children;\n    }\n}\nfunction PopChild({ children, isPresent }) {\n    const id = useId();\n    const ref = useRef(null);\n    const size = useRef({\n        width: 0,\n        height: 0,\n        top: 0,\n        left: 0,\n    });\n    /**\n     * We create and inject a style block so we can apply this explicit\n     * sizing in a non-destructive manner by just deleting the style block.\n     *\n     * We can't apply size via render as the measurement happens\n     * in getSnapshotBeforeUpdate (post-render), likewise if we apply the\n     * styles directly on the DOM node, we might be overwriting\n     * styles set via the style prop.\n     */\n    useInsertionEffect(() => {\n        const { width, height, top, left } = size.current;\n        if (isPresent || !ref.current || !width || !height)\n            return;\n        ref.current.dataset.motionPopId = id;\n        const style = document.createElement(\"style\");\n        document.head.appendChild(style);\n        if (style.sheet) {\n            style.sheet.insertRule(`\n          [data-motion-pop-id=\"${id}\"] {\n            position: absolute !important;\n            width: ${width}px !important;\n            height: ${height}px !important;\n            top: ${top}px !important;\n            left: ${left}px !important;\n          }\n        `);\n        }\n        return () => {\n            document.head.removeChild(style);\n        };\n    }, [isPresent]);\n    return (React.createElement(PopChildMeasure, { isPresent: isPresent, childRef: ref, sizeRef: size }, React.cloneElement(children, { ref })));\n}\n\nexport { PopChild };\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,KAAK,EAAEC,MAAM,EAAEC,kBAAkB,QAAQ,OAAO;;AAEzD;AACA;AACA;AACA;AACA,MAAMC,eAAe,SAASJ,KAAK,CAACK,SAAS,CAAC;EAC1CC,uBAAuBA,CAACC,SAAS,EAAE;IAC/B,MAAMC,OAAO,GAAG,IAAI,CAACC,KAAK,CAACC,QAAQ,CAACC,OAAO;IAC3C,IAAIH,OAAO,IAAID,SAAS,CAACK,SAAS,IAAI,CAAC,IAAI,CAACH,KAAK,CAACG,SAAS,EAAE;MACzD,MAAMC,IAAI,GAAG,IAAI,CAACJ,KAAK,CAACK,OAAO,CAACH,OAAO;MACvCE,IAAI,CAACE,MAAM,GAAGP,OAAO,CAACQ,YAAY,IAAI,CAAC;MACvCH,IAAI,CAACI,KAAK,GAAGT,OAAO,CAACU,WAAW,IAAI,CAAC;MACrCL,IAAI,CAACM,GAAG,GAAGX,OAAO,CAACY,SAAS;MAC5BP,IAAI,CAACQ,IAAI,GAAGb,OAAO,CAACc,UAAU;IAClC;IACA,OAAO,IAAI;EACf;EACA;AACJ;AACA;EACIC,kBAAkBA,CAAA,EAAG,CAAE;EACvBC,MAAMA,CAAA,EAAG;IACL,OAAO,IAAI,CAACf,KAAK,CAACgB,QAAQ;EAC9B;AACJ;AACA,SAASC,QAAQA,CAAAC,IAAA,EAA0B;EAAA,IAAzB;IAAEF,QAAQ;IAAEb;EAAU,CAAC,GAAAe,IAAA;EACrC,MAAMC,EAAE,GAAG3B,KAAK,CAAC,CAAC;EAClB,MAAM4B,GAAG,GAAG3B,MAAM,CAAC,IAAI,CAAC;EACxB,MAAMW,IAAI,GAAGX,MAAM,CAAC;IAChBe,KAAK,EAAE,CAAC;IACRF,MAAM,EAAE,CAAC;IACTI,GAAG,EAAE,CAAC;IACNE,IAAI,EAAE;EACV,CAAC,CAAC;EACF;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIlB,kBAAkB,CAAC,MAAM;IACrB,MAAM;MAAEc,KAAK;MAAEF,MAAM;MAAEI,GAAG;MAAEE;IAAK,CAAC,GAAGR,IAAI,CAACF,OAAO;IACjD,IAAIC,SAAS,IAAI,CAACiB,GAAG,CAAClB,OAAO,IAAI,CAACM,KAAK,IAAI,CAACF,MAAM,EAC9C;IACJc,GAAG,CAAClB,OAAO,CAACmB,OAAO,CAACC,WAAW,GAAGH,EAAE;IACpC,MAAMI,KAAK,GAAGC,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC;IAC7CD,QAAQ,CAACE,IAAI,CAACC,WAAW,CAACJ,KAAK,CAAC;IAChC,IAAIA,KAAK,CAACK,KAAK,EAAE;MACbL,KAAK,CAACK,KAAK,CAACC,UAAU,sCAAAC,MAAA,CACDX,EAAE,4EAAAW,MAAA,CAEdtB,KAAK,0CAAAsB,MAAA,CACJxB,MAAM,uCAAAwB,MAAA,CACTpB,GAAG,wCAAAoB,MAAA,CACFlB,IAAI,0CAEf,CAAC;IACF;IACA,OAAO,MAAM;MACTY,QAAQ,CAACE,IAAI,CAACK,WAAW,CAACR,KAAK,CAAC;IACpC,CAAC;EACL,CAAC,EAAE,CAACpB,SAAS,CAAC,CAAC;EACf,OAAQZ,KAAK,CAACkC,aAAa,CAAC9B,eAAe,EAAE;IAAEQ,SAAS,EAAEA,SAAS;IAAEF,QAAQ,EAAEmB,GAAG;IAAEf,OAAO,EAAED;EAAK,CAAC,EAAEb,KAAK,CAACyC,YAAY,CAAChB,QAAQ,EAAE;IAAEI;EAAI,CAAC,CAAC,CAAC;AAC/I;AAEA,SAASH,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}