{"ast": null, "code": "/**\n * Check if QR Code version is valid\n *\n * @param  {Number}  version QR Code version\n * @return {Boolean}         true if valid version, false otherwise\n */\nexports.isValid = function isValid(version) {\n  return !isNaN(version) && version >= 1 && version <= 40;\n};", "map": {"version": 3, "names": ["exports", "<PERSON><PERSON><PERSON><PERSON>", "version", "isNaN"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/test 3/medical-assistant-react/node_modules/qrcode/lib/core/version-check.js"], "sourcesContent": ["/**\n * Check if QR Code version is valid\n *\n * @param  {Number}  version QR Code version\n * @return {Boolean}         true if valid version, false otherwise\n */\nexports.isValid = function isValid (version) {\n  return !isNaN(version) && version >= 1 && version <= 40\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACAA,OAAO,CAACC,OAAO,GAAG,SAASA,OAAOA,CAAEC,OAAO,EAAE;EAC3C,OAAO,CAACC,KAAK,CAACD,OAAO,CAAC,IAAIA,OAAO,IAAI,CAAC,IAAIA,OAAO,IAAI,EAAE;AACzD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}