{"ast": null, "code": "export const ATTRIBUTE_PREFIX = \"data-rr-ui-\";\nexport const PROPERTY_PREFIX = \"rrUi\";\nexport function dataAttr(property) {\n  return \"\".concat(ATTRIBUTE_PREFIX).concat(property);\n}\nexport function dataProp(property) {\n  return \"\".concat(PROPERTY_PREFIX).concat(property);\n}", "map": {"version": 3, "names": ["ATTRIBUTE_PREFIX", "PROPERTY_PREFIX", "dataAttr", "property", "concat", "dataProp"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/test 3/medical-assistant-react/node_modules/@restart/ui/esm/DataKey.js"], "sourcesContent": ["export const ATTRIBUTE_PREFIX = `data-rr-ui-`;\nexport const PROPERTY_PREFIX = `rrUi`;\nexport function dataAttr(property) {\n  return `${ATTRIBUTE_PREFIX}${property}`;\n}\nexport function dataProp(property) {\n  return `${PROPERTY_PREFIX}${property}`;\n}"], "mappings": "AAAA,OAAO,MAAMA,gBAAgB,gBAAgB;AAC7C,OAAO,MAAMC,eAAe,SAAS;AACrC,OAAO,SAASC,QAAQA,CAACC,QAAQ,EAAE;EACjC,UAAAC,MAAA,CAAUJ,gBAAgB,EAAAI,MAAA,CAAGD,QAAQ;AACvC;AACA,OAAO,SAASE,QAAQA,CAACF,QAAQ,EAAE;EACjC,UAAAC,MAAA,CAAUH,eAAe,EAAAG,MAAA,CAAGD,QAAQ;AACtC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}