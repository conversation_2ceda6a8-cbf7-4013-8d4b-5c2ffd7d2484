import React from 'react';
import { Container, Row, Col, But<PERSON> } from 'react-bootstrap';
import { <PERSON> } from 'react-router-dom';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faStethoscope, faCalendarPlus } from '@fortawesome/free-solid-svg-icons';

const HeroSection: React.FC = () => {
  return (
    <section className="hero py-5 py-md-6">
      <Container>
        <Row className="align-items-center">
          <Col lg={6} className="mb-5 mb-lg-0">
            <div className="hero-content animate-fadeIn">
              <h1 className="display-4 fw-bold mb-4">مساعدك الطبي الذكي</h1>
              <p className="lead mb-4">
                تحليل ذكي للأعراض باستخدام الذكاء الاصطناعي ومعالجة اللغة الطبيعية 
                للحصول على تشخيص أولي دقيق وسريع
              </p>
              <div className="d-flex flex-column flex-sm-row gap-3">
                <Link to="/diagnosis" className="text-decoration-none">
                  <Button
                    variant="primary"
                    size="lg"
                    className="d-flex align-items-center justify-content-center w-100"
                  >
                    <FontAwesomeIcon icon={faStethoscope} className="me-2" />
                    ابدأ التشخيص
                  </Button>
                </Link>
                <Link to="/appointments" className="text-decoration-none">
                  <Button
                    variant="outline-primary"
                    size="lg"
                    className="d-flex align-items-center justify-content-center w-100"
                  >
                    <FontAwesomeIcon icon={faCalendarPlus} className="me-2" />
                    احجز موعد
                  </Button>
                </Link>
              </div>
            </div>
          </Col>
          <Col lg={6} className="d-none d-lg-block">
            <div className="hero-image-container position-relative animate-slideInUp">
              <img 
                src="/images/medical-banner-with-doctor-wearing-goggles.jpg" 
                className="img-fluid rounded-4 shadow-lg" 
                alt="Doctor using medical technology"
                onError={(e) => {
                  // Fallback to a placeholder if image doesn't exist
                  e.currentTarget.src = 'https://via.placeholder.com/600x400/007bff/ffffff?text=Medical+Assistant';
                }}
              />
            </div>
          </Col>
        </Row>
      </Container>
    </section>
  );
};

export default HeroSection;
