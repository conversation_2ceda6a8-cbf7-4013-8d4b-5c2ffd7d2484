import React, { useEffect, useState } from 'react';
import { Con<PERSON><PERSON>, <PERSON>, Col, Button, Badge } from 'react-bootstrap';
import { Link } from 'react-router-dom';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faStethoscope,
  faCalendarPlus,
  faUserMd,
  faHeartbeat,
  faClock,
  faShieldAlt,
  faAward,
  faUsers
} from '@fortawesome/free-solid-svg-icons';

const HeroSection: React.FC = () => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    setIsVisible(true);
  }, []);

  const stats = [
    { icon: faUserMd, value: '500+', label: 'أطباء متخصصون' },
    { icon: faUsers, value: '10,000+', label: 'مريض راضٍ' },
    { icon: faAward, value: '98%', label: 'دقة التشخيص' },
    { icon: faClock, value: '24/7', label: 'خدمة متواصلة' }
  ];

  return (
    <section className="hero py-5 py-md-6 bg-medical-pattern position-relative overflow-hidden">
      {/* Background Elements */}
      <div className="position-absolute top-0 start-0 w-100 h-100">
        <div className="position-absolute animate-float" style={{
          top: '10%',
          right: '10%',
          width: '60px',
          height: '60px',
          background: 'var(--medical-primary-200)',
          borderRadius: '50%',
          opacity: 0.3
        }}></div>
        <div className="position-absolute animate-float" style={{
          top: '60%',
          left: '5%',
          width: '40px',
          height: '40px',
          background: 'var(--medical-accent-200)',
          borderRadius: '50%',
          opacity: 0.3,
          animationDelay: '1s'
        }}></div>
        <div className="position-absolute animate-float" style={{
          top: '30%',
          left: '15%',
          width: '80px',
          height: '80px',
          background: 'var(--medical-secondary-200)',
          borderRadius: '50%',
          opacity: 0.2,
          animationDelay: '2s'
        }}></div>
      </div>

      <Container className="position-relative">
        <Row className="align-items-center min-vh-75">
          <Col lg={6} className="mb-5 mb-lg-0">
            <div className={`hero-content ${isVisible ? 'animate-slideInLeft' : ''}`}>
              {/* Trust Badge */}
              <div className="mb-4">
                <Badge bg="light" text="dark" className="px-3 py-2 rounded-pill border border-primary-200">
                  <FontAwesomeIcon icon={faShieldAlt} className="me-2 text-primary" />
                  معتمد من وزارة الصحة
                </Badge>
              </div>

              <h1 className="display-3 fw-bold mb-4 text-gradient-medical">
                مساعدك الطبي الذكي
              </h1>

              <h2 className="h4 text-muted mb-4 fw-normal">
                تحليل ذكي للأعراض باستخدام الذكاء الاصطناعي
              </h2>

              <p className="lead mb-5 text-gray-600">
                احصل على تشخيص أولي دقيق وسريع من خلال تقنيات الذكاء الاصطناعي المتطورة
                ومعالجة اللغة الطبيعية، مع إمكانية حجز مواعيد فورية مع أفضل الأطباء المتخصصين
              </p>

              <div className="d-flex flex-column flex-sm-row gap-3 mb-5">
                <Link to="/diagnosis" className="text-decoration-none">
                  <Button
                    variant="primary"
                    size="lg"
                    className="d-flex align-items-center justify-content-center w-100 hover-lift animate-pulse-medical"
                  >
                    <FontAwesomeIcon icon={faStethoscope} className="me-2" />
                    ابدأ التشخيص الآن
                  </Button>
                </Link>
                <Link to="/appointments" className="text-decoration-none">
                  <Button
                    variant="outline-primary"
                    size="lg"
                    className="d-flex align-items-center justify-content-center w-100 hover-lift"
                  >
                    <FontAwesomeIcon icon={faCalendarPlus} className="me-2" />
                    احجز موعد طبي
                  </Button>
                </Link>
              </div>

              {/* Stats Section */}
              <Row className="g-3">
                {stats.map((stat, index) => (
                  <Col xs={6} md={3} key={index}>
                    <div className={`text-center animate-scaleIn animate-stagger-${index + 1}`}>
                      <div className="bg-white rounded-3 p-3 shadow-sm hover-lift">
                        <FontAwesomeIcon
                          icon={stat.icon}
                          className="text-primary mb-2"
                          size="lg"
                        />
                        <div className="fw-bold text-primary h5 mb-1">{stat.value}</div>
                        <small className="text-muted">{stat.label}</small>
                      </div>
                    </div>
                  </Col>
                ))}
              </Row>
            </div>
          </Col>

          <Col lg={6} className="d-none d-lg-block">
            <div className={`hero-image-container position-relative ${isVisible ? 'animate-slideInRight' : ''}`}>
              {/* Main Image */}
              <div className="position-relative">
                <img
                  src="/images/AdobeStock_305412791_Preview.jpeg"
                  className="img-fluid rounded-medical shadow-lg hover-glow"
                  alt="Professional medical consultation"
                  style={{ maxHeight: '500px', objectFit: 'cover', width: '100%' }}
                  onError={(e) => {
                    e.currentTarget.src = '/images/medical-banner-with-doctor-wearing-goggles.jpg';
                  }}
                />

                {/* Floating Elements */}
                <div className="position-absolute top-0 end-0 translate-middle">
                  <div className="bg-white rounded-circle p-3 shadow-lg animate-pulse-medical">
                    <FontAwesomeIcon icon={faHeartbeat} className="text-danger" size="lg" />
                  </div>
                </div>

                <div className="position-absolute bottom-0 start-0 translate-middle">
                  <div className="bg-white rounded-3 p-3 shadow-lg">
                    <div className="d-flex align-items-center">
                      <div className="bg-success rounded-circle me-2" style={{width: '8px', height: '8px'}}></div>
                      <small className="text-muted">متاح الآن</small>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </Col>
        </Row>
      </Container>
    </section>
  );
};

export default HeroSection;
