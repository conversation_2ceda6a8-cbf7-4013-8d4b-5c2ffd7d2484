import React, { useState } from 'react';
import { Modal, Tab, Tabs } from 'react-bootstrap';
import LoginForm from './LoginForm';
import RegisterForm from './RegisterForm';

interface AuthModalProps {
  show: boolean;
  onHide: () => void;
  defaultTab?: 'login' | 'register';
}

const AuthModal: React.FC<AuthModalProps> = ({ show, onHide, defaultTab = 'login' }) => {
  const [activeTab, setActiveTab] = useState(defaultTab);

  const handleClose = () => {
    onHide();
    // Reset to login tab when modal closes
    setTimeout(() => setActiveTab('login'), 300);
  };

  return (
    <Modal
      show={show}
      onHide={handleClose}
      centered
      size="lg"
    >
      <Modal.Header closeButton className="border-0 pb-0">
        <Tabs
          activeKey={activeTab}
          onSelect={(k) => setActiveTab(k as 'login' | 'register')}
          className="nav-fill w-100"
          id="auth-tabs"
        >
          <Tab eventKey="login" title="تسجيل الدخول" />
          <Tab eventKey="register" title="إنشاء حساب" />
        </Tabs>
      </Modal.Header>
      
      <Modal.Body className="pt-0">
        {activeTab === 'login' ? (
          <LoginForm onSuccess={handleClose} />
        ) : (
          <RegisterForm onSuccess={handleClose} />
        )}
      </Modal.Body>
    </Modal>
  );
};

export default AuthModal;
