{"ast": null, "code": "import canUseDOM from './canUseDOM';\nvar size;\nexport default function scrollbarSize(recalc) {\n  if (!size && size !== 0 || recalc) {\n    if (canUseDOM) {\n      var scrollDiv = document.createElement('div');\n      scrollDiv.style.position = 'absolute';\n      scrollDiv.style.top = '-9999px';\n      scrollDiv.style.width = '50px';\n      scrollDiv.style.height = '50px';\n      scrollDiv.style.overflow = 'scroll';\n      document.body.appendChild(scrollDiv);\n      size = scrollDiv.offsetWidth - scrollDiv.clientWidth;\n      document.body.removeChild(scrollDiv);\n    }\n  }\n  return size;\n}", "map": {"version": 3, "names": ["canUseDOM", "size", "scrollbarSize", "recalc", "scrollDiv", "document", "createElement", "style", "position", "top", "width", "height", "overflow", "body", "append<PERSON><PERSON><PERSON>", "offsetWidth", "clientWidth", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/test 3/medical-assistant-react/node_modules/dom-helpers/esm/scrollbarSize.js"], "sourcesContent": ["import canUseDOM from './canUseDOM';\nvar size;\nexport default function scrollbarSize(recalc) {\n  if (!size && size !== 0 || recalc) {\n    if (canUseDOM) {\n      var scrollDiv = document.createElement('div');\n      scrollDiv.style.position = 'absolute';\n      scrollDiv.style.top = '-9999px';\n      scrollDiv.style.width = '50px';\n      scrollDiv.style.height = '50px';\n      scrollDiv.style.overflow = 'scroll';\n      document.body.appendChild(scrollDiv);\n      size = scrollDiv.offsetWidth - scrollDiv.clientWidth;\n      document.body.removeChild(scrollDiv);\n    }\n  }\n\n  return size;\n}"], "mappings": "AAAA,OAAOA,SAAS,MAAM,aAAa;AACnC,IAAIC,IAAI;AACR,eAAe,SAASC,aAAaA,CAACC,MAAM,EAAE;EAC5C,IAAI,CAACF,IAAI,IAAIA,IAAI,KAAK,CAAC,IAAIE,MAAM,EAAE;IACjC,IAAIH,SAAS,EAAE;MACb,IAAII,SAAS,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MAC7CF,SAAS,CAACG,KAAK,CAACC,QAAQ,GAAG,UAAU;MACrCJ,SAAS,CAACG,KAAK,CAACE,GAAG,GAAG,SAAS;MAC/BL,SAAS,CAACG,KAAK,CAACG,KAAK,GAAG,MAAM;MAC9BN,SAAS,CAACG,KAAK,CAACI,MAAM,GAAG,MAAM;MAC/BP,SAAS,CAACG,KAAK,CAACK,QAAQ,GAAG,QAAQ;MACnCP,QAAQ,CAACQ,IAAI,CAACC,WAAW,CAACV,SAAS,CAAC;MACpCH,IAAI,GAAGG,SAAS,CAACW,WAAW,GAAGX,SAAS,CAACY,WAAW;MACpDX,QAAQ,CAACQ,IAAI,CAACI,WAAW,CAACb,SAAS,CAAC;IACtC;EACF;EAEA,OAAOH,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}