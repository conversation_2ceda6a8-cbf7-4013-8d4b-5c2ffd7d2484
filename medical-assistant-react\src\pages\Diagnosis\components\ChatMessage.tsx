import React, { useState, useEffect } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faUser, faRobot, faStethoscope, faCheckCircle } from '@fortawesome/free-solid-svg-icons';

interface Message {
  id: string;
  text: string;
  isUser: boolean;
  timestamp: Date;
}

interface ChatMessageProps {
  message: Message;
}

const ChatMessage: React.FC<ChatMessageProps> = ({ message }) => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => setIsVisible(true), 100);
    return () => clearTimeout(timer);
  }, []);

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('ar-SA', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    });
  };

  return (
    <div
      className={`d-flex mb-4 ${message.isUser ? 'flex-row-reverse' : ''} ${
        isVisible ? 'animate-slideInUp' : ''
      }`}
      style={{ animationDelay: '0.1s' }}
    >
      {/* Avatar */}
      <div
        className={`rounded-circle d-flex align-items-center justify-content-center text-white me-3 hover-scale ${
          message.isUser ? 'ms-3 me-0' : ''
        }`}
        style={{
          width: '45px',
          height: '45px',
          background: message.isUser
            ? 'var(--medical-gradient-primary)'
            : 'var(--medical-gradient-secondary)',
          flexShrink: 0,
          boxShadow: message.isUser
            ? 'var(--medical-shadow-soft)'
            : '0 4px 15px rgba(34, 197, 94, 0.3)',
          border: '3px solid white'
        }}
      >
        <FontAwesomeIcon
          icon={message.isUser ? faUser : faStethoscope}
          className={message.isUser ? '' : 'animate-pulse-medical'}
        />
      </div>

      {/* Message Content */}
      <div style={{ maxWidth: '75%' }}>
        <div
          className={`p-4 rounded-medical shadow-sm position-relative ${
            message.isUser
              ? 'text-white'
              : 'bg-white border'
          }`}
          style={{
            background: message.isUser
              ? 'var(--medical-gradient-primary)'
              : 'white',
            wordWrap: 'break-word',
            whiteSpace: 'pre-line',
            border: message.isUser ? 'none' : '2px solid var(--medical-primary-100)',
            fontSize: '0.95rem',
            lineHeight: '1.6'
          }}
        >
          {/* Message indicator for bot */}
          {!message.isUser && (
            <div className="position-absolute top-0 end-0 m-2">
              <FontAwesomeIcon
                icon={faCheckCircle}
                className="text-success"
                style={{ fontSize: '0.8rem' }}
              />
            </div>
          )}

          {message.text}

          {/* Message tail */}
          <div
            className="position-absolute"
            style={{
              bottom: '15px',
              [message.isUser ? 'right' : 'left']: '-8px',
              width: '0',
              height: '0',
              borderTop: '8px solid transparent',
              borderBottom: '8px solid transparent',
              [message.isUser ? 'borderLeft' : 'borderRight']: message.isUser
                ? '8px solid var(--medical-primary-500)'
                : '8px solid white'
            }}
          />
        </div>

        <div
          className={`mt-2 small d-flex align-items-center ${
            message.isUser ? 'justify-content-end' : 'justify-content-start'
          }`}
          style={{
            color: '#6c757d',
            fontSize: '0.8rem'
          }}
        >
          {!message.isUser && (
            <span className="me-2 text-success">
              <FontAwesomeIcon icon={faStethoscope} className="me-1" />
              مساعد طبي
            </span>
          )}
          <span>{formatTime(message.timestamp)}</span>
        </div>
      </div>
    </div>
  );
};

export default ChatMessage;
