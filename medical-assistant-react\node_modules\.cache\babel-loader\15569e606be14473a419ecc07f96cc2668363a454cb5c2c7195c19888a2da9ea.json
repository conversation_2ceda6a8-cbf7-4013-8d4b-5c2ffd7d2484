{"ast": null, "code": "/**\n * Helper class to handle QR Code symbol modules\n *\n * @param {Number} size Symbol size\n */\nfunction BitMatrix(size) {\n  if (!size || size < 1) {\n    throw new Error('BitMatrix size must be defined and greater than 0');\n  }\n  this.size = size;\n  this.data = new Uint8Array(size * size);\n  this.reservedBit = new Uint8Array(size * size);\n}\n\n/**\n * Set bit value at specified location\n * If reserved flag is set, this bit will be ignored during masking process\n *\n * @param {Number}  row\n * @param {Number}  col\n * @param {Boolean} value\n * @param {Boolean} reserved\n */\nBitMatrix.prototype.set = function (row, col, value, reserved) {\n  const index = row * this.size + col;\n  this.data[index] = value;\n  if (reserved) this.reservedBit[index] = true;\n};\n\n/**\n * Returns bit value at specified location\n *\n * @param  {Number}  row\n * @param  {Number}  col\n * @return {Boolean}\n */\nBitMatrix.prototype.get = function (row, col) {\n  return this.data[row * this.size + col];\n};\n\n/**\n * Applies xor operator at specified location\n * (used during masking process)\n *\n * @param {Number}  row\n * @param {Number}  col\n * @param {Boolean} value\n */\nBitMatrix.prototype.xor = function (row, col, value) {\n  this.data[row * this.size + col] ^= value;\n};\n\n/**\n * Check if bit at specified location is reserved\n *\n * @param {Number}   row\n * @param {Number}   col\n * @return {Boolean}\n */\nBitMatrix.prototype.isReserved = function (row, col) {\n  return this.reservedBit[row * this.size + col];\n};\nmodule.exports = BitMatrix;", "map": {"version": 3, "names": ["BitMatrix", "size", "Error", "data", "Uint8Array", "reservedBit", "prototype", "set", "row", "col", "value", "reserved", "index", "get", "xor", "isReserved", "module", "exports"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/test 3/medical-assistant-react/node_modules/qrcode/lib/core/bit-matrix.js"], "sourcesContent": ["/**\n * Helper class to handle QR Code symbol modules\n *\n * @param {Number} size Symbol size\n */\nfunction BitMatrix (size) {\n  if (!size || size < 1) {\n    throw new Error('BitMatrix size must be defined and greater than 0')\n  }\n\n  this.size = size\n  this.data = new Uint8Array(size * size)\n  this.reservedBit = new Uint8Array(size * size)\n}\n\n/**\n * Set bit value at specified location\n * If reserved flag is set, this bit will be ignored during masking process\n *\n * @param {Number}  row\n * @param {Number}  col\n * @param {Boolean} value\n * @param {Boolean} reserved\n */\nBitMatrix.prototype.set = function (row, col, value, reserved) {\n  const index = row * this.size + col\n  this.data[index] = value\n  if (reserved) this.reservedBit[index] = true\n}\n\n/**\n * Returns bit value at specified location\n *\n * @param  {Number}  row\n * @param  {Number}  col\n * @return {Boolean}\n */\nBitMatrix.prototype.get = function (row, col) {\n  return this.data[row * this.size + col]\n}\n\n/**\n * Applies xor operator at specified location\n * (used during masking process)\n *\n * @param {Number}  row\n * @param {Number}  col\n * @param {Boolean} value\n */\nBitMatrix.prototype.xor = function (row, col, value) {\n  this.data[row * this.size + col] ^= value\n}\n\n/**\n * Check if bit at specified location is reserved\n *\n * @param {Number}   row\n * @param {Number}   col\n * @return {Boolean}\n */\nBitMatrix.prototype.isReserved = function (row, col) {\n  return this.reservedBit[row * this.size + col]\n}\n\nmodule.exports = BitMatrix\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA,SAASA,SAASA,CAAEC,IAAI,EAAE;EACxB,IAAI,CAACA,IAAI,IAAIA,IAAI,GAAG,CAAC,EAAE;IACrB,MAAM,IAAIC,KAAK,CAAC,mDAAmD,CAAC;EACtE;EAEA,IAAI,CAACD,IAAI,GAAGA,IAAI;EAChB,IAAI,CAACE,IAAI,GAAG,IAAIC,UAAU,CAACH,IAAI,GAAGA,IAAI,CAAC;EACvC,IAAI,CAACI,WAAW,GAAG,IAAID,UAAU,CAACH,IAAI,GAAGA,IAAI,CAAC;AAChD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAD,SAAS,CAACM,SAAS,CAACC,GAAG,GAAG,UAAUC,GAAG,EAAEC,GAAG,EAAEC,KAAK,EAAEC,QAAQ,EAAE;EAC7D,MAAMC,KAAK,GAAGJ,GAAG,GAAG,IAAI,CAACP,IAAI,GAAGQ,GAAG;EACnC,IAAI,CAACN,IAAI,CAACS,KAAK,CAAC,GAAGF,KAAK;EACxB,IAAIC,QAAQ,EAAE,IAAI,CAACN,WAAW,CAACO,KAAK,CAAC,GAAG,IAAI;AAC9C,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACAZ,SAAS,CAACM,SAAS,CAACO,GAAG,GAAG,UAAUL,GAAG,EAAEC,GAAG,EAAE;EAC5C,OAAO,IAAI,CAACN,IAAI,CAACK,GAAG,GAAG,IAAI,CAACP,IAAI,GAAGQ,GAAG,CAAC;AACzC,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAT,SAAS,CAACM,SAAS,CAACQ,GAAG,GAAG,UAAUN,GAAG,EAAEC,GAAG,EAAEC,KAAK,EAAE;EACnD,IAAI,CAACP,IAAI,CAACK,GAAG,GAAG,IAAI,CAACP,IAAI,GAAGQ,GAAG,CAAC,IAAIC,KAAK;AAC3C,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACAV,SAAS,CAACM,SAAS,CAACS,UAAU,GAAG,UAAUP,GAAG,EAAEC,GAAG,EAAE;EACnD,OAAO,IAAI,CAACJ,WAAW,CAACG,GAAG,GAAG,IAAI,CAACP,IAAI,GAAGQ,GAAG,CAAC;AAChD,CAAC;AAEDO,MAAM,CAACC,OAAO,GAAGjB,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}